generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}


model FormConfig {
  id           Int      @id @default(autoincrement())
  formId       String   @unique @map("form_id") @db.VarChar(50)
  formName     String   @map("form_name") @db.VarChar(200)
  sampleJson   Json?    @map("sample_json")
  fieldMapping <PERSON>son     @map("field_mapping")
  fieldCount   Int      @default(0) @map("field_count")
  tableName    String?  @map("table_name") @db.VarChar(100)
  tableCreated <PERSON><PERSON><PERSON>  @default(false) @map("table_created")
  webhookUrl   String?  @map("webhook_url") @db.VarChar(500)
  isActive     Boolean  @default(true) @map("is_active")
  isDeleted    Boolean  @default(false) @map("is_deleted")
  createdById  Int?     @map("created_by")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  // 新增字段支持多种数据格式
  dataFormat   String   @default("jinshuju_standard") @map("data_format") @db.VarChar(50)  // 数据格式类型：jinshuju_standard, jinshuju_automation, custom
  dataPathConfig Json?  @map("data_path_config")     // 数据路径配置，用于从不同JSON结构中提取数据
  createdBy    user?    @relation("CreatedBy", fields: [createdById], references: [id])

  @@index([formId])
  @@index([formName])
  @@index([isActive])
  @@index([isDeleted])
  @@index([isActive, isDeleted])
  @@index([createdById])
  @@index([createdAt])
  @@index([isActive, createdAt])
  @@index([createdById, isActive])
  @@map("form_configs")
}

model SystemLog {
  id         BigInt   @id @default(autoincrement())
  userId     Int?     @map("user_id")
  action     String   @db.VarChar(50)
  resource   String   @db.VarChar(50)
  resourceId String?  @map("resource_id") @db.VarChar(50)
  details    Json?
  ipAddress  String   @map("ip_address") @db.VarChar(45)
  userAgent  String   @map("user_agent") @db.Text
  createdAt  DateTime @default(now()) @map("created_at")
  user       user?    @relation(fields: [userId], references: [id])

  @@index([action])
  @@index([resource])
  @@index([userId])
  @@index([createdAt])
  @@index([action, createdAt])
  @@index([userId, action])
  @@index([resource, action])
  @@map("system_logs")
}

model calculation_task {
  id                 Int       @id @default(autoincrement())
  user_id            Int
  task_name          String    @db.VarChar(100)
  status             String?   @db.VarChar(20)
  created_at         DateTime? @db.DateTime(0)
  completed_at       DateTime? @db.DateTime(0)
  result_file        String?   @db.VarChar(255)
  error_message      String?   @db.Text
  data_folder        String?   @db.VarChar(255)
  duplicate_strategy String?   @db.VarChar(20)
  user               user      @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "calculation_task_ibfk_1")

  @@index([user_id], map: "user_id")
}

model doctor {
  id           Int          @id @default(autoincrement())
  name         String       @unique(map: "name") @db.VarChar(50)
  group_id     Int
  title        String?      @db.VarChar(50)
  department   String?      @db.VarChar(50)
  phone        String?      @db.VarChar(20)
  email        String?      @db.VarChar(120)
  is_active    Boolean?
  created_at   DateTime?    @db.DateTime(0)
  updated_at   DateTime?    @db.DateTime(0)
  doctor_group doctor_group @relation(fields: [group_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "doctor_ibfk_1")

  @@index([group_id], map: "group_id")
}

model doctor_group {
  id          Int       @id @default(autoincrement())
  group_name  String    @unique(map: "group_name") @db.VarChar(50)
  description String?   @db.VarChar(200)
  created_by  Int?
  created_at  DateTime? @db.DateTime(0)
  updated_at  DateTime? @db.DateTime(0)
  doctor      doctor[]
  user        user?     @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "doctor_group_ibfk_1")

  @@index([created_by], map: "created_by")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model form_data_CykkAU {
  id                                     BigInt   @id @default(autoincrement())
  serial_number                          Int
  created_at                             DateTime @default(now()) @db.Timestamp(0)
  updated_at                             DateTime @default(now()) @db.Timestamp(0)
  source_ip                              String?  @db.VarChar(45)
  creator_name                           String?  @db.VarChar(100)
  raw_data                               Json?
  field_1                                String?  @db.VarChar(500)
  field_2                                String?  @db.VarChar(500)
  field_3                                String?  @db.VarChar(500)
  field_4                                String?  @db.VarChar(500)
  field_5                                String?  @db.VarChar(500)
  field_6                                Decimal? @db.Decimal(10, 2)
  x_field_1                              String?  @db.Text
  referred_from                          String?  @db.VarChar(500)
  referred_from_associated_serial_number Decimal? @db.Decimal(10, 2)
  referral_users_count                   Decimal? @db.Decimal(10, 2)
  referral_link                          String?  @db.VarChar(500)
  referral_poster_url                    String?  @db.VarChar(500)

  @@index([created_at], map: "idx_created_at")
  @@index([serial_number], map: "idx_serial_number")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model form_data_ZFs2eo {
  id            BigInt   @id @default(autoincrement())
  serial_number Int
  created_at    DateTime @default(now()) @db.Timestamp(0)
  updated_at    DateTime @default(now()) @db.Timestamp(0)
  source_ip     String?  @db.VarChar(45)
  creator_name  String?  @db.VarChar(100)
  raw_data      Json?
  field_1       String?  @db.VarChar(500)
  field_2       String?  @db.VarChar(500)
  field_6       Decimal? @db.Decimal(10, 2)
  field_7       String?  @db.VarChar(500)
  field_3       String?  @db.VarChar(500)
  field_4       Json?
  x_field_1     String?  @db.Text

  @@index([created_at], map: "idx_created_at")
  @@index([serial_number], map: "idx_serial_number")
}

model user {
  id               Int                @id @default(autoincrement())
  username         String             @unique(map: "username") @db.VarChar(80)
  email            String             @unique(map: "email") @db.VarChar(120)
  password_hash    String             @db.VarChar(255)
  role             String?            @db.VarChar(20)
  doctor_name      String?            @db.VarChar(50)
  created_at       DateTime?          @db.DateTime(0)
  is_active        Boolean?
  calculation_task calculation_task[]
  doctor_group     doctor_group[]
  createdFormConfigs FormConfig[] @relation("CreatedBy")
  systemLogs         SystemLog[]
  resolvedValidationFailures WebhookValidationFailure[] @relation("ResolvedBy")
  notifications    Notification[]
}

// Webhook验证失败记录表
model WebhookValidationFailure {
  id             BigInt   @id @default(autoincrement())
  formId         String   @map("form_id") @db.VarChar(50)
  originalData   Json     @map("original_data")       // 原始JSON数据
  validationErrors Json   @map("validation_errors")   // 验证错误详情
  errorType      String   @map("error_type") @db.VarChar(50)      // 错误类型：SCHEMA_VALIDATION, FIELD_MAPPING, DATA_TYPE, etc.
  errorMessage   String   @map("error_message") @db.Text          // 错误消息
  expectedFormat Json?    @map("expected_format")     // 期望的数据格式
  suggestions    Json?    @map("suggestions")         // 修复建议
  status         String   @default("pending") @db.VarChar(20)     // 处理状态：pending, reviewing, resolved, ignored
  resolvedBy     Int?     @map("resolved_by")         // 处理人
  resolvedAt     DateTime? @map("resolved_at")        // 处理时间
  notes          String?  @map("notes") @db.Text      // 处理备注
  ipAddress      String   @map("ip_address") @db.VarChar(45)
  userAgent      String   @map("user_agent") @db.Text
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // 关联关系
  resolver user? @relation("ResolvedBy", fields: [resolvedBy], references: [id])

  @@map("webhook_validation_failures")
  @@index([formId])
  @@index([errorType])
  @@index([status])
  @@index([createdAt])
  @@index([resolvedBy])
  @@index([formId, status])
  @@index([status, createdAt])
  @@index([errorType, status])
}

// 通知系统模型
model Notification {
  id          String   @id @default(cuid())
  userId      Int      @map("user_id")
  title       String   @db.VarChar(200)
  message     String   @db.Text
  type        NotificationType @default(INFO)
  read        Boolean  @default(false)
  actionUrl   String?  @map("action_url") @db.VarChar(500)
  sourceType  String?  @map("source_type") @db.VarChar(50)  // 'webhook_failure', 'form_submission', 'system'
  sourceId    String?  @map("source_id") @db.VarChar(100)   // 关联的源记录ID
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  user        user     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId, read])
  @@index([createdAt])
  @@index([userId, createdAt])
  @@index([sourceType, sourceId])
  @@map("notifications")
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
}
