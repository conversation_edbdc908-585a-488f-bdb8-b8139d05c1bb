<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 200px;
            font-family: monospace;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        .error {
            color: #ff4d4f;
            background: #fff2f0;
            border: 1px solid #ffccc7;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            color: #52c41a;
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .info {
            color: #1890ff;
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .example {
            background: #f9f9f9;
            border-left: 4px solid #1890ff;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>表单配置测试页面</h1>
        <p>这个页面用于测试不同数据格式的JSON验证功能</p>
    </div>

    <div class="container">
        <h2>步骤1: 选择数据格式</h2>
        <div class="form-group">
            <label for="dataFormat">数据格式:</label>
            <select id="dataFormat">
                <option value="jinshuju_standard">金数据标准格式</option>
                <option value="jinshuju_automation">金数据自动化格式</option>
                <option value="custom">自定义格式</option>
            </select>
        </div>
        <button onclick="updateExample()">更新示例</button>
    </div>

    <div class="container">
        <h2>步骤2: 输入JSON数据</h2>
        <div class="form-group">
            <label for="jsonInput">JSON数据:</label>
            <textarea id="jsonInput" placeholder="请输入JSON数据..."></textarea>
        </div>
        <button onclick="validateJson()">验证JSON</button>
        <button onclick="useExample()">使用示例数据</button>
        <div id="result"></div>
    </div>

    <div class="container">
        <h2>示例数据</h2>
        <div id="examples">
            <h3>当前格式示例:</h3>
            <div id="currentExample" class="example"></div>
        </div>
    </div>

    <script>
        // 示例数据
        const exampleJsonTemplates = {
            jinshuju_standard: {
                form: 'ZFs2eo',
                form_name: '预约免费肺功能检查',
                entry: {
                    serial_number: 123,
                    field_1: '张三',
                    field_2: '选项1',
                    field_3: '13812345678',
                    field_4: ['选项1', '选项2'],
                    creator_name: '小王',
                    created_at: '2025-06-29T05:16:12.175Z',
                    updated_at: '2025-06-29T05:16:12.175Z',
                    info_remote_ip: '127.0.0.1'
                }
            },
            jinshuju_automation: {
                form: 'usk6CY',
                form_name: '120餐券领取',
                entry: {
                    token: 'TzbIm8FV',
                    field_3: '',
                    field_4: '2025-07-10',
                    field_5: '高安市人民医院',
                    field_6: '李卫平',
                    field_7: '13707003778',
                    field_8: '邹春香',
                    field_11: '爱国路院区食堂',
                    field_12: '120司机',
                    gen_code: '031576671523',
                    referral_link: 'http://jinshuju.net/f/usk6CY?referred_from=TzbIm8FV',
                    x_field_weixin_openid: 'orNMswF4m_KgjYAyPLr4E5CbsW2Q',
                    x_field_weixin_unionid: 'oRZSYuIDHs9G1s6kG5ca_TUErN8k',
                    x_field_weixin_nickname: '李卫平'
                }
            },
            custom: {
                form: 'custom_form',
                form_name: '自定义格式示例',
                entry: {
                    id: 'unique_id_123',
                    field_1: '示例数据',
                    field_2: '自定义字段',
                    custom_field: '特殊字段',
                    metadata: {
                        source: 'custom_system',
                        timestamp: '2025-07-10T10:00:00Z'
                    }
                }
            }
        };

        // 验证函数
        function validateJsonStructure(json, dataFormat) {
            if (!json || typeof json !== 'object') {
                return 'JSON必须是一个对象';
            }

            if (!json.form || typeof json.form !== 'string') {
                return '缺少必要字段: form (表单ID)';
            }

            if (!json.entry || typeof json.entry !== 'object') {
                return '缺少必要字段: entry (表单数据)';
            }

            // 根据数据格式进行不同的验证
            switch (dataFormat) {
                case 'jinshuju_standard':
                    if (!json.form_name || typeof json.form_name !== 'string') {
                        return '缺少必要字段: form_name (表单名称)';
                    }
                    if (!json.entry.serial_number || typeof json.entry.serial_number !== 'number') {
                        return '缺少必要字段: entry.serial_number (序列号)';
                    }
                    break;
                
                case 'jinshuju_automation':
                    if (!json.form_name || typeof json.form_name !== 'string') {
                        return '缺少必要字段: form_name (表单名称)';
                    }
                    if (!json.entry.token || typeof json.entry.token !== 'string') {
                        return '缺少必要字段: entry.token (令牌标识)';
                    }
                    break;
                
                case 'custom':
                    // 自定义格式的验证比较宽松
                    if (Object.keys(json.entry).length === 0) {
                        return 'entry对象不能为空';
                    }
                    break;
                
                default:
                    return '不支持的数据格式';
            }

            return null;
        }

        function updateExample() {
            const dataFormat = document.getElementById('dataFormat').value;
            const example = exampleJsonTemplates[dataFormat];
            document.getElementById('currentExample').textContent = JSON.stringify(example, null, 2);
        }

        function useExample() {
            const dataFormat = document.getElementById('dataFormat').value;
            const example = exampleJsonTemplates[dataFormat];
            document.getElementById('jsonInput').value = JSON.stringify(example, null, 2);
        }

        function validateJson() {
            const dataFormat = document.getElementById('dataFormat').value;
            const jsonText = document.getElementById('jsonInput').value;
            const resultDiv = document.getElementById('result');

            try {
                const parsed = JSON.parse(jsonText);
                const error = validateJsonStructure(parsed, dataFormat);
                
                if (error) {
                    resultDiv.innerHTML = `<div class="error"><strong>验证失败:</strong> ${error}</div>`;
                } else {
                    const fields = Object.keys(parsed.entry).filter(key => 
                        key.startsWith('field_') || key.startsWith('x_field_') || 
                        key === 'token' || key === 'gen_code' || key === 'referral_link'
                    );
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>验证成功!</strong><br>
                            表单ID: ${parsed.form}<br>
                            表单名称: ${parsed.form_name || '未指定'}<br>
                            数据格式: ${dataFormat}<br>
                            检测到字段: ${fields.length} 个<br>
                            字段列表: ${fields.join(', ')}
                        </div>
                    `;
                }
            } catch (e) {
                resultDiv.innerHTML = `<div class="error"><strong>JSON解析错误:</strong> ${e.message}</div>`;
            }
        }

        // 初始化
        updateExample();
    </script>
</body>
</html>