# 肺功能数据管理平台

<div align="center">

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Node](https://img.shields.io/badge/node-%3E%3D18.0.0-green.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

一个现代化的全栈数据管理平台，专为处理金数据表单提交、用户管理和数据分析而设计。

</div>

## 📖 项目文档

- **[🚀 快速上手 (Quick Start)](#-快速上手)**
- **[📄 核心技术文档 (Documentation)](docs/DOCUMENTATION.md)**
- **[⚙️ 部署与运维 (Deployment)](docs/DEPLOYMENT.md)**
- **[🤝 贡献指南 (Contributing)](CONTRIBUTING.md)**
- **[📅 更新日志 (Changelog)](CHANGELOG.md)**

---

## ✨ 项目概述

肺功能数据管理平台是一个用于接收和处理来自“金数据”等平台的Webhook数据，并提供完整的数据管理、用户管理和数据导出功能的Web应用。

### 核心价值

- **自动化**: 自动完成数据收集、处理和入库。
- **可视化**: 提供直观、现代化的数据管理和配置界面。
- **灵活性**: 通过动态表单配置机制，适应不同类型的数据结构。
- **安全性**: 完整的用户认证、权限控制和操作审计。
- **智能化**: 内置数据验证、错误处理和通知系统

## 🎯 核心功能

### 📊 数据管理
- **动态表单配置**: 自动解析JSON样本，生成对应数据表结构
- **多格式支持**: 支持金数据标准格式、自动化格式和自定义格式
- **实时数据接收**: 通过Webhook自动接收和处理表单提交数据
- **数据验证**: 智能数据验证和错误处理机制
- **批量操作**: 支持批量导入、导出、删除和更新

### 👥 用户管理
- **角色权限**: 管理员、普通用户等多级权限控制
- **用户操作**: 用户增删改查、密码重置、账户状态管理
- **个人中心**: 用户可自主修改个人资料和密码
- **操作审计**: 完整的用户操作日志记录

### 📈 数据分析与导出
- **可视化仪表板**: 实时统计数据展示
- **高级搜索**: 多字段、多条件组合搜索
- **数据导出**: 支持Excel、CSV格式导出
- **自定义报表**: 根据筛选条件生成定制化报表

### 🔔 通知系统
- **实时通知**: 数据异常、系统状态等实时提醒
- **通知管理**: 通知历史查看和状态管理
- **多类型通知**: 信息、成功、警告、错误等分类通知

### 🛡️ 安全特性
- **数据加密**: 密码bcrypt加密存储
- **会话管理**: 基于NextAuth.js的安全会话管理
- **输入验证**: 前后端双重数据验证
- **访问控制**: 基于角色的访问控制(RBAC)
- **操作日志**: 详细的系统操作审计日志

---

## 🏗️ 系统架构

### 技术栈
- **前端**: Next.js 14 (App Router) + React 18 + TypeScript
- **UI框架**: Ant Design 5.x + Tailwind CSS 3.x
- **状态管理**: Zustand 4.x + React Hook Form
- **数据库**: MySQL 8.0 + Prisma ORM 5.x
- **认证**: NextAuth.js 4.x
- **测试**: Jest + Playwright + Testing Library
- **部署**: PM2 / Docker + 腾讯云

### 项目结构
```
src/
├── app/                    # Next.js App Router页面
│   ├── (auth)/            # 认证相关页面
│   ├── (dashboard)/       # 仪表板页面
│   └── api/               # API路由
├── components/            # React组件
│   ├── forms/            # 表单组件
│   ├── layout/           # 布局组件
│   ├── notifications/    # 通知组件
│   └── ui/               # 基础UI组件
├── lib/                  # 工具库和配置
├── hooks/                # 自定义React Hooks
└── types/                # TypeScript类型定义
```

## 📱 功能模块详解

### 🎛️ 仪表板 (Dashboard)
- **数据概览**: 表单总数、数据记录、用户数量、今日新增等关键指标
- **活动监控**: 最近数据活动、系统状态实时监控
- **快速操作**: 一键访问常用功能，提升操作效率

### 📋 表单配置管理 (Forms)
- **智能配置**: 粘贴JSON样本自动生成表单配置
- **字段映射**: 可视化字段映射和类型定义
- **动态表创建**: 根据配置自动创建对应数据库表
- **多格式支持**: 
  - 金数据标准格式 (`jinshuju_standard`)
  - 金数据自动化格式 (`jinshuju_automation`) 
  - 自定义格式 (`custom`)

### 📊 数据管理 (Data)
- **数据查看**: 分页展示、实时搜索、多条件筛选
- **数据操作**: 增删改查、批量操作、数据验证
- **数据导出**: Excel/CSV格式导出，支持自定义字段选择
- **批量导入**: 支持Excel/CSV批量数据导入

### ⚙️ 系统设置 (Settings)
- **用户管理**: 用户增删改查、角色分配、密码重置
- **个人资料**: 头像上传、基本信息修改、密码变更
- **系统日志**: 操作审计、错误日志、性能监控
- **验证失败**: Webhook验证失败记录和处理

## 🔗 API接口

### Webhook接口
```bash
POST /api/webhook/{formId}
Content-Type: application/json

# 金数据标准格式示例
{
  "form": { "id": "abc123", "name": "表单名称" },
  "entry": {
    "serial_number": 12345,
    "field_1": "用户输入值",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 数据管理接口
```bash
# 获取表单数据
GET /api/data/{formId}?page=1&pageSize=20&search=关键词

# 批量删除数据
DELETE /api/data/{formId}/batch
Content-Type: application/json
{ "ids": [1, 2, 3] }

# 导出数据
GET /api/data/{formId}/export?format=xlsx&fields=field_1,field_2
```

---

## 🚀 快速上手

### 1. 环境要求

- Node.js 18.0+
- MySQL 8.0+
- npm 或 yarn

### 2. 本地开发步骤

```bash
# 1. 克隆项目
git clone https://github.com/peckbyte/free_lung_function_project_admin.git
cd free_lung_function_project_admin

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.local
# (根据.env.example的提示，编辑.env.local文件，填入数据库连接和密钥)

# 4. 初始化数据库
npx prisma db push

# 5. 启动开发服务器
npm run dev
```

应用将在 `http://localhost:3000` 上运行。默认管理员账户: `admin` / `admin123`。

### 3. 常用开发命令

```bash
# 开发相关
npm run dev              # 启动开发服务器 (端口3011)
npm run build           # 构建生产版本
npm run start           # 启动生产服务器

# 代码质量
npm run lint            # 代码检查
npm run lint:fix        # 自动修复代码问题
npm run type-check      # TypeScript类型检查
npm run format          # 代码格式化

# 测试相关
npm test                # 运行单元测试
npm run test:watch      # 监听模式运行测试

# 数据库相关
npm run db:generate     # 生成Prisma客户端
npm run db:push         # 推送数据库模式
npm run db:migrate      # 运行数据库迁移
npm run db:studio       # 打开Prisma Studio
npm run db:seed         # 运行数据库种子

# 通知系统
npm run notifications:seed  # 生成测试通知数据
npm run notifications:test  # 通知系统性能测试

# MCP (Model Context Protocol)
npm run mcp:start       # 启动MCP服务
npm run mcp:dev         # 开发模式MCP服务
npm run mcp:test        # 测试MCP配置
```

## 🎨 使用示例

### 配置新表单
1. 登录管理后台
2. 进入"表单管理" → "表单配置"
3. 点击"新建配置"
4. 粘贴金数据提供的JSON样本
5. 系统自动解析字段并生成配置
6. 确认字段映射后保存
7. 系统自动创建对应数据表

### 数据导出流程
1. 进入"数据管理" → "数据查看"
2. 选择要导出的表单
3. 设置筛选条件（可选）
4. 点击"导出数据"
5. 选择导出格式（Excel/CSV）
6. 选择要导出的字段
7. 下载生成的文件

## 🔧 配置说明

### 环境变量配置
```bash
# 数据库配置
DATABASE_URL="mysql://username:password@localhost:3306/database_name"

# NextAuth配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# 应用配置
NODE_ENV="development"
PORT=3011
```

### 数据格式支持
系统支持多种数据格式，通过 `dataPathUtils.ts` 自动检测：

- **金数据标准格式**: 包含 `serial_number` 字段
- **金数据自动化格式**: 包含 `token` 字段  
- **自定义格式**: 其他JSON结构

## 🚨 故障排除

### 常见问题

**1. 数据库连接失败**
```bash
# 检查数据库服务状态
mysql -u username -p

# 重新生成Prisma客户端
npm run db:generate
```

**2. Webhook接收失败**
- 检查表单ID是否正确
- 验证JSON格式是否符合预期
- 查看系统日志获取详细错误信息

**3. 数据验证错误**
- 检查 `debug-validation.js` 中的验证逻辑
- 确认字段映射配置正确
- 查看验证失败记录页面

### 性能优化建议
- 定期清理系统日志和通知记录
- 为常用查询字段添加数据库索引
- 使用分页查询避免大量数据加载
- 启用数据缓存机制

---

## 🤝 如何贡献

我们欢迎所有形式的贡献！请查看 **[贡献指南](CONTRIBUTING.md)** 了解如何参与项目开发。

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。