/**
 * 离线数据存储和同步工具
 * 提供IndexedDB数据存储、离线请求队列和数据同步功能
 */

// 数据库名称和版本
const DB_NAME = 'lungFunctionOfflineDB';
const DB_VERSION = 1;

// 存储对象名称
const STORES = {
  OFFLINE_REQUESTS: 'offlineRequests',
  CACHED_DATA: 'cachedData',
  USER_DATA: 'userData',
  SETTINGS: 'settings'
};

// 打开数据库连接
export async function openDatabase(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      // 创建离线请求存储
      if (!db.objectStoreNames.contains(STORES.OFFLINE_REQUESTS)) {
        const offlineRequestsStore = db.createObjectStore(STORES.OFFLINE_REQUESTS, { 
          keyPath: 'id', 
          autoIncrement: true 
        });
        offlineRequestsStore.createIndex('timestamp', 'timestamp', { unique: false });
        offlineRequestsStore.createIndex('url', 'url', { unique: false });
      }
      
      // 创建缓存数据存储
      if (!db.objectStoreNames.contains(STORES.CACHED_DATA)) {
        const cachedDataStore = db.createObjectStore(STORES.CACHED_DATA, { 
          keyPath: 'key' 
        });
        cachedDataStore.createIndex('timestamp', 'timestamp', { unique: false });
        cachedDataStore.createIndex('expiry', 'expiry', { unique: false });
      }
      
      // 创建用户数据存储
      if (!db.objectStoreNames.contains(STORES.USER_DATA)) {
        const userDataStore = db.createObjectStore(STORES.USER_DATA, { 
          keyPath: 'id' 
        });
        userDataStore.createIndex('userId', 'userId', { unique: false });
        userDataStore.createIndex('timestamp', 'timestamp', { unique: false });
      }
      
      // 创建设置存储
      if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
        db.createObjectStore(STORES.SETTINGS, { 
          keyPath: 'key' 
        });
      }
    };

    request.onsuccess = (event) => {
      resolve((event.target as IDBOpenDBRequest).result);
    };

    request.onerror = (event) => {
      reject((event.target as IDBOpenDBRequest).error);
    };
  });
}

// 离线请求接口
export interface OfflineRequest {
  id?: number;
  url: string;
  method: string;
  headers: Record<string, string>;
  body?: string;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

// 缓存数据接口
export interface CachedData {
  key: string;
  data: any;
  timestamp: number;
  expiry?: number;
}

// 用户数据接口
export interface UserData {
  id: string;
  userId: string;
  data: any;
  timestamp: number;
  synced: boolean;
}

// 设置数据接口
export interface SettingsData {
  key: string;
  value: any;
}

/**
 * 离线请求管理器
 */
export class OfflineRequestManager {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    this.db = await openDatabase();
  }

  // 添加离线请求到队列
  async addRequest(request: Omit<OfflineRequest, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.OFFLINE_REQUESTS], 'readwrite');
    const store = transaction.objectStore(STORES.OFFLINE_REQUESTS);
    
    const offlineRequest: Omit<OfflineRequest, 'id'> = {
      ...request,
      timestamp: Date.now(),
      retryCount: 0
    };
    
    return new Promise((resolve, reject) => {
      const addRequest = store.add(offlineRequest);
      addRequest.onsuccess = () => resolve();
      addRequest.onerror = () => reject(addRequest.error);
    });
  }

  // 获取所有待处理的请求
  async getPendingRequests(): Promise<OfflineRequest[]> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.OFFLINE_REQUESTS], 'readonly');
    const store = transaction.objectStore(STORES.OFFLINE_REQUESTS);
    
    return new Promise((resolve, reject) => {
      const getAllRequest = store.getAll();
      getAllRequest.onsuccess = () => resolve(getAllRequest.result);
      getAllRequest.onerror = () => reject(getAllRequest.error);
    });
  }

  // 删除已处理的请求
  async removeRequest(id: number): Promise<void> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.OFFLINE_REQUESTS], 'readwrite');
    const store = transaction.objectStore(STORES.OFFLINE_REQUESTS);
    
    return new Promise((resolve, reject) => {
      const deleteRequest = store.delete(id);
      deleteRequest.onsuccess = () => resolve();
      deleteRequest.onerror = () => reject(deleteRequest.error);
    });
  }

  // 更新请求重试次数
  async updateRetryCount(id: number, retryCount: number): Promise<void> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.OFFLINE_REQUESTS], 'readwrite');
    const store = transaction.objectStore(STORES.OFFLINE_REQUESTS);
    
    return new Promise((resolve, reject) => {
      const getRequest = store.get(id);
      getRequest.onsuccess = () => {
        const request = getRequest.result;
        if (request) {
          request.retryCount = retryCount;
          const updateRequest = store.put(request);
          updateRequest.onsuccess = () => resolve();
          updateRequest.onerror = () => reject(updateRequest.error);
        } else {
          resolve();
        }
      };
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // 清理过期的请求
  async cleanupExpiredRequests(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
    if (!this.db) await this.init();
    
    const cutoffTime = Date.now() - maxAge;
    const transaction = this.db!.transaction([STORES.OFFLINE_REQUESTS], 'readwrite');
    const store = transaction.objectStore(STORES.OFFLINE_REQUESTS);
    const index = store.index('timestamp');
    
    return new Promise((resolve, reject) => {
      const range = IDBKeyRange.upperBound(cutoffTime);
      const deleteRequest = index.openCursor(range);
      
      deleteRequest.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve();
        }
      };
      
      deleteRequest.onerror = () => reject(deleteRequest.error);
    });
  }
}

/**
 * 缓存数据管理器
 */
export class CacheManager {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    this.db = await openDatabase();
  }

  // 设置缓存数据
  async set(key: string, data: any, ttl?: number): Promise<void> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.CACHED_DATA], 'readwrite');
    const store = transaction.objectStore(STORES.CACHED_DATA);
    
    const cachedData: CachedData = {
      key,
      data,
      timestamp: Date.now(),
      expiry: ttl ? Date.now() + ttl : undefined
    };
    
    return new Promise((resolve, reject) => {
      const putRequest = store.put(cachedData);
      putRequest.onsuccess = () => resolve();
      putRequest.onerror = () => reject(putRequest.error);
    });
  }

  // 获取缓存数据
  async get(key: string): Promise<any | null> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.CACHED_DATA], 'readonly');
    const store = transaction.objectStore(STORES.CACHED_DATA);
    
    return new Promise((resolve, reject) => {
      const getRequest = store.get(key);
      getRequest.onsuccess = () => {
        const result = getRequest.result;
        if (!result) {
          resolve(null);
          return;
        }
        
        // 检查是否过期
        if (result.expiry && Date.now() > result.expiry) {
          // 异步删除过期数据
          this.delete(key);
          resolve(null);
          return;
        }
        
        resolve(result.data);
      };
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // 删除缓存数据
  async delete(key: string): Promise<void> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.CACHED_DATA], 'readwrite');
    const store = transaction.objectStore(STORES.CACHED_DATA);
    
    return new Promise((resolve, reject) => {
      const deleteRequest = store.delete(key);
      deleteRequest.onsuccess = () => resolve();
      deleteRequest.onerror = () => reject(deleteRequest.error);
    });
  }

  // 清理过期缓存
  async cleanupExpired(): Promise<void> {
    if (!this.db) await this.init();
    
    const now = Date.now();
    const transaction = this.db!.transaction([STORES.CACHED_DATA], 'readwrite');
    const store = transaction.objectStore(STORES.CACHED_DATA);
    const index = store.index('expiry');
    
    return new Promise((resolve, reject) => {
      const range = IDBKeyRange.upperBound(now);
      const deleteRequest = index.openCursor(range);
      
      deleteRequest.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          if (cursor.value.expiry && cursor.value.expiry <= now) {
            cursor.delete();
          }
          cursor.continue();
        } else {
          resolve();
        }
      };
      
      deleteRequest.onerror = () => reject(deleteRequest.error);
    });
  }

  // 获取缓存统计信息
  async getStats(): Promise<{ count: number; size: number }> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.CACHED_DATA], 'readonly');
    const store = transaction.objectStore(STORES.CACHED_DATA);
    
    return new Promise((resolve, reject) => {
      const getAllRequest = store.getAll();
      getAllRequest.onsuccess = () => {
        const results = getAllRequest.result;
        const count = results.length;
        const size = results.reduce((total, item) => {
          return total + JSON.stringify(item.data).length;
        }, 0);
        resolve({ count, size });
      };
      getAllRequest.onerror = () => reject(getAllRequest.error);
    });
  }
}

/**
 * 用户数据管理器
 */
export class UserDataManager {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    this.db = await openDatabase();
  }

  // 保存用户数据
  async saveUserData(id: string, userId: string, data: any): Promise<void> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.USER_DATA], 'readwrite');
    const store = transaction.objectStore(STORES.USER_DATA);
    
    const userData: UserData = {
      id,
      userId,
      data,
      timestamp: Date.now(),
      synced: false
    };
    
    return new Promise((resolve, reject) => {
      const putRequest = store.put(userData);
      putRequest.onsuccess = () => resolve();
      putRequest.onerror = () => reject(putRequest.error);
    });
  }

  // 获取用户数据
  async getUserData(id: string): Promise<UserData | null> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.USER_DATA], 'readonly');
    const store = transaction.objectStore(STORES.USER_DATA);
    
    return new Promise((resolve, reject) => {
      const getRequest = store.get(id);
      getRequest.onsuccess = () => resolve(getRequest.result || null);
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // 获取未同步的数据
  async getUnsyncedData(): Promise<UserData[]> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.USER_DATA], 'readonly');
    const store = transaction.objectStore(STORES.USER_DATA);
    
    return new Promise((resolve, reject) => {
      const getAllRequest = store.getAll();
      getAllRequest.onsuccess = () => {
        const results = getAllRequest.result.filter(item => !item.synced);
        resolve(results);
      };
      getAllRequest.onerror = () => reject(getAllRequest.error);
    });
  }

  // 标记数据为已同步
  async markAsSynced(id: string): Promise<void> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.USER_DATA], 'readwrite');
    const store = transaction.objectStore(STORES.USER_DATA);
    
    return new Promise((resolve, reject) => {
      const getRequest = store.get(id);
      getRequest.onsuccess = () => {
        const userData = getRequest.result;
        if (userData) {
          userData.synced = true;
          const putRequest = store.put(userData);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(putRequest.error);
        } else {
          resolve();
        }
      };
      getRequest.onerror = () => reject(getRequest.error);
    });
  }
}

/**
 * 设置管理器
 */
export class SettingsManager {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    this.db = await openDatabase();
  }

  // 设置配置
  async set(key: string, value: any): Promise<void> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.SETTINGS], 'readwrite');
    const store = transaction.objectStore(STORES.SETTINGS);
    
    const settingsData: SettingsData = { key, value };
    
    return new Promise((resolve, reject) => {
      const putRequest = store.put(settingsData);
      putRequest.onsuccess = () => resolve();
      putRequest.onerror = () => reject(putRequest.error);
    });
  }

  // 获取配置
  async get(key: string, defaultValue?: any): Promise<any> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.SETTINGS], 'readonly');
    const store = transaction.objectStore(STORES.SETTINGS);
    
    return new Promise((resolve, reject) => {
      const getRequest = store.get(key);
      getRequest.onsuccess = () => {
        const result = getRequest.result;
        resolve(result ? result.value : defaultValue);
      };
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // 删除配置
  async delete(key: string): Promise<void> {
    if (!this.db) await this.init();
    
    const transaction = this.db!.transaction([STORES.SETTINGS], 'readwrite');
    const store = transaction.objectStore(STORES.SETTINGS);
    
    return new Promise((resolve, reject) => {
      const deleteRequest = store.delete(key);
      deleteRequest.onsuccess = () => resolve();
      deleteRequest.onerror = () => reject(deleteRequest.error);
    });
  }
}

// 创建单例实例
export const offlineRequestManager = new OfflineRequestManager();
export const cacheManager = new CacheManager();
export const userDataManager = new UserDataManager();
export const settingsManager = new SettingsManager();

/**
 * 数据同步服务
 */
export class SyncService {
  private isOnline = navigator.onLine;
  private syncInProgress = false;

  constructor() {
    // 监听网络状态变化
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.syncWhenOnline();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  // 当网络恢复时同步数据
  async syncWhenOnline(): Promise<void> {
    if (!this.isOnline || this.syncInProgress) return;
    
    this.syncInProgress = true;
    
    try {
      await this.syncOfflineRequests();
      await this.syncUserData();
      
      // 更新最后同步时间
      localStorage.setItem('lastSyncTime', Date.now().toString());
      
      console.log('[SyncService] Data sync completed successfully');
    } catch (error) {
      console.error('[SyncService] Data sync failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  // 同步离线请求
  private async syncOfflineRequests(): Promise<void> {
    const pendingRequests = await offlineRequestManager.getPendingRequests();
    
    for (const request of pendingRequests) {
      try {
        const response = await fetch(request.url, {
          method: request.method,
          headers: request.headers,
          body: request.body,
          credentials: 'include'
        });
        
        if (response.ok) {
          await offlineRequestManager.removeRequest(request.id!);
        } else if (request.retryCount < request.maxRetries) {
          await offlineRequestManager.updateRetryCount(request.id!, request.retryCount + 1);
        } else {
          // 达到最大重试次数，删除请求
          await offlineRequestManager.removeRequest(request.id!);
        }
      } catch (error) {
        if (request.retryCount < request.maxRetries) {
          await offlineRequestManager.updateRetryCount(request.id!, request.retryCount + 1);
        } else {
          await offlineRequestManager.removeRequest(request.id!);
        }
      }
    }
  }

  // 同步用户数据
  private async syncUserData(): Promise<void> {
    const unsyncedData = await userDataManager.getUnsyncedData();
    
    for (const userData of unsyncedData) {
      try {
        const response = await fetch('/api/sync/user-data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id: userData.id,
            userId: userData.userId,
            data: userData.data,
            timestamp: userData.timestamp
          }),
          credentials: 'include'
        });
        
        if (response.ok) {
          await userDataManager.markAsSynced(userData.id);
        }
      } catch (error) {
        console.error('[SyncService] Failed to sync user data:', error);
      }
    }
  }

  // 手动触发同步
  async manualSync(): Promise<void> {
    if (this.isOnline) {
      await this.syncWhenOnline();
    } else {
      throw new Error('无法同步：当前处于离线状态');
    }
  }

  // 获取同步状态
  getSyncStatus(): { isOnline: boolean; syncInProgress: boolean; lastSyncTime: number | null } {
    const lastSyncTime = localStorage.getItem('lastSyncTime');
    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      lastSyncTime: lastSyncTime ? parseInt(lastSyncTime) : null
    };
  }
}

// 创建同步服务实例
export const syncService = new SyncService();