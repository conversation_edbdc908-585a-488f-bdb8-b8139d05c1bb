/**
 * 响应式断点系统和检测工具
 * Responsive breakpoint system and detection utilities
 */

// 响应式断点定义
export const BREAKPOINTS = {
  xs: 0,      // 超小屏幕 (手机竖屏)
  sm: 576,    // 小屏幕 (手机横屏)
  md: 768,    // 中等屏幕 (平板竖屏)
  lg: 992,    // 大屏幕 (平板横屏)
  xl: 1200,   // 超大屏幕 (桌面)
  xxl: 1600   // 超超大屏幕 (大桌面)
} as const

export type BreakpointKey = keyof typeof BREAKPOINTS
export type ScreenSize = BreakpointKey

// 响应式配置接口
export interface ResponsiveConfig {
  breakpoints: Record<string, number>
  containerMaxWidths: Record<string, number>
  gridColumns: Record<string, number>
  spacing: Record<string, number>
}

// 设备信息接口
export interface DeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop'
  os: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown'
  browser: 'chrome' | 'safari' | 'firefox' | 'edge' | 'other'
  hasTouch: boolean
  orientation: 'portrait' | 'landscape'
  pixelRatio: number
}

// 响应式信息接口
export interface ResponsiveInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  screenSize: ScreenSize
  width: number
  height: number
}

/**
 * 设备检测工具类
 */
export class DeviceDetector {
  /**
   * 检测是否为移动设备
   */
  static isMobile(): boolean {
    if (typeof window === 'undefined') return false
    
    const userAgent = navigator.userAgent.toLowerCase()
    const mobileKeywords = [
      'mobile', 'android', 'iphone', 'ipod', 'blackberry', 
      'windows phone', 'opera mini', 'iemobile'
    ]
    
    return mobileKeywords.some(keyword => userAgent.includes(keyword)) ||
           window.innerWidth < BREAKPOINTS.md
  }

  /**
   * 检测是否为平板设备
   */
  static isTablet(): boolean {
    if (typeof window === 'undefined') return false
    
    const userAgent = navigator.userAgent.toLowerCase()
    const tabletKeywords = ['ipad', 'tablet', 'kindle']
    const isTabletUA = tabletKeywords.some(keyword => userAgent.includes(keyword))
    
    // 基于屏幕尺寸判断平板
    const width = window.innerWidth
    const isTabletSize = width >= BREAKPOINTS.md && width < BREAKPOINTS.xl
    
    return isTabletUA || (isTabletSize && this.hasTouch())
  }

  /**
   * 检测是否为桌面设备
   */
  static isDesktop(): boolean {
    return !this.isMobile() && !this.isTablet()
  }

  /**
   * 检测是否为iOS设备
   */
  static isIOS(): boolean {
    if (typeof window === 'undefined') return false
    
    const userAgent = navigator.userAgent.toLowerCase()
    return /iphone|ipad|ipod/.test(userAgent)
  }

  /**
   * 检测是否为Android设备
   */
  static isAndroid(): boolean {
    if (typeof window === 'undefined') return false
    
    const userAgent = navigator.userAgent.toLowerCase()
    return /android/.test(userAgent)
  }

  /**
   * 检测是否支持触摸
   */
  static hasTouch(): boolean {
    if (typeof window === 'undefined') return false
    
    return 'ontouchstart' in window || 
           navigator.maxTouchPoints > 0 ||
           // @ts-ignore
           navigator.msMaxTouchPoints > 0
  }

  /**
   * 获取操作系统类型
   */
  static getOS(): DeviceInfo['os'] {
    if (typeof window === 'undefined') return 'unknown'
    
    const userAgent = navigator.userAgent.toLowerCase()
    
    if (/iphone|ipad|ipod/.test(userAgent)) return 'ios'
    if (/android/.test(userAgent)) return 'android'
    if (/windows/.test(userAgent)) return 'windows'
    if (/mac/.test(userAgent)) return 'macos'
    if (/linux/.test(userAgent)) return 'linux'
    
    return 'unknown'
  }

  /**
   * 获取浏览器类型
   */
  static getBrowser(): DeviceInfo['browser'] {
    if (typeof window === 'undefined') return 'other'
    
    const userAgent = navigator.userAgent.toLowerCase()
    
    if (/chrome/.test(userAgent) && !/edge/.test(userAgent)) return 'chrome'
    if (/safari/.test(userAgent) && !/chrome/.test(userAgent)) return 'safari'
    if (/firefox/.test(userAgent)) return 'firefox'
    if (/edge/.test(userAgent)) return 'edge'
    
    return 'other'
  }

  /**
   * 获取屏幕方向
   */
  static getOrientation(): DeviceInfo['orientation'] {
    if (typeof window === 'undefined') return 'portrait'
    
    return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
  }

  /**
   * 获取设备像素比
   */
  static getPixelRatio(): number {
    if (typeof window === 'undefined') return 1
    
    return window.devicePixelRatio || 1
  }

  /**
   * 获取完整设备信息
   */
  static getDeviceInfo(): DeviceInfo {
    const isMobile = this.isMobile()
    const isTablet = this.isTablet()
    
    let type: DeviceInfo['type'] = 'desktop'
    if (isMobile) type = 'mobile'
    else if (isTablet) type = 'tablet'

    return {
      type,
      os: this.getOS(),
      browser: this.getBrowser(),
      hasTouch: this.hasTouch(),
      orientation: this.getOrientation(),
      pixelRatio: this.getPixelRatio()
    }
  }

  /**
   * 获取当前屏幕尺寸分类
   */
  static getScreenSize(): ScreenSize {
    if (typeof window === 'undefined') return 'lg'
    
    const width = window.innerWidth
    
    if (width < BREAKPOINTS.sm) return 'xs'
    if (width < BREAKPOINTS.md) return 'sm'
    if (width < BREAKPOINTS.lg) return 'md'
    if (width < BREAKPOINTS.xl) return 'lg'
    if (width < BREAKPOINTS.xxl) return 'xl'
    return 'xxl'
  }

  /**
   * 获取响应式信息
   */
  static getResponsiveInfo(): ResponsiveInfo {
    if (typeof window === 'undefined') {
      return {
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        screenSize: 'lg',
        width: 1200,
        height: 800
      }
    }

    const isMobile = this.isMobile()
    const isTablet = this.isTablet()
    const isDesktop = this.isDesktop()

    return {
      isMobile,
      isTablet,
      isDesktop,
      screenSize: this.getScreenSize(),
      width: window.innerWidth,
      height: window.innerHeight
    }
  }
}

/**
 * 响应式工具函数
 */
export class ResponsiveUtils {
  /**
   * 检查当前屏幕是否匹配指定断点
   */
  static matchBreakpoint(breakpoint: BreakpointKey): boolean {
    if (typeof window === 'undefined') return false
    
    const width = window.innerWidth
    const breakpointValue = BREAKPOINTS[breakpoint]
    
    // 获取下一个断点值
    const breakpointKeys = Object.keys(BREAKPOINTS) as BreakpointKey[]
    const currentIndex = breakpointKeys.indexOf(breakpoint)
    const nextBreakpoint = breakpointKeys[currentIndex + 1]
    const nextBreakpointValue = nextBreakpoint ? BREAKPOINTS[nextBreakpoint] : Infinity
    
    return width >= breakpointValue && width < nextBreakpointValue
  }

  /**
   * 检查当前屏幕是否大于等于指定断点
   */
  static isBreakpointUp(breakpoint: BreakpointKey): boolean {
    if (typeof window === 'undefined') return false
    
    return window.innerWidth >= BREAKPOINTS[breakpoint]
  }

  /**
   * 检查当前屏幕是否小于指定断点
   */
  static isBreakpointDown(breakpoint: BreakpointKey): boolean {
    if (typeof window === 'undefined') return false
    
    return window.innerWidth < BREAKPOINTS[breakpoint]
  }

  /**
   * 获取适合当前屏幕的网格列数
   */
  static getGridColumns(): number {
    const screenSize = DeviceDetector.getScreenSize()
    
    const columnMap: Record<ScreenSize, number> = {
      xs: 1,
      sm: 2,
      md: 2,
      lg: 3,
      xl: 4,
      xxl: 4
    }
    
    return columnMap[screenSize]
  }

  /**
   * 获取适合当前屏幕的容器最大宽度
   */
  static getContainerMaxWidth(): string {
    const screenSize = DeviceDetector.getScreenSize()
    
    const maxWidthMap: Record<ScreenSize, string> = {
      xs: '100%',
      sm: '540px',
      md: '720px',
      lg: '960px',
      xl: '1140px',
      xxl: '1320px'
    }
    
    return maxWidthMap[screenSize]
  }

  /**
   * 获取适合当前屏幕的间距大小
   */
  static getSpacing(size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md'): number {
    const isMobile = DeviceDetector.isMobile()
    
    const spacingMap = {
      xs: isMobile ? 4 : 8,
      sm: isMobile ? 8 : 12,
      md: isMobile ? 12 : 16,
      lg: isMobile ? 16 : 24,
      xl: isMobile ? 24 : 32
    }
    
    return spacingMap[size]
  }
}

/**
 * 媒体查询工具
 */
export class MediaQueryUtils {
  /**
   * 创建媒体查询字符串
   */
  static createQuery(breakpoint: BreakpointKey, direction: 'up' | 'down' | 'only' = 'up'): string {
    const value = BREAKPOINTS[breakpoint]
    
    switch (direction) {
      case 'up':
        return `(min-width: ${value}px)`
      case 'down':
        return `(max-width: ${value - 1}px)`
      case 'only':
        const breakpointKeys = Object.keys(BREAKPOINTS) as BreakpointKey[]
        const currentIndex = breakpointKeys.indexOf(breakpoint)
        const nextBreakpoint = breakpointKeys[currentIndex + 1]
        const nextValue = nextBreakpoint ? BREAKPOINTS[nextBreakpoint] : Infinity
        
        if (nextValue === Infinity) {
          return `(min-width: ${value}px)`
        }
        return `(min-width: ${value}px) and (max-width: ${nextValue - 1}px)`
      default:
        return `(min-width: ${value}px)`
    }
  }

  /**
   * 检查媒体查询是否匹配
   */
  static matches(query: string): boolean {
    if (typeof window === 'undefined') return false
    
    return window.matchMedia(query).matches
  }

  /**
   * 监听媒体查询变化
   */
  static addListener(query: string, callback: (matches: boolean) => void): () => void {
    if (typeof window === 'undefined') return () => {}
    
    const mediaQuery = window.matchMedia(query)
    const handler = (e: MediaQueryListEvent) => callback(e.matches)
    
    // 兼容旧版本浏览器
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handler)
      return () => mediaQuery.removeEventListener('change', handler)
    } else {
      // @ts-ignore
      mediaQuery.addListener(handler)
      // @ts-ignore
      return () => mediaQuery.removeListener(handler)
    }
  }
}

// 默认响应式配置
export const DEFAULT_RESPONSIVE_CONFIG: ResponsiveConfig = {
  breakpoints: BREAKPOINTS,
  containerMaxWidths: {
    xs: 0,
    sm: 540,
    md: 720,
    lg: 960,
    xl: 1140,
    xxl: 1320
  },
  gridColumns: {
    xs: 1,
    sm: 2,
    md: 2,
    lg: 3,
    xl: 4,
    xxl: 4
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32
  }
}