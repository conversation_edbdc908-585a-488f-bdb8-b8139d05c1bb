import { NotificationHelpers } from './notification-helpers'
import { prisma } from './prisma'

/**
 * 创建示例通知用于测试
 */
export async function seedNotifications() {
  try {
    console.log('开始创建示例通知...')

    // 获取第一个管理员用户
    const adminUser = await prisma.user.findFirst({
      where: {
        role: 'admin',
        is_active: true,
      }
    })

    if (!adminUser) {
      console.log('未找到管理员用户，跳过创建示例通知')
      return
    }

    console.log(`为用户 ${adminUser.username} (ID: ${adminUser.id}) 创建示例通知`)

    // 创建各种类型的示例通知
    const notifications = [
      {
        title: '系统维护通知',
        message: '系统将于今晚23:00-24:00进行维护升级，期间可能影响服务访问。',
        type: 'WARNING' as const,
        sourceType: 'system_maintenance',
      },
      {
        title: 'Webhook验证失败',
        message: '表单 testForm001 的webhook数据验证失败：字段类型不匹配',
        type: 'ERROR' as const,
        actionUrl: '/dashboard/webhook-failures?formId=testForm001',
        sourceType: 'webhook_failure',
        sourceId: 'test_failure_001',
      },
      {
        title: '新表单配置创建',
        message: '表单 "肺功能检测问卷" (lungTest2024) 已被创建',
        type: 'INFO' as const,
        actionUrl: '/dashboard/forms/config',
        sourceType: 'form_config',
        sourceId: 'lungTest2024',
      },
      {
        title: '数据导出完成',
        message: '您的数据导出已完成，共 1,234 条记录。文件：export_2024-07-13.xlsx',
        type: 'SUCCESS' as const,
        actionUrl: '/dashboard/exports',
        sourceType: 'data_export',
        sourceId: 'export_2024-07-13.xlsx',
      },
      {
        title: '登录异常提醒',
        message: '检测到用户 testuser 从异常IP地址登录：*************',
        type: 'WARNING' as const,
        actionUrl: '/dashboard/system-logs',
        sourceType: 'login_anomaly',
        sourceId: 'testuser',
      }
    ]

    // 创建通知
    for (const notification of notifications) {
      await NotificationHelpers.notifyUser(
        adminUser.id,
        notification.title,
        notification.message,
        notification.type,
        notification.actionUrl,
        notification.sourceType,
        notification.sourceId
      )
    }

    console.log(`成功创建 ${notifications.length} 个示例通知`)

    // 创建一些已读通知（用于测试）
    const readNotifications = [
      {
        title: '昨日数据统计',
        message: '昨日共收到 156 条表单数据，其中有效数据 152 条。',
        type: 'INFO' as const,
        sourceType: 'daily_stats',
      },
      {
        title: '系统备份完成',
        message: '系统数据备份已完成，备份文件大小：2.3GB',
        type: 'SUCCESS' as const,
        sourceType: 'system_backup',
      }
    ]

    // 创建已读通知
    for (const notification of readNotifications) {
      const created = await NotificationHelpers.notifyUser(
        adminUser.id,
        notification.title,
        notification.message,
        notification.type,
        undefined,
        notification.sourceType
      )

      // 立即标记为已读
      if (created) {
        const recentNotification = await prisma.notification.findFirst({
          where: {
            userId: adminUser.id,
            title: notification.title,
          },
          orderBy: {
            createdAt: 'desc'
          }
        })

        if (recentNotification) {
          await prisma.notification.update({
            where: { id: recentNotification.id },
            data: { read: true }
          })
        }
      }
    }

    console.log(`成功创建 ${readNotifications.length} 个已读示例通知`)
    console.log('示例通知创建完成！')

  } catch (error) {
    console.error('创建示例通知失败:', error)
  }
}

// 如果直接运行此文件
if (require.main === module) {
  seedNotifications()
    .then(() => {
      console.log('种子数据创建完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('种子数据创建失败:', error)
      process.exit(1)
    })
}