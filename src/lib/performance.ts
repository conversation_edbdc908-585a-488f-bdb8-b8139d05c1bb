/**
 * 性能监控和优化工具
 * 提供性能指标收集、分析和优化建议
 */

// 性能指标接口
export interface PerformanceMetrics {
  // 页面加载性能
  pageLoad: {
    domContentLoaded: number
    loadComplete: number
    firstContentfulPaint: number
    largestContentfulPaint: number
    firstInputDelay: number
    cumulativeLayoutShift: number
  }
  
  // 资源加载性能
  resources: {
    totalSize: number
    loadTime: number
    cacheHitRate: number
    failedRequests: number
  }
  
  // 运行时性能
  runtime: {
    memoryUsage: number
    jsHeapSize: number
    renderTime: number
    interactionLatency: number
  }
  
  // 网络性能
  network: {
    connectionType: string
    downlink: number
    rtt: number
    effectiveType: string
  }
  
  // 用户体验指标
  userExperience: {
    timeToInteractive: number
    totalBlockingTime: number
    speedIndex: number
  }
}

// 性能警告级别
export enum PerformanceWarningLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 性能警告接口
export interface PerformanceWarning {
  level: PerformanceWarningLevel
  metric: string
  value: number
  threshold: number
  message: string
  suggestion: string
}

// 性能优化建议接口
export interface PerformanceOptimization {
  category: string
  priority: 'high' | 'medium' | 'low'
  title: string
  description: string
  impact: string
  implementation: string[]
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {}
  private observers: PerformanceObserver[] = []
  private startTime = performance.now()

  constructor() {
    this.initializeObservers()
    this.collectInitialMetrics()
  }

  // 初始化性能观察器
  private initializeObservers(): void {
    // 观察导航时间
    if ('PerformanceObserver' in window) {
      try {
        // 观察页面加载性能
        const navigationObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              this.processNavigationEntry(entry as PerformanceNavigationTiming)
            }
          }
        })
        navigationObserver.observe({ entryTypes: ['navigation'] })
        this.observers.push(navigationObserver)

        // 观察资源加载性能
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'resource') {
              this.processResourceEntry(entry as PerformanceResourceTiming)
            }
          }
        })
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)

        // 观察绘制性能
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'paint') {
              this.processPaintEntry(entry as PerformancePaintTiming)
            }
          }
        })
        paintObserver.observe({ entryTypes: ['paint'] })
        this.observers.push(paintObserver)

        // 观察最大内容绘制
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'largest-contentful-paint') {
              this.processLCPEntry(entry as any)
            }
          }
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)

        // 观察首次输入延迟
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'first-input') {
              this.processFIDEntry(entry as any)
            }
          }
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)

        // 观察布局偏移
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'layout-shift') {
              this.processCLSEntry(entry as any)
            }
          }
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)
      } catch (error) {
        console.warn('[PerformanceMonitor] Failed to initialize observers:', error)
      }
    }
  }

  // 收集初始指标
  private collectInitialMetrics(): void {
    // 收集内存使用情况
    if ('memory' in performance) {
      const memory = (performance as any).memory
      this.metrics.runtime = {
        ...this.metrics.runtime,
        memoryUsage: memory.usedJSHeapSize,
        jsHeapSize: memory.totalJSHeapSize,
        renderTime: 0,
        interactionLatency: 0
      }
    }

    // 收集网络信息
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      this.metrics.network = {
        connectionType: connection.type || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0,
        effectiveType: connection.effectiveType || 'unknown'
      }
    }
  }

  // 处理导航性能条目
  private processNavigationEntry(entry: PerformanceNavigationTiming): void {
    this.metrics.pageLoad = {
      ...this.metrics.pageLoad,
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart
    }
  }

  // 处理资源性能条目
  private processResourceEntry(entry: PerformanceResourceTiming): void {
    if (!this.metrics.resources) {
      this.metrics.resources = {
        totalSize: 0,
        loadTime: 0,
        cacheHitRate: 0,
        failedRequests: 0
      }
    }

    // 累计资源大小和加载时间
    const size = entry.transferSize || 0
    const loadTime = entry.responseEnd - entry.requestStart

    this.metrics.resources.totalSize += size
    this.metrics.resources.loadTime = Math.max(this.metrics.resources.loadTime, loadTime)

    // 检查缓存命中
    if (entry.transferSize === 0 && entry.decodedBodySize > 0) {
      // 从缓存加载
      this.metrics.resources.cacheHitRate += 1
    }
  }

  // 处理绘制性能条目
  private processPaintEntry(entry: PerformancePaintTiming): void {
    if (!this.metrics.pageLoad) {
      this.metrics.pageLoad = {} as any
    }

    if (entry.name === 'first-contentful-paint') {
      this.metrics.pageLoad.firstContentfulPaint = entry.startTime
    }
  }

  // 处理最大内容绘制条目
  private processLCPEntry(entry: any): void {
    if (!this.metrics.pageLoad) {
      this.metrics.pageLoad = {} as any
    }
    this.metrics.pageLoad.largestContentfulPaint = entry.startTime
  }

  // 处理首次输入延迟条目
  private processFIDEntry(entry: any): void {
    if (!this.metrics.pageLoad) {
      this.metrics.pageLoad = {} as any
    }
    this.metrics.pageLoad.firstInputDelay = entry.processingStart - entry.startTime
  }

  // 处理累积布局偏移条目
  private processCLSEntry(entry: any): void {
    if (!this.metrics.pageLoad) {
      this.metrics.pageLoad = {} as any
    }
    
    if (!entry.hadRecentInput) {
      this.metrics.pageLoad.cumulativeLayoutShift = 
        (this.metrics.pageLoad.cumulativeLayoutShift || 0) + entry.value
    }
  }

  // 获取当前性能指标
  getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics }
  }

  // 分析性能并生成警告
  analyzePerformance(): PerformanceWarning[] {
    const warnings: PerformanceWarning[] = []
    const metrics = this.metrics

    // 检查页面加载性能
    if (metrics.pageLoad) {
      const { firstContentfulPaint, largestContentfulPaint, firstInputDelay, cumulativeLayoutShift } = metrics.pageLoad

      // FCP 检查
      if (firstContentfulPaint && firstContentfulPaint > 1800) {
        warnings.push({
          level: firstContentfulPaint > 3000 ? PerformanceWarningLevel.ERROR : PerformanceWarningLevel.WARNING,
          metric: 'First Contentful Paint',
          value: firstContentfulPaint,
          threshold: 1800,
          message: `首次内容绘制时间过长: ${Math.round(firstContentfulPaint)}ms`,
          suggestion: '优化关键渲染路径，减少阻塞资源'
        })
      }

      // LCP 检查
      if (largestContentfulPaint && largestContentfulPaint > 2500) {
        warnings.push({
          level: largestContentfulPaint > 4000 ? PerformanceWarningLevel.ERROR : PerformanceWarningLevel.WARNING,
          metric: 'Largest Contentful Paint',
          value: largestContentfulPaint,
          threshold: 2500,
          message: `最大内容绘制时间过长: ${Math.round(largestContentfulPaint)}ms`,
          suggestion: '优化图片加载，使用预加载和懒加载'
        })
      }

      // FID 检查
      if (firstInputDelay && firstInputDelay > 100) {
        warnings.push({
          level: firstInputDelay > 300 ? PerformanceWarningLevel.ERROR : PerformanceWarningLevel.WARNING,
          metric: 'First Input Delay',
          value: firstInputDelay,
          threshold: 100,
          message: `首次输入延迟过长: ${Math.round(firstInputDelay)}ms`,
          suggestion: '减少主线程阻塞，优化JavaScript执行'
        })
      }

      // CLS 检查
      if (cumulativeLayoutShift && cumulativeLayoutShift > 0.1) {
        warnings.push({
          level: cumulativeLayoutShift > 0.25 ? PerformanceWarningLevel.ERROR : PerformanceWarningLevel.WARNING,
          metric: 'Cumulative Layout Shift',
          value: cumulativeLayoutShift,
          threshold: 0.1,
          message: `累积布局偏移过大: ${cumulativeLayoutShift.toFixed(3)}`,
          suggestion: '为图片和广告预留空间，避免动态内容插入'
        })
      }
    }

    // 检查内存使用
    if (metrics.runtime?.memoryUsage && metrics.runtime.memoryUsage > 50 * 1024 * 1024) {
      warnings.push({
        level: metrics.runtime.memoryUsage > 100 * 1024 * 1024 ? PerformanceWarningLevel.ERROR : PerformanceWarningLevel.WARNING,
        metric: 'Memory Usage',
        value: metrics.runtime.memoryUsage,
        threshold: 50 * 1024 * 1024,
        message: `内存使用过高: ${Math.round(metrics.runtime.memoryUsage / 1024 / 1024)}MB`,
        suggestion: '检查内存泄漏，优化数据结构和缓存策略'
      })
    }

    return warnings
  }

  // 生成性能优化建议
  generateOptimizations(): PerformanceOptimization[] {
    const optimizations: PerformanceOptimization[] = []
    const warnings = this.analyzePerformance()

    // 基于警告生成优化建议
    warnings.forEach(warning => {
      switch (warning.metric) {
        case 'First Contentful Paint':
          optimizations.push({
            category: '页面加载',
            priority: 'high',
            title: '优化首次内容绘制',
            description: '减少首次内容绘制时间，提升用户感知性能',
            impact: '用户能更快看到页面内容',
            implementation: [
              '内联关键CSS',
              '预加载重要资源',
              '优化字体加载',
              '减少阻塞JavaScript'
            ]
          })
          break

        case 'Largest Contentful Paint':
          optimizations.push({
            category: '资源优化',
            priority: 'high',
            title: '优化最大内容绘制',
            description: '加快主要内容的加载和渲染',
            impact: '提升页面加载完成的感知速度',
            implementation: [
              '优化图片格式和大小',
              '使用CDN加速',
              '实现图片懒加载',
              '预加载关键资源'
            ]
          })
          break

        case 'First Input Delay':
          optimizations.push({
            category: '交互性能',
            priority: 'medium',
            title: '减少首次输入延迟',
            description: '提升页面交互响应速度',
            impact: '用户操作响应更快',
            implementation: [
              '代码分割和懒加载',
              '优化JavaScript执行',
              '使用Web Workers',
              '减少主线程阻塞'
            ]
          })
          break

        case 'Cumulative Layout Shift':
          optimizations.push({
            category: '视觉稳定性',
            priority: 'medium',
            title: '减少累积布局偏移',
            description: '提升页面视觉稳定性',
            impact: '避免用户误操作，提升体验',
            implementation: [
              '为图片设置尺寸属性',
              '预留广告位空间',
              '避免动态插入内容',
              '使用CSS aspect-ratio'
            ]
          })
          break

        case 'Memory Usage':
          optimizations.push({
            category: '内存优化',
            priority: 'low',
            title: '优化内存使用',
            description: '减少内存占用，防止内存泄漏',
            impact: '提升应用稳定性和性能',
            implementation: [
              '及时清理事件监听器',
              '优化数据结构',
              '实现虚拟滚动',
              '使用对象池'
            ]
          })
          break
      }
    })

    // 添加通用优化建议
    optimizations.push(
      {
        category: '缓存策略',
        priority: 'high',
        title: '实现智能缓存',
        description: '通过缓存策略减少网络请求',
        impact: '显著提升重复访问性能',
        implementation: [
          '配置HTTP缓存头',
          '实现Service Worker缓存',
          '使用浏览器缓存API',
          '实现数据缓存策略'
        ]
      },
      {
        category: '网络优化',
        priority: 'medium',
        title: '优化网络请求',
        description: '减少网络请求数量和大小',
        impact: '提升加载速度，减少流量消耗',
        implementation: [
          '合并CSS和JavaScript文件',
          '压缩资源文件',
          '使用HTTP/2',
          '实现请求去重'
        ]
      },
      {
        category: '渲染优化',
        priority: 'medium',
        title: '优化渲染性能',
        description: '提升页面渲染效率',
        impact: '减少卡顿，提升流畅度',
        implementation: [
          '使用CSS transform代替position',
          '避免强制同步布局',
          '优化动画性能',
          '实现虚拟滚动'
        ]
      }
    )

    return optimizations
  }

  // 开始性能测量
  startMeasure(name: string): void {
    performance.mark(`${name}-start`)
  }

  // 结束性能测量
  endMeasure(name: string): number {
    performance.mark(`${name}-end`)
    performance.measure(name, `${name}-start`, `${name}-end`)
    
    const measure = performance.getEntriesByName(name, 'measure')[0]
    return measure ? measure.duration : 0
  }

  // 清理观察器
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }

  // 导出性能报告
  exportReport(): string {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: this.getMetrics(),
      warnings: this.analyzePerformance(),
      optimizations: this.generateOptimizations(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    return JSON.stringify(report, null, 2)
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()

// 性能工具函数
export const performanceUtils = {
  // 测量函数执行时间
  measureFunction: <T extends (...args: any[]) => any>(
    fn: T,
    name?: string
  ): T => {
    return ((...args: any[]) => {
      const measureName = name || fn.name || 'anonymous'
      performanceMonitor.startMeasure(measureName)
      
      try {
        const result = fn(...args)
        
        // 处理异步函数
        if (result instanceof Promise) {
          return result.finally(() => {
            performanceMonitor.endMeasure(measureName)
          })
        }
        
        performanceMonitor.endMeasure(measureName)
        return result
      } catch (error) {
        performanceMonitor.endMeasure(measureName)
        throw error
      }
    }) as T
  },

  // 防抖函数
  debounce: <T extends (...args: any[]) => any>(
    fn: T,
    delay: number
  ): T => {
    let timeoutId: NodeJS.Timeout
    return ((...args: any[]) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => fn(...args), delay)
    }) as T
  },

  // 节流函数
  throttle: <T extends (...args: any[]) => any>(
    fn: T,
    delay: number
  ): T => {
    let lastCall = 0
    return ((...args: any[]) => {
      const now = Date.now()
      if (now - lastCall >= delay) {
        lastCall = now
        return fn(...args)
      }
    }) as T
  },

  // 批量处理函数
  batchProcess: <T>(
    items: T[],
    processor: (item: T) => void,
    batchSize: number = 100,
    delay: number = 0
  ): Promise<void> => {
    return new Promise((resolve) => {
      let index = 0
      
      const processBatch = () => {
        const endIndex = Math.min(index + batchSize, items.length)
        
        for (let i = index; i < endIndex; i++) {
          processor(items[i])
        }
        
        index = endIndex
        
        if (index < items.length) {
          setTimeout(processBatch, delay)
        } else {
          resolve()
        }
      }
      
      processBatch()
    })
  }
}

// 导出相关类型
export type {
  PerformanceMetrics,
  PerformanceWarning,
  PerformanceOptimization
}