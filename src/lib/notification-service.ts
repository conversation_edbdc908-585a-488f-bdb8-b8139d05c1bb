import { prisma } from '@/lib/prisma'
import { Notification, NotificationType } from '@prisma/client'
import type { CreateNotificationRequest, NotificationListResponse } from '@/types'
import { withCache, cacheKeys, invalidateCache, cache } from './cache'

export class NotificationService {
  /**
   * 创建新通知
   */
  static async createNotification(data: CreateNotificationRequest): Promise<Notification> {
    const notification = await prisma.notification.create({
      data: {
        userId: data.userId,
        title: data.title,
        message: data.message,
        type: data.type as NotificationType,
        actionUrl: data.actionUrl,
        sourceType: data.sourceType,
        sourceId: data.sourceId,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
          }
        }
      }
    })

    // 清除用户相关的通知缓存
    this.invalidateUserNotificationCache(data.userId)

    return notification
  }

  /**
   * 获取用户通知列表
   */
  static async getNotifications(
    userId: number,
    options: {
      page?: number
      limit?: number
      read?: boolean
      type?: NotificationType
    } = {}
  ): Promise<NotificationListResponse> {
    const { page = 1, limit = 20, read, type } = options
    const skip = (page - 1) * limit

    const where = {
      userId,
      ...(read !== undefined && { read }),
      ...(type && { type }),
    }

    const [notifications, total, unreadCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit,
      }),
      prisma.notification.count({ where }),
      prisma.notification.count({
        where: {
          userId,
          read: false,
        }
      })
    ])

    return {
      notifications: notifications.map(notification => ({
        id: notification.id,
        userId: notification.userId,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        read: notification.read,
        actionUrl: notification.actionUrl || undefined,
        sourceType: notification.sourceType || undefined,
        sourceId: notification.sourceId || undefined,
        createdAt: notification.createdAt.toISOString(),
        updatedAt: notification.updatedAt.toISOString(),
      })),
      total,
      unreadCount,
    }
  }

  /**
   * 标记通知为已读/未读
   */
  static async markAsRead(id: string, userId: number, read: boolean = true): Promise<Notification | null> {
    const notification = await prisma.notification.findFirst({
      where: {
        id,
        userId, // 确保用户只能操作自己的通知
      }
    })

    if (!notification) {
      return null
    }

    const updatedNotification = await prisma.notification.update({
      where: { id },
      data: { read }
    })

    // 清除用户相关的通知缓存
    this.invalidateUserNotificationCache(userId)

    return updatedNotification
  }

  /**
   * 删除通知
   */
  static async deleteNotification(id: string, userId: number): Promise<boolean> {
    const notification = await prisma.notification.findFirst({
      where: {
        id,
        userId, // 确保用户只能删除自己的通知
      }
    })

    if (!notification) {
      return false
    }

    await prisma.notification.delete({
      where: { id }
    })

    // 清除用户相关的通知缓存
    this.invalidateUserNotificationCache(userId)

    return true
  }

  /**
   * 批量标记为已读
   */
  static async markAllAsRead(userId: number): Promise<number> {
    const result = await prisma.notification.updateMany({
      where: {
        userId,
        read: false,
      },
      data: {
        read: true,
      }
    })

    // 清除用户相关的通知缓存
    this.invalidateUserNotificationCache(userId)

    return result.count
  }

  /**
   * 批量删除通知
   */
  static async deleteMultiple(ids: string[], userId: number): Promise<number> {
    const result = await prisma.notification.deleteMany({
      where: {
        id: {
          in: ids
        },
        userId, // 确保用户只能删除自己的通知
      }
    })

    // 清除用户相关的通知缓存
    this.invalidateUserNotificationCache(userId)

    return result.count
  }

  /**
   * 获取未读通知数量（带缓存）
   */
  static async getUnreadCount(userId: number): Promise<number> {
    const cacheKey = `notifications:unread_count:${userId}`
    
    return await withCache(
      cacheKey,
      async () => {
        return await prisma.notification.count({
          where: {
            userId,
            read: false,
          }
        })
      },
      60 // 缓存60秒
    )
  }

  /**
   * 清理过期通知 (30天前的已读通知)
   */
  static async cleanupOldNotifications(): Promise<number> {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const result = await prisma.notification.deleteMany({
      where: {
        read: true,
        createdAt: {
          lt: thirtyDaysAgo
        }
      }
    })

    return result.count
  }

  /**
   * 清除用户通知相关缓存
   */
  private static invalidateUserNotificationCache(userId: number) {
    // 清除未读数量缓存
    const unreadCountKey = `notifications:unread_count:${userId}`
    cache.delete(unreadCountKey)
  }

  /**
   * 批量清除多个用户的通知缓存
   */
  static invalidateMultipleUsersCache(userIds: number[]) {
    const cacheKeys = userIds.map(userId => `notifications:unread_count:${userId}`)
    cacheKeys.forEach(key => cache.delete(key))
  }
}