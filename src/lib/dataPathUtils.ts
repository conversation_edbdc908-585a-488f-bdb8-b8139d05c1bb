import { DataPathConfig, WebhookData } from '@/types'

/**
 * 从嵌套对象中根据路径提取值
 * @param obj 源对象
 * @param path 路径字符串，如 "entry.serial_number"
 * @returns 提取到的值，如果路径不存在返回undefined
 */
export function getValueByPath(obj: any, path: string): any {
  if (!path || !obj) return undefined
  
  const keys = path.split('.')
  let current = obj
  
  for (const key of keys) {
    if (current === null || current === undefined) return undefined
    current = current[key]
  }
  
  return current
}

/**
 * 根据数据路径配置提取webhook数据
 * @param rawData 原始webhook数据
 * @param dataPathConfig 数据路径配置
 * @returns 提取后的标准化数据
 */
export function extractWebhookData(rawData: WebhookData, dataPathConfig: DataPathConfig) {
  const { paths, fieldPrefix = 'field_', customFieldMapping = {} } = dataPathConfig
  
  // 提取基本信息
  const formId = getValueByPath(rawData, paths.formId)
  const formName = getValueByPath(rawData, paths.formName)
  const entryData = getValueByPath(rawData, paths.entryData)
  
  if (!formId || !entryData) {
    throw new Error('Invalid webhook data: missing required fields')
  }
  
  // 提取元数据
  const serialNumber = paths.serialNumber ? getValueByPath(rawData, paths.serialNumber) : undefined
  const createdAt = paths.createdAt ? getValueByPath(rawData, paths.createdAt) : undefined
  const updatedAt = paths.updatedAt ? getValueByPath(rawData, paths.updatedAt) : undefined
  const creatorName = paths.creatorName ? getValueByPath(rawData, paths.creatorName) : undefined
  const ipAddress = paths.ipAddress ? getValueByPath(rawData, paths.ipAddress) : undefined
  
  // 提取字段数据
  const fields: Record<string, any> = {}
  
  if (entryData && typeof entryData === 'object') {
    for (const [key, value] of Object.entries(entryData)) {
      // 跳过元数据字段
      if (key === 'token' || key === 'serial_number' || key === 'created_at' || 
          key === 'updated_at' || key === 'creator_name' || key.startsWith('info_')) {
        continue
      }
      
      // 处理字段映射
      let fieldName = key
      if (customFieldMapping[key]) {
        fieldName = customFieldMapping[key]
      } else if (key.startsWith(fieldPrefix)) {
        fieldName = key
      } else {
        // 如果字段不以指定前缀开头，添加前缀
        fieldName = `${fieldPrefix}${key}`
      }
      
      fields[fieldName] = value
    }
  }
  
  return {
    formId,
    formName,
    entryData,
    serialNumber,
    createdAt,
    updatedAt,
    creatorName,
    ipAddress,
    fields
  }
}

/**
 * 预定义的数据路径配置
 */
export const DATA_PATH_CONFIGS = {
  // 金数据标准格式
  jinshuju_standard: {
    paths: {
      formId: 'form',
      formName: 'form_name',
      entryData: 'entry',
      serialNumber: 'entry.serial_number',
      createdAt: 'entry.created_at',
      updatedAt: 'entry.updated_at',
      creatorName: 'entry.creator_name',
      ipAddress: 'entry.info_remote_ip'
    },
    fieldPrefix: 'field_'
  } as DataPathConfig,
  
  // 金数据自动化格式
  jinshuju_automation: {
    paths: {
      formId: 'form',
      formName: 'form_name',
      entryData: 'entry',
      serialNumber: 'entry.token',  // 自动化格式使用token作为唯一标识
      createdAt: 'entry.created_at',
      updatedAt: 'entry.updated_at',
      creatorName: 'entry.x_field_weixin_nickname' // 自动化格式可能使用微信昵称
    },
    fieldPrefix: 'field_',
    customFieldMapping: {
      'x_field_weixin_openid': 'x_field_weixin_openid',
      'x_field_weixin_unionid': 'x_field_weixin_unionid',
      'x_field_weixin_nickname': 'x_field_weixin_nickname',
      'gen_code': 'gen_code',
      'referral_link': 'referral_link',
      'referral_users_count': 'referral_users_count',
      'referred_from_associated_serial_number': 'referred_from_associated_serial_number'
    }
  } as DataPathConfig
}

/**
 * 根据数据格式获取对应的数据路径配置
 * @param dataFormat 数据格式类型
 * @returns 数据路径配置
 */
export function getDataPathConfig(dataFormat: string): DataPathConfig {
  const config = DATA_PATH_CONFIGS[dataFormat as keyof typeof DATA_PATH_CONFIGS]
  if (!config) {
    throw new Error(`Unsupported data format: ${dataFormat}`)
  }
  return config
}

/**
 * 自动检测webhook数据格式
 * @param rawData 原始webhook数据
 * @returns 检测到的数据格式
 */
export function detectWebhookDataFormat(rawData: WebhookData): string {
  // 检查是否是金数据格式
  if (rawData && typeof rawData === 'object' && 'form' in rawData && 'entry' in rawData) {
    const entry = rawData.entry
    if (entry && typeof entry === 'object') {
      // 检查是否有serial_number字段（标准格式）
      if ('serial_number' in entry && typeof entry.serial_number === 'number') {
        return 'jinshuju_standard'
      }
      // 检查是否有token字段（自动化格式）
      if ('token' in entry && typeof entry.token === 'string') {
        return 'jinshuju_automation'
      }
    }
  }
  
  // 默认返回自定义格式
  return 'custom'
}

/**
 * 验证webhook数据格式
 * @param rawData 原始webhook数据
 * @param expectedFormat 期望的格式
 * @returns 验证结果
 */
export function validateWebhookDataFormat(rawData: WebhookData, expectedFormat: string): {
  valid: boolean
  detected: string
  errors: string[]
} {
  const detected = detectWebhookDataFormat(rawData)
  const errors: string[] = []
  
  if (detected !== expectedFormat) {
    errors.push(`Expected format '${expectedFormat}', but detected '${detected}'`)
  }
  
  // 基本结构验证
  if (!rawData || typeof rawData !== 'object') {
    errors.push('Invalid webhook data: must be an object')
  } else {
    if (!('form' in rawData)) {
      errors.push('Missing required field: form')
    }
    if (!('entry' in rawData)) {
      errors.push('Missing required field: entry')
    }
  }
  
  return {
    valid: errors.length === 0,
    detected,
    errors
  }
}