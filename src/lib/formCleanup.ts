import { prisma } from '@/lib/prisma'
import { dropDynamicTable, checkTableExists } from '@/lib/dynamicTable'

export interface FormCleanupResult {
  success: boolean
  error?: string
  warnings?: string[]
  details?: {
    configDeleted: boolean
    tableDropped: boolean
    validationFailuresDeleted: number
    systemLogsDeleted: number
  }
}

/**
 * 完全清理表单相关的所有数据
 * 包括表单配置、动态数据表、验证失败记录等
 */
export async function completeFormCleanup(
  formId: string,
  userId: string,
  ipAddress: string = 'unknown',
  userAgent: string = 'unknown'
): Promise<FormCleanupResult> {
  const warnings: string[] = []
  let configDeleted = false
  let tableDropped = false
  let validationFailuresDeleted = 0
  const systemLogsDeleted = 0
  let formConfig: any = null

  try {
    // 1. 检查表单是否存在
    formConfig = await prisma.formConfig.findUnique({
      where: { formId },
      select: {
        id: true,
        formId: true,
        formName: true,
        tableName: true,
        isDeleted: true,
        isActive: true,
      },
    })

    if (!formConfig) {
      return {
        success: false,
        error: '表单配置不存在',
      }
    }

    // 2. 软删除表单配置（如果还没有被删除）
    if (!formConfig.isDeleted) {
      await prisma.formConfig.update({
        where: { formId },
        data: {
          isDeleted: true,
          isActive: false,
          updatedAt: new Date(),
        },
      })
      configDeleted = true
      console.log(`表单配置 ${formId} 已标记为删除`)
    } else {
      warnings.push('表单配置已经被标记为删除')
    }

    // 3. 删除动态数据表（如果存在）
    if (formConfig.tableName) {
      try {
        const dropResult = await dropDynamicTable(formId)
        if (dropResult.success) {
          tableDropped = true
          console.log(`动态数据表 ${formConfig.tableName} 已删除`)
        } else {
          warnings.push(`删除数据表失败: ${dropResult.error}`)
        }
      } catch (error) {
        warnings.push(`删除数据表时发生错误: ${error}`)
      }
    } else {
      warnings.push('表单配置中没有关联的数据表名')
    }

    // 4. 清理 Webhook 验证失败记录
    try {
      const validationFailureResult = await prisma.webhookValidationFailure.deleteMany({
        where: { formId },
      })
      validationFailuresDeleted = validationFailureResult.count
      if (validationFailuresDeleted > 0) {
        console.log(`已清理 ${validationFailuresDeleted} 个验证失败记录`)
      }
    } catch (error) {
      warnings.push(`清理验证失败记录时发生错误: ${error}`)
    }

    // 5. 记录系统日志（但不删除历史日志，保留审计跟踪）
    await prisma.systemLog.create({
      data: {
        userId: parseInt(userId),
        action: 'COMPLETE_FORM_CLEANUP',
        resource: 'FormConfig',
        resourceId: formConfig.id.toString(),
        details: {
          formId,
          formName: formConfig.formName,
          tableName: formConfig.tableName,
          cleanupDetails: {
            configDeleted,
            tableDropped,
            validationFailuresDeleted,
          },
          warnings: warnings.length > 0 ? warnings : undefined,
        },
        ipAddress,
        userAgent,
      },
    })

    const result: FormCleanupResult = {
      success: true,
      details: {
        configDeleted,
        tableDropped,
        validationFailuresDeleted,
        systemLogsDeleted,
      },
    }

    if (warnings.length > 0) {
      result.warnings = warnings
    }

    return result

  } catch (error) {
    console.error('完全清理表单数据失败:', error)
    
    // 记录清理失败的系统日志
    try {
      await prisma.systemLog.create({
        data: {
          userId: parseInt(userId),
          action: 'COMPLETE_FORM_CLEANUP_FAILED',
          resource: 'FormConfig',
          resourceId: formConfig?.id?.toString() || 'unknown',
          details: {
            formId,
            error: error instanceof Error ? error.message : String(error),
            partialResults: {
              configDeleted,
              tableDropped,
              validationFailuresDeleted,
            },
          },
          ipAddress,
          userAgent,
        },
      })
    } catch (logError) {
      console.error('记录清理失败日志失败:', logError)
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  }
}

/**
 * 清理所有孤立的验证失败记录
 * （对应已删除表单的验证失败记录）
 */
export async function cleanupOrphanedValidationFailures(): Promise<{
  success: boolean
  deletedCount: number
  error?: string
}> {
  try {
    // 查找所有已删除的表单ID
    const deletedForms = await prisma.formConfig.findMany({
      where: { isDeleted: true },
      select: { formId: true },
    })

    const deletedFormIds = deletedForms.map(form => form.formId)

    if (deletedFormIds.length === 0) {
      return { success: true, deletedCount: 0 }
    }

    // 删除这些表单对应的验证失败记录
    const deleteResult = await prisma.webhookValidationFailure.deleteMany({
      where: {
        formId: { in: deletedFormIds },
      },
    })

    console.log(`清理了 ${deleteResult.count} 个孤立的验证失败记录`)

    return {
      success: true,
      deletedCount: deleteResult.count,
    }

  } catch (error) {
    console.error('清理孤立验证失败记录失败:', error)
    return {
      success: false,
      deletedCount: 0,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 检查表单是否可以安全删除
 */
export async function checkFormDeletionSafety(formId: string): Promise<{
  safe: boolean
  warnings: string[]
  dataCount?: number
  tableName?: string
}> {
  const warnings: string[] = []

  try {
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId },
      select: {
        formId: true,
        formName: true,
        tableName: true,
        isActive: true,
        isDeleted: true,
      },
    })

    if (!formConfig) {
      return {
        safe: false,
        warnings: ['表单配置不存在'],
      }
    }

    if (formConfig.isDeleted) {
      warnings.push('表单已经被标记为删除')
    }

    if (formConfig.isActive) {
      warnings.push('表单当前处于活跃状态，删除后将停止接收数据')
    }

    // 检查是否有关联的数据表和数据
    let dataCount = 0
    if (formConfig.tableName) {
      try {
        // 检查表是否存在
        const tableExists = await checkTableExists(formConfig.tableName)
        if (tableExists) {
          const result = await prisma.$queryRawUnsafe<Array<{ count: bigint }>>(
            `SELECT COUNT(*) as count FROM \`${formConfig.tableName}\``
          )
          dataCount = Number(result[0]?.count || 0)
          
          if (dataCount > 0) {
            warnings.push(`数据表中包含 ${dataCount} 条记录，删除后将永久丢失`)
          }
        } else {
          warnings.push('对应的数据表不存在')
        }
      } catch (error) {
        warnings.push(`检查数据表时发生错误: ${error}`)
      }
    }

    return {
      safe: warnings.length === 0,
      warnings,
      dataCount,
      tableName: formConfig.tableName || undefined,
    }

  } catch (error) {
    return {
      safe: false,
      warnings: [`检查删除安全性失败: ${error}`],
    }
  }
}

// checkTableExists 函数已从 dynamicTable.ts 导入