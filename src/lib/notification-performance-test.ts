import { NotificationService } from './notification-service'
import { NotificationHelpers } from './notification-helpers'
import { prisma } from './prisma'

/**
 * 通知系统性能测试
 */
export async function performanceTest() {
  console.log('开始通知系统性能测试...')

  try {
    // 获取测试用户
    const testUser = await prisma.user.findFirst({
      where: {
        role: 'admin',
        is_active: true,
      }
    })

    if (!testUser) {
      console.log('未找到测试用户，测试终止')
      return
    }

    console.log(`使用用户 ${testUser.username} (ID: ${testUser.id}) 进行测试`)

    // 测试1: 创建通知性能
    console.log('\n测试1: 创建通知性能')
    const createStartTime = Date.now()
    
    const createPromises = Array.from({ length: 100 }, (_, i) =>
      NotificationService.createNotification({
        userId: testUser.id,
        title: `性能测试通知 ${i + 1}`,
        message: `这是第 ${i + 1} 个性能测试通知`,
        type: 'INFO',
        sourceType: 'performance_test',
        sourceId: `test_${i + 1}`,
      })
    )

    await Promise.all(createPromises)
    const createEndTime = Date.now()
    console.log(`创建100个通知耗时: ${createEndTime - createStartTime}ms`)
    console.log(`平均每个通知创建时间: ${(createEndTime - createStartTime) / 100}ms`)

    // 测试2: 获取通知列表性能
    console.log('\n测试2: 获取通知列表性能')
    const listStartTime = Date.now()
    
    const listResult = await NotificationService.getNotifications(testUser.id, {
      page: 1,
      limit: 50,
    })
    
    const listEndTime = Date.now()
    console.log(`获取通知列表耗时: ${listEndTime - listStartTime}ms`)
    console.log(`获取到 ${listResult.notifications.length} 个通知`)

    // 测试3: 获取未读数量性能（测试缓存）
    console.log('\n测试3: 获取未读数量性能（缓存测试）')
    
    // 第一次调用（无缓存）
    const unreadStartTime1 = Date.now()
    const unreadCount1 = await NotificationService.getUnreadCount(testUser.id)
    const unreadEndTime1 = Date.now()
    console.log(`第一次获取未读数量耗时: ${unreadEndTime1 - unreadStartTime1}ms，未读数量: ${unreadCount1}`)

    // 第二次调用（有缓存）
    const unreadStartTime2 = Date.now()
    const unreadCount2 = await NotificationService.getUnreadCount(testUser.id)
    const unreadEndTime2 = Date.now()
    console.log(`第二次获取未读数量耗时: ${unreadEndTime2 - unreadStartTime2}ms，未读数量: ${unreadCount2}`)

    // 测试4: 批量标记已读性能
    console.log('\n测试4: 批量标记已读性能')
    const markReadStartTime = Date.now()
    
    const markReadCount = await NotificationService.markAllAsRead(testUser.id)
    
    const markReadEndTime = Date.now()
    console.log(`批量标记已读耗时: ${markReadEndTime - markReadStartTime}ms`)
    console.log(`标记了 ${markReadCount} 个通知为已读`)

    // 测试5: 批量删除性能
    console.log('\n测试5: 批量删除性能')
    
    // 获取要删除的通知ID
    const notificationsToDelete = await prisma.notification.findMany({
      where: {
        userId: testUser.id,
        sourceType: 'performance_test',
      },
      select: {
        id: true,
      },
      take: 50,
    })

    if (notificationsToDelete.length > 0) {
      const deleteStartTime = Date.now()
      const idsToDelete = notificationsToDelete.map(n => n.id)
      
      const deleteCount = await NotificationService.deleteMultiple(idsToDelete, testUser.id)
      
      const deleteEndTime = Date.now()
      console.log(`批量删除耗时: ${deleteEndTime - deleteStartTime}ms`)
      console.log(`删除了 ${deleteCount} 个通知`)
    }

    // 测试6: 系统通知助手性能
    console.log('\n测试6: 系统通知助手性能')
    const helperStartTime = Date.now()
    
    await NotificationHelpers.notifySystemError(
      '性能测试',
      '这是一个性能测试的系统错误通知',
      { testType: 'performance' }
    )
    
    const helperEndTime = Date.now()
    console.log(`系统通知助手创建通知耗时: ${helperEndTime - helperStartTime}ms`)

    // 清理测试数据
    console.log('\n清理测试数据...')
    const cleanupResult = await prisma.notification.deleteMany({
      where: {
        userId: testUser.id,
        sourceType: 'performance_test',
      }
    })
    console.log(`清理了 ${cleanupResult.count} 个测试通知`)

    console.log('\n性能测试完成！')

  } catch (error) {
    console.error('性能测试失败:', error)
  }
}

// 如果直接运行此文件
if (require.main === module) {
  performanceTest()
    .then(() => {
      console.log('性能测试执行完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('性能测试执行失败:', error)
      process.exit(1)
    })
}