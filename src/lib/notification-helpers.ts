import { NotificationService } from './notification-service'
import { prisma } from './prisma'

/**
 * 创建系统通知的辅助函数
 */
export class NotificationHelpers {
  /**
   * 为所有管理员创建通知
   */
  static async notifyAdmins(
    title: string,
    message: string,
    type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' = 'INFO',
    actionUrl?: string,
    sourceType?: string,
    sourceId?: string
  ) {
    try {
      const adminUsers = await prisma.user.findMany({
        where: {
          role: 'admin',
          is_active: true,
        },
        select: {
          id: true,
        }
      })

      const promises = adminUsers.map(admin =>
        NotificationService.createNotification({
          userId: admin.id,
          title,
          message,
          type,
          actionUrl,
          sourceType,
          sourceId,
        })
      )

      await Promise.all(promises)
      return adminUsers.length
    } catch (error) {
      console.error('为管理员创建通知失败:', error)
      return 0
    }
  }

  /**
   * 为特定用户创建通知
   */
  static async notifyUser(
    userId: number,
    title: string,
    message: string,
    type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' = 'INFO',
    actionUrl?: string,
    sourceType?: string,
    sourceId?: string
  ) {
    try {
      await NotificationService.createNotification({
        userId,
        title,
        message,
        type,
        actionUrl,
        sourceType,
        sourceId,
      })
      return true
    } catch (error) {
      console.error('为用户创建通知失败:', error)
      return false
    }
  }

  /**
   * Webhook验证失败通知
   */
  static async notifyWebhookFailure(
    formId: string,
    errorMessage: string,
    failureId: string
  ) {
    return await this.notifyAdmins(
      'Webhook数据验证失败',
      `表单 ${formId} 的webhook数据验证失败：${errorMessage}`,
      'ERROR',
      `/dashboard/webhook-failures?formId=${formId}`,
      'webhook_failure',
      failureId
    )
  }

  /**
   * 表单配置变更通知
   */
  static async notifyFormConfigChange(
    formId: string,
    formName: string,
    action: 'created' | 'updated' | 'deleted',
    operatorId: number
  ) {
    const actionTexts = {
      created: '创建',
      updated: '更新',
      deleted: '删除'
    }

    const actionText = actionTexts[action]
    const type = action === 'deleted' ? 'WARNING' : 'INFO'

    return await this.notifyAdmins(
      `表单配置${actionText}`,
      `表单 "${formName}" (${formId}) 已被${actionText}`,
      type,
      `/dashboard/forms/config`,
      'form_config',
      formId
    )
  }

  /**
   * 用户登录异常通知
   */
  static async notifyLoginAnomaly(
    username: string,
    ip: string,
    reason: string
  ) {
    return await this.notifyAdmins(
      '用户登录异常',
      `用户 ${username} 从 ${ip} 登录异常：${reason}`,
      'WARNING',
      '/dashboard/system-logs',
      'login_anomaly',
      username
    )
  }

  /**
   * 系统错误通知
   */
  static async notifySystemError(
    errorType: string,
    errorMessage: string,
    context?: any
  ) {
    const contextStr = context ? ` (${JSON.stringify(context)})` : ''
    
    return await this.notifyAdmins(
      '系统错误',
      `${errorType}: ${errorMessage}${contextStr}`,
      'ERROR',
      '/dashboard/system-logs',
      'system_error',
      errorType
    )
  }

  /**
   * 数据导出完成通知
   */
  static async notifyDataExportComplete(
    userId: number,
    fileName: string,
    recordCount: number,
    downloadUrl?: string
  ) {
    return await this.notifyUser(
      userId,
      '数据导出完成',
      `您的数据导出已完成，共 ${recordCount} 条记录。文件：${fileName}`,
      'SUCCESS',
      downloadUrl,
      'data_export',
      fileName
    )
  }

  /**
   * 数据导入完成通知
   */
  static async notifyDataImportComplete(
    userId: number,
    fileName: string,
    successCount: number,
    errorCount: number = 0
  ) {
    const type = errorCount > 0 ? 'WARNING' : 'SUCCESS'
    const message = errorCount > 0 
      ? `数据导入完成，成功 ${successCount} 条，失败 ${errorCount} 条。文件：${fileName}`
      : `数据导入完成，成功导入 ${successCount} 条记录。文件：${fileName}`

    return await this.notifyUser(
      userId,
      '数据导入完成',
      message,
      type,
      undefined,
      'data_import',
      fileName
    )
  }

  /**
   * 系统维护通知
   */
  static async notifySystemMaintenance(
    title: string,
    message: string,
    scheduledTime?: Date
  ) {
    const fullMessage = scheduledTime 
      ? `${message} 计划时间：${scheduledTime.toLocaleString('zh-CN')}`
      : message

    // 通知所有活跃用户
    try {
      const activeUsers = await prisma.user.findMany({
        where: {
          is_active: true,
        },
        select: {
          id: true,
        }
      })

      const promises = activeUsers.map(user =>
        NotificationService.createNotification({
          userId: user.id,
          title,
          message: fullMessage,
          type: 'WARNING',
          sourceType: 'system_maintenance',
          sourceId: scheduledTime?.getTime().toString(),
        })
      )

      await Promise.all(promises)
      return activeUsers.length
    } catch (error) {
      console.error('发送系统维护通知失败:', error)
      return 0
    }
  }
}