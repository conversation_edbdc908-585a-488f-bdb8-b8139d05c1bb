/**
 * 移动端专用CSS工具类和变量系统
 * Mobile-specific CSS utility classes and variable system
 */

/* CSS 自定义属性 - 响应式变量 */
:root {
  /* 断点变量 */
  --breakpoint-xs: 0px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1600px;

  /* 容器最大宽度 */
  --container-xs: 100%;
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;

  /* 移动端专用间距 */
  --mobile-spacing-xs: 4px;
  --mobile-spacing-sm: 8px;
  --mobile-spacing-md: 12px;
  --mobile-spacing-lg: 16px;
  --mobile-spacing-xl: 24px;
  --mobile-spacing-xxl: 32px;

  /* 桌面端间距 */
  --desktop-spacing-xs: 8px;
  --desktop-spacing-sm: 12px;
  --desktop-spacing-md: 16px;
  --desktop-spacing-lg: 24px;
  --desktop-spacing-xl: 32px;
  --desktop-spacing-xxl: 48px;

  /* 触摸目标尺寸 */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 56px;

  /* 移动端字体大小 */
  --mobile-font-xs: 12px;
  --mobile-font-sm: 14px;
  --mobile-font-md: 16px;
  --mobile-font-lg: 18px;
  --mobile-font-xl: 20px;
  --mobile-font-xxl: 24px;

  /* 移动端行高 */
  --mobile-line-height-tight: 1.2;
  --mobile-line-height-normal: 1.4;
  --mobile-line-height-relaxed: 1.6;

  /* 移动端圆角 */
  --mobile-radius-sm: 4px;
  --mobile-radius-md: 6px;
  --mobile-radius-lg: 8px;
  --mobile-radius-xl: 12px;

  /* 移动端阴影 */
  --mobile-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --mobile-shadow-md: 0 1px 3px rgba(0, 0, 0, 0.1);
  --mobile-shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
  --mobile-shadow-xl: 0 10px 15px rgba(0, 0, 0, 0.1);

  /* 移动端动画时长 */
  --mobile-duration-fast: 150ms;
  --mobile-duration-normal: 250ms;
  --mobile-duration-slow: 350ms;

  /* 移动端z-index层级 */
  --mobile-z-dropdown: 1000;
  --mobile-z-sticky: 1010;
  --mobile-z-fixed: 1020;
  --mobile-z-modal-backdrop: 1030;
  --mobile-z-modal: 1040;
  --mobile-z-popover: 1050;
  --mobile-z-tooltip: 1060;
  --mobile-z-toast: 1070;
}

/* 响应式容器 */
.responsive-container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--mobile-spacing-md);
  padding-right: var(--mobile-spacing-md);
}

@media (min-width: 576px) {
  .responsive-container {
    max-width: var(--container-sm);
    padding-left: var(--desktop-spacing-md);
    padding-right: var(--desktop-spacing-md);
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: var(--container-md);
  }
}

@media (min-width: 992px) {
  .responsive-container {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1200px) {
  .responsive-container {
    max-width: var(--container-xl);
  }
}

@media (min-width: 1600px) {
  .responsive-container {
    max-width: var(--container-xxl);
  }
}

/* 响应式网格系统 */
.responsive-grid {
  display: grid;
  gap: var(--mobile-spacing-md);
  grid-template-columns: 1fr;
}

@media (min-width: 576px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--desktop-spacing-md);
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 992px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1200px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 移动端专用工具类 */

/* 显示/隐藏工具类 */
.mobile-show {
  display: block !important;
}

.mobile-hide {
  display: none !important;
}

.tablet-show {
  display: none !important;
}

.tablet-hide {
  display: block !important;
}

.desktop-show {
  display: none !important;
}

.desktop-hide {
  display: block !important;
}

@media (min-width: 576px) and (max-width: 991px) {
  .tablet-show {
    display: block !important;
  }
  
  .tablet-hide {
    display: none !important;
  }
}

@media (min-width: 992px) {
  .mobile-show {
    display: none !important;
  }
  
  .mobile-hide {
    display: block !important;
  }
  
  .desktop-show {
    display: block !important;
  }
  
  .desktop-hide {
    display: none !important;
  }
}

/* 触摸友好的元素 */
.touch-target {
  min-height: var(--touch-target-min) !important;
  min-width: var(--touch-target-min) !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.touch-target-comfortable {
  min-height: var(--touch-target-comfortable) !important;
  min-width: var(--touch-target-comfortable) !important;
}

.touch-target-large {
  min-height: var(--touch-target-large) !important;
  min-width: var(--touch-target-large) !important;
}

/* 触摸反馈效果 */
.touch-feedback {
  transition: transform var(--mobile-duration-fast) ease;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.touch-feedback:active {
  transform: scale(0.98);
}

/* 移动端字体大小工具类 */
.mobile-text-xs {
  font-size: var(--mobile-font-xs) !important;
  line-height: var(--mobile-line-height-normal);
}

.mobile-text-sm {
  font-size: var(--mobile-font-sm) !important;
  line-height: var(--mobile-line-height-normal);
}

.mobile-text-md {
  font-size: var(--mobile-font-md) !important;
  line-height: var(--mobile-line-height-normal);
}

.mobile-text-lg {
  font-size: var(--mobile-font-lg) !important;
  line-height: var(--mobile-line-height-normal);
}

.mobile-text-xl {
  font-size: var(--mobile-font-xl) !important;
  line-height: var(--mobile-line-height-tight);
}

.mobile-text-xxl {
  font-size: var(--mobile-font-xxl) !important;
  line-height: var(--mobile-line-height-tight);
}

/* 移动端间距工具类 */
.mobile-p-xs { padding: var(--mobile-spacing-xs) !important; }
.mobile-p-sm { padding: var(--mobile-spacing-sm) !important; }
.mobile-p-md { padding: var(--mobile-spacing-md) !important; }
.mobile-p-lg { padding: var(--mobile-spacing-lg) !important; }
.mobile-p-xl { padding: var(--mobile-spacing-xl) !important; }
.mobile-p-xxl { padding: var(--mobile-spacing-xxl) !important; }

.mobile-px-xs { padding-left: var(--mobile-spacing-xs) !important; padding-right: var(--mobile-spacing-xs) !important; }
.mobile-px-sm { padding-left: var(--mobile-spacing-sm) !important; padding-right: var(--mobile-spacing-sm) !important; }
.mobile-px-md { padding-left: var(--mobile-spacing-md) !important; padding-right: var(--mobile-spacing-md) !important; }
.mobile-px-lg { padding-left: var(--mobile-spacing-lg) !important; padding-right: var(--mobile-spacing-lg) !important; }
.mobile-px-xl { padding-left: var(--mobile-spacing-xl) !important; padding-right: var(--mobile-spacing-xl) !important; }

.mobile-py-xs { padding-top: var(--mobile-spacing-xs) !important; padding-bottom: var(--mobile-spacing-xs) !important; }
.mobile-py-sm { padding-top: var(--mobile-spacing-sm) !important; padding-bottom: var(--mobile-spacing-sm) !important; }
.mobile-py-md { padding-top: var(--mobile-spacing-md) !important; padding-bottom: var(--mobile-spacing-md) !important; }
.mobile-py-lg { padding-top: var(--mobile-spacing-lg) !important; padding-bottom: var(--mobile-spacing-lg) !important; }
.mobile-py-xl { padding-top: var(--mobile-spacing-xl) !important; padding-bottom: var(--mobile-spacing-xl) !important; }

.mobile-m-xs { margin: var(--mobile-spacing-xs) !important; }
.mobile-m-sm { margin: var(--mobile-spacing-sm) !important; }
.mobile-m-md { margin: var(--mobile-spacing-md) !important; }
.mobile-m-lg { margin: var(--mobile-spacing-lg) !important; }
.mobile-m-xl { margin: var(--mobile-spacing-xl) !important; }
.mobile-m-xxl { margin: var(--mobile-spacing-xxl) !important; }

.mobile-mx-xs { margin-left: var(--mobile-spacing-xs) !important; margin-right: var(--mobile-spacing-xs) !important; }
.mobile-mx-sm { margin-left: var(--mobile-spacing-sm) !important; margin-right: var(--mobile-spacing-sm) !important; }
.mobile-mx-md { margin-left: var(--mobile-spacing-md) !important; margin-right: var(--mobile-spacing-md) !important; }
.mobile-mx-lg { margin-left: var(--mobile-spacing-lg) !important; margin-right: var(--mobile-spacing-lg) !important; }
.mobile-mx-xl { margin-left: var(--mobile-spacing-xl) !important; margin-right: var(--mobile-spacing-xl) !important; }

.mobile-my-xs { margin-top: var(--mobile-spacing-xs) !important; margin-bottom: var(--mobile-spacing-xs) !important; }
.mobile-my-sm { margin-top: var(--mobile-spacing-sm) !important; margin-bottom: var(--mobile-spacing-sm) !important; }
.mobile-my-md { margin-top: var(--mobile-spacing-md) !important; margin-bottom: var(--mobile-spacing-md) !important; }
.mobile-my-lg { margin-top: var(--mobile-spacing-lg) !important; margin-bottom: var(--mobile-spacing-lg) !important; }
.mobile-my-xl { margin-top: var(--mobile-spacing-xl) !important; margin-bottom: var(--mobile-spacing-xl) !important; }

/* 移动端布局工具类 */
.mobile-flex {
  display: flex !important;
}

.mobile-flex-col {
  flex-direction: column !important;
}

.mobile-flex-row {
  flex-direction: row !important;
}

.mobile-flex-wrap {
  flex-wrap: wrap !important;
}

.mobile-flex-nowrap {
  flex-wrap: nowrap !important;
}

.mobile-items-center {
  align-items: center !important;
}

.mobile-items-start {
  align-items: flex-start !important;
}

.mobile-items-end {
  align-items: flex-end !important;
}

.mobile-justify-center {
  justify-content: center !important;
}

.mobile-justify-start {
  justify-content: flex-start !important;
}

.mobile-justify-end {
  justify-content: flex-end !important;
}

.mobile-justify-between {
  justify-content: space-between !important;
}

.mobile-justify-around {
  justify-content: space-around !important;
}

/* 移动端宽度工具类 */
.mobile-w-full {
  width: 100% !important;
}

.mobile-w-auto {
  width: auto !important;
}

.mobile-h-full {
  height: 100% !important;
}

.mobile-h-auto {
  height: auto !important;
}

.mobile-h-screen {
  height: 100vh !important;
}

/* 移动端文本对齐 */
.mobile-text-left {
  text-align: left !important;
}

.mobile-text-center {
  text-align: center !important;
}

.mobile-text-right {
  text-align: right !important;
}

/* 移动端圆角工具类 */
.mobile-rounded-sm {
  border-radius: var(--mobile-radius-sm) !important;
}

.mobile-rounded-md {
  border-radius: var(--mobile-radius-md) !important;
}

.mobile-rounded-lg {
  border-radius: var(--mobile-radius-lg) !important;
}

.mobile-rounded-xl {
  border-radius: var(--mobile-radius-xl) !important;
}

/* 移动端阴影工具类 */
.mobile-shadow-sm {
  box-shadow: var(--mobile-shadow-sm) !important;
}

.mobile-shadow-md {
  box-shadow: var(--mobile-shadow-md) !important;
}

.mobile-shadow-lg {
  box-shadow: var(--mobile-shadow-lg) !important;
}

.mobile-shadow-xl {
  box-shadow: var(--mobile-shadow-xl) !important;
}

/* 移动端卡片样式 */
.mobile-card {
  background: white;
  border-radius: var(--mobile-radius-lg);
  box-shadow: var(--mobile-shadow-md);
  padding: var(--mobile-spacing-lg);
  margin-bottom: var(--mobile-spacing-md);
  border: 1px solid #f0f0f0;
}

.mobile-card-compact {
  padding: var(--mobile-spacing-md);
  margin-bottom: var(--mobile-spacing-sm);
}

.mobile-card-spacious {
  padding: var(--mobile-spacing-xl);
  margin-bottom: var(--mobile-spacing-lg);
}

/* 移动端按钮样式增强 */
.mobile-btn {
  min-height: var(--touch-target-min);
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-lg);
  border-radius: var(--mobile-radius-md);
  font-size: var(--mobile-font-md);
  font-weight: 500;
  transition: all var(--mobile-duration-fast) ease;
  -webkit-tap-highlight-color: transparent;
}

.mobile-btn:active {
  transform: scale(0.98);
}

.mobile-btn-large {
  min-height: var(--touch-target-comfortable);
  padding: var(--mobile-spacing-md) var(--mobile-spacing-xl);
  font-size: var(--mobile-font-lg);
}

.mobile-btn-small {
  min-height: 36px;
  padding: var(--mobile-spacing-xs) var(--mobile-spacing-md);
  font-size: var(--mobile-font-sm);
}

/* 移动端表单元素样式 */
.mobile-input {
  min-height: var(--touch-target-min);
  font-size: var(--mobile-font-md) !important; /* 防止iOS缩放 */
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-md);
  border-radius: var(--mobile-radius-md);
  border: 1px solid #d9d9d9;
  transition: border-color var(--mobile-duration-fast) ease;
}

.mobile-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 移动端列表样式 */
.mobile-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-list-item {
  padding: var(--mobile-spacing-md);
  border-bottom: 1px solid #f0f0f0;
  background: white;
  transition: background-color var(--mobile-duration-fast) ease;
}

.mobile-list-item:last-child {
  border-bottom: none;
}

.mobile-list-item:active {
  background-color: #f5f5f5;
}

.mobile-list-item-clickable {
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

/* 移动端分割线 */
.mobile-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: var(--mobile-spacing-md) 0;
}

.mobile-divider-thick {
  height: 8px;
  background-color: #f5f5f5;
  margin: var(--mobile-spacing-lg) 0;
}

/* 移动端加载状态 */
.mobile-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--mobile-spacing-xl);
  color: #8c8c8c;
  font-size: var(--mobile-font-sm);
}

/* 移动端空状态 */
.mobile-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--mobile-spacing-xxl);
  color: #8c8c8c;
  text-align: center;
}

.mobile-empty-icon {
  font-size: 48px;
  margin-bottom: var(--mobile-spacing-md);
  opacity: 0.5;
}

.mobile-empty-text {
  font-size: var(--mobile-font-md);
  margin-bottom: var(--mobile-spacing-sm);
}

.mobile-empty-description {
  font-size: var(--mobile-font-sm);
  color: #bfbfbf;
}

/* 移动端滚动优化 */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-x: auto;
  overflow-y: visible;
}

.mobile-scroll-y {
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 移动端固定定位 */
.mobile-fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--mobile-z-fixed);
}

.mobile-fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--mobile-z-fixed);
}

/* 移动端粘性定位 */
.mobile-sticky-top {
  position: sticky;
  top: 0;
  z-index: var(--mobile-z-sticky);
}

/* 移动端安全区域适配 */
.mobile-safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.mobile-safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.mobile-safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.mobile-safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.mobile-safe-area-all {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 移动端动画类 */
.mobile-fade-in {
  animation: mobileFadeIn var(--mobile-duration-normal) ease-out;
}

.mobile-slide-up {
  animation: mobileSlideUp var(--mobile-duration-normal) ease-out;
}

.mobile-slide-down {
  animation: mobileSlideDown var(--mobile-duration-normal) ease-out;
}

.mobile-slide-left {
  animation: mobileSlideLeft var(--mobile-duration-normal) ease-out;
}

.mobile-slide-right {
  animation: mobileSlideRight var(--mobile-duration-normal) ease-out;
}

@keyframes mobileFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes mobileSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes mobileSlideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes mobileSlideLeft {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes mobileSlideRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 移动端专用媒体查询 */
@media (max-width: 575px) {
  /* 超小屏幕专用样式 */
  .mobile-xs-only {
    display: block !important;
  }
  
  .mobile-xs-hide {
    display: none !important;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  /* 小屏幕专用样式 */
  .mobile-sm-only {
    display: block !important;
  }
  
  .mobile-sm-hide {
    display: none !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* 中等屏幕专用样式 */
  .mobile-md-only {
    display: block !important;
  }
  
  .mobile-md-hide {
    display: none !important;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-card {
    box-shadow: var(--mobile-shadow-sm);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --mobile-shadow-sm: 0 1px 2px rgba(255, 255, 255, 0.05);
    --mobile-shadow-md: 0 1px 3px rgba(255, 255, 255, 0.1);
    --mobile-shadow-lg: 0 4px 6px rgba(255, 255, 255, 0.1);
    --mobile-shadow-xl: 0 10px 15px rgba(255, 255, 255, 0.1);
  }
  
  .mobile-card {
    background: #1f1f1f;
    border-color: #303030;
  }
  
  .mobile-divider {
    background-color: #303030;
  }
  
  .mobile-divider-thick {
    background-color: #262626;
  }
}

/* 移动端导航专用样式 */

/* 移动端侧边栏抽屉样式 */
.mobile-sidebar-drawer .ant-drawer-body {
  padding: 0 !important;
  background: #001529 !important;
}

.mobile-sidebar-drawer .ant-drawer-header {
  display: none !important;
}

.mobile-sidebar-drawer .ant-drawer-close {
  display: none !important;
}

.mobile-sidebar-drawer .ant-drawer-mask {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.mobile-sidebar-drawer .ant-drawer-content {
  border-radius: 0 12px 12px 0 !important;
  overflow: hidden !important;
}

/* 移动端导航菜单样式 */
.mobile-navigation-menu {
  background: #001529 !important;
  border: none !important;
}

.mobile-navigation-menu .ant-menu-item,
.mobile-navigation-menu .ant-menu-submenu-title {
  height: var(--touch-target-comfortable) !important;
  line-height: var(--touch-target-comfortable) !important;
  margin: 0 !important;
  padding: 0 var(--mobile-spacing-lg) !important;
}

.mobile-navigation-menu .ant-menu-item a,
.mobile-navigation-menu .ant-menu-submenu-title {
  display: flex !important;
  align-items: center !important;
  color: rgba(255, 255, 255, 0.85) !important;
  font-size: var(--mobile-font-md) !important;
}

.mobile-navigation-menu .ant-menu-item:hover,
.mobile-navigation-menu .ant-menu-submenu-title:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.mobile-navigation-menu .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.mobile-navigation-menu .ant-menu-item-icon,
.mobile-navigation-menu .ant-menu-submenu-arrow {
  font-size: 16px !important;
  margin-right: var(--mobile-spacing-sm) !important;
}

/* 移动端头部导航样式 */
.mobile-header {
  padding: 0 var(--mobile-spacing-md) !important;
  height: 56px !important;
  line-height: 56px !important;
}

.mobile-header .ant-layout-header {
  height: 56px !important;
  line-height: 56px !important;
}

/* 移动端面包屑样式 */
.mobile-breadcrumb {
  font-size: var(--mobile-font-sm) !important;
}

.mobile-breadcrumb .ant-breadcrumb-link {
  color: #666 !important;
  font-weight: normal !important;
}

.mobile-breadcrumb .ant-breadcrumb-separator {
  color: #999 !important;
  margin: 0 var(--mobile-spacing-xs) !important;
}

/* 移动端页面标题样式 */
.mobile-page-title {
  font-size: var(--mobile-font-lg) !important;
  font-weight: 600 !important;
  color: #262626 !important;
  margin: 0 !important;
  line-height: 1.2 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* 移动端导航按钮样式 */
.mobile-nav-button {
  width: var(--touch-target-min) !important;
  height: var(--touch-target-min) !important;
  border-radius: var(--mobile-radius-md) !important;
  border: none !important;
  background: transparent !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color var(--mobile-duration-fast) ease !important;
  -webkit-tap-highlight-color: transparent !important;
}

.mobile-nav-button:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

.mobile-nav-button:active {
  background-color: rgba(0, 0, 0, 0.08) !important;
  transform: scale(0.98) !important;
}

.mobile-nav-button .anticon {
  font-size: 18px !important;
  color: #666 !important;
}

/* 移动端用户下拉菜单样式 */
.mobile-user-dropdown .ant-dropdown-menu {
  min-width: 200px !important;
  border-radius: var(--mobile-radius-lg) !important;
  box-shadow: var(--mobile-shadow-xl) !important;
  padding: var(--mobile-spacing-sm) 0 !important;
}

.mobile-user-dropdown .ant-dropdown-menu-item {
  height: var(--touch-target-min) !important;
  line-height: var(--touch-target-min) !important;
  padding: 0 var(--mobile-spacing-lg) !important;
  font-size: var(--mobile-font-md) !important;
}

.mobile-user-dropdown .ant-dropdown-menu-item:hover {
  background-color: #f5f5f5 !important;
}

.mobile-user-dropdown .ant-dropdown-menu-item .anticon {
  margin-right: var(--mobile-spacing-sm) !important;
  font-size: 16px !important;
}

/* 移动端通知下拉样式 */
.mobile-notification .ant-badge {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.mobile-notification .ant-badge-count {
  font-size: 10px !important;
  min-width: 16px !important;
  height: 16px !important;
  line-height: 16px !important;
  padding: 0 4px !important;
}

/* 移动端布局遮罩 */
.mobile-layout-mask {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.45) !important;
  z-index: var(--mobile-z-modal-backdrop) !important;
  animation: mobileFadeIn var(--mobile-duration-fast) ease-out !important;
}

/* 移动端导航动画 */
.mobile-nav-slide-enter {
  transform: translateX(-100%) !important;
}

.mobile-nav-slide-enter-active {
  transform: translateX(0) !important;
  transition: transform var(--mobile-duration-normal) ease-out !important;
}

.mobile-nav-slide-exit {
  transform: translateX(0) !important;
}

.mobile-nav-slide-exit-active {
  transform: translateX(-100%) !important;
  transition: transform var(--mobile-duration-normal) ease-in !important;
}

/* 移动端导航层级指示器 */
.mobile-breadcrumb-indicator {
  display: inline-flex !important;
  align-items: center !important;
  font-size: var(--mobile-font-xs) !important;
  color: #999 !important;
  margin-left: var(--mobile-spacing-xs) !important;
}

.mobile-breadcrumb-indicator::before {
  content: '•••' !important;
  font-weight: bold !important;
}

/* 桌面端面包屑样式 */
.desktop-breadcrumb .ant-breadcrumb-link {
  color: #666 !important;
  transition: color var(--mobile-duration-fast) ease !important;
}

.desktop-breadcrumb .ant-breadcrumb-link:hover {
  color: #1890ff !important;
}

/* 平板端面包屑样式 */
.tablet-breadcrumb .ant-breadcrumb-link {
  color: #666 !important;
  font-size: var(--mobile-font-sm) !important;
}

.tablet-breadcrumb .ant-breadcrumb-separator {
  margin: 0 var(--mobile-spacing-xs) !important;
}

/* 移动端导航手势支持 */
.mobile-nav-swipe-area {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 20px !important;
  height: 100vh !important;
  z-index: var(--mobile-z-fixed) !important;
  background: transparent !important;
  touch-action: pan-x !important;
}

/* 移动端导航增强样式 */
.mobile-navigation-menu .ant-menu-submenu {
  background: transparent !important;
}

.mobile-navigation-menu .ant-menu-submenu-title:active,
.mobile-navigation-menu .ant-menu-item:active {
  background-color: rgba(255, 255, 255, 0.15) !important;
  transform: scale(0.98) !important;
  transition: all var(--mobile-duration-fast) ease !important;
}

.mobile-navigation-menu .ant-menu-submenu-open > .ant-menu-submenu-title {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.mobile-navigation-menu .ant-menu-sub {
  background: rgba(0, 0, 0, 0.3) !important;
}

.mobile-navigation-menu .ant-menu-sub .ant-menu-item {
  padding-left: var(--mobile-spacing-xxl) !important;
  background: transparent !important;
}

.mobile-navigation-menu .ant-menu-sub .ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

.mobile-navigation-menu .ant-menu-sub .ant-menu-item-selected {
  background-color: #1890ff !important;
}

/* 移动端导航关闭按钮增强 */
.mobile-sidebar-drawer .touch-target:active {
  transform: scale(0.95) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 移动端导航状态栏适配 */
@supports (padding-top: env(safe-area-inset-top)) {
  .mobile-header {
    padding-top: env(safe-area-inset-top) !important;
    height: calc(56px + env(safe-area-inset-top)) !important;
  }
  
  .mobile-sidebar-drawer .ant-drawer-body {
    padding-top: env(safe-area-inset-top) !important;
  }
}

/* 移动端横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-header {
    height: 48px !important;
    line-height: 48px !important;
  }
  
  .mobile-nav-button {
    width: 40px !important;
    height: 40px !important;
  }
  
  .mobile-navigation-menu .ant-menu-item,
  .mobile-navigation-menu .ant-menu-submenu-title {
    height: 40px !important;
    line-height: 40px !important;
  }
}

/* 移动端表格增强样式 */

/* 移动端表格容器 */
.mobile-table-container {
  position: relative;
  width: 100%;
}

.mobile-table-enhanced {
  width: 100% !important;
}

.mobile-table-enhanced.mobile-optimized .ant-table-thead > tr > th {
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-xs) !important;
  font-size: var(--mobile-font-sm) !important;
  white-space: nowrap !important;
}

.mobile-table-enhanced.mobile-optimized .ant-table-tbody > tr > td {
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-xs) !important;
  font-size: var(--mobile-font-sm) !important;
}

/* 移动端表格滚动指示器 */
.mobile-table-scroll-indicator {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm) !important;
  background: #fafafa !important;
  border: 1px solid #f0f0f0 !important;
  border-bottom: none !important;
  border-radius: var(--mobile-radius-md) var(--mobile-radius-md) 0 0 !important;
}

.mobile-table-scroll-btn {
  min-width: var(--touch-target-min) !important;
  height: var(--touch-target-min) !important;
  border-radius: var(--mobile-radius-sm) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all var(--mobile-duration-fast) ease !important;
}

.mobile-table-scroll-btn:not(.disabled):active {
  transform: scale(0.95) !important;
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.mobile-table-scroll-btn.disabled {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
}

/* 滚动进度条 */
.mobile-table-scroll-progress {
  flex: 1 !important;
  margin: 0 var(--mobile-spacing-sm) !important;
  height: 4px !important;
}

.mobile-table-scroll-track {
  width: 100% !important;
  height: 100% !important;
  background: #e8e8e8 !important;
  border-radius: 2px !important;
  overflow: hidden !important;
}

.mobile-table-scroll-thumb {
  height: 100% !important;
  background: #1890ff !important;
  border-radius: 2px !important;
  transition: width var(--mobile-duration-fast) ease !important;
  min-width: 10% !important;
}

/* 移动端表格滚动提示 */
.mobile-table-scroll-hint {
  text-align: center !important;
  padding: var(--mobile-spacing-sm) !important;
  background: #f9f9f9 !important;
  border: 1px solid #f0f0f0 !important;
  border-top: none !important;
  border-radius: 0 0 var(--mobile-radius-md) var(--mobile-radius-md) !important;
  animation: mobileSlideUp var(--mobile-duration-normal) ease-out !important;
}

/* 移动端表格分页样式 */
.mobile-table-pagination {
  text-align: center !important;
  margin-top: var(--mobile-spacing-md) !important;
}

.mobile-table-pagination .ant-pagination-item,
.mobile-table-pagination .ant-pagination-prev,
.mobile-table-pagination .ant-pagination-next {
  min-width: var(--touch-target-min) !important;
  height: var(--touch-target-min) !important;
  line-height: calc(var(--touch-target-min) - 2px) !important;
  margin: 0 2px !important;
}

/* 自适应表格样式 */
.adaptive-table-container {
  width: 100%;
}

.adaptive-table-toolbar {
  margin-bottom: var(--mobile-spacing-md) !important;
}

.adaptive-table-wrapper {
  position: relative;
}

.adaptive-card-view {
  width: 100%;
}

.adaptive-table-empty {
  text-align: center !important;
  padding: var(--mobile-spacing-xxl) !important;
  color: #8c8c8c !important;
}

/* 移动端卡片样式 */
.mobile-card {
  margin-bottom: var(--mobile-spacing-md) !important;
  border-radius: var(--mobile-radius-lg) !important;
  box-shadow: var(--mobile-shadow-sm) !important;
  transition: all var(--mobile-duration-fast) ease !important;
}

.mobile-card-clickable {
  cursor: pointer !important;
}

.mobile-card-clickable:active {
  transform: scale(0.98) !important;
  box-shadow: var(--mobile-shadow-md) !important;
}

.mobile-card-header {
  display: flex !important;
  align-items: flex-start !important;
  justify-content: space-between !important;
  margin-bottom: var(--mobile-spacing-sm) !important;
}

.mobile-card-title {
  font-size: var(--mobile-font-md) !important;
  font-weight: 600 !important;
  line-height: var(--mobile-line-height-tight) !important;
  color: #262626 !important;
}

.mobile-card-subtitle {
  font-size: var(--mobile-font-sm) !important;
  color: #8c8c8c !important;
  line-height: var(--mobile-line-height-normal) !important;
}

.mobile-card-description {
  font-size: var(--mobile-font-sm) !important;
  color: #595959 !important;
  line-height: var(--mobile-line-height-relaxed) !important;
}

.mobile-card-actions {
  flex-shrink: 0 !important;
  margin-left: var(--mobile-spacing-sm) !important;
}

.mobile-card-fields {
  margin-top: var(--mobile-spacing-sm) !important;
}

.mobile-card-field {
  padding: var(--mobile-spacing-xs) 0 !important;
}

.mobile-card-field:not(:last-child) {
  border-bottom: 1px solid #f5f5f5 !important;
}

.mobile-card-field-label {
  font-size: var(--mobile-font-xs) !important;
  color: #8c8c8c !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.mobile-card-field-value {
  font-size: var(--mobile-font-sm) !important;
  color: #262626 !important;
  word-break: break-word !important;
}

/* 移动端表格阴影效果 */
.mobile-table-enhanced.scrolled::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 10px !important;
  height: 100% !important;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent) !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

.mobile-table-enhanced:not(.scrolled-to-right)::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  width: 10px !important;
  height: 100% !important;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.1), transparent) !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

/* 移动端表格响应式优化 */
@media (max-width: 575px) {
  .mobile-table-enhanced .ant-table-thead > tr > th {
    padding: var(--mobile-spacing-xs) 4px !important;
    font-size: 11px !important;
  }
  
  .mobile-table-enhanced .ant-table-tbody > tr > td {
    padding: var(--mobile-spacing-xs) 4px !important;
    font-size: 12px !important;
  }
  
  .mobile-card {
    margin-bottom: var(--mobile-spacing-sm) !important;
    padding: var(--mobile-spacing-md) !important;
  }
  
  .mobile-card-title {
    font-size: var(--mobile-font-sm) !important;
  }
  
  .mobile-card-field-label,
  .mobile-card-field-value {
    font-size: var(--mobile-font-xs) !important;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-table-scroll-track {
    height: 3px !important;
  }
  
  .mobile-table-scroll-thumb {
    height: 3px !important;
  }
  
  .mobile-card {
    box-shadow: var(--mobile-shadow-sm) !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .mobile-table-scroll-indicator {
    background: #1f1f1f !important;
    border-color: #303030 !important;
  }
  
  .mobile-table-scroll-hint {
    background: #262626 !important;
    border-color: #303030 !important;
  }
  
  .mobile-table-scroll-track {
    background: #404040 !important;
  }
  
  .mobile-card {
    background: #1f1f1f !important;
    border-color: #303030 !important;
  }
  
  .mobile-card-title {
    color: #ffffff !important;
  }
  
  .mobile-card-subtitle,
  .mobile-card-description {
    color: #bfbfbf !important;
  }
  
  .mobile-card-field-label {
    color: #8c8c8c !important;
  }
  
  .mobile-card-field-value {
    color: #ffffff !important;
  }
  
  .mobile-card-field:not(:last-child) {
    border-bottom-color: #303030 !important;
  }
}

/* 移动端表单专用样式 */

/* 移动端表单容器 */
.mobile-form-container {
  position: relative;
  width: 100%;
}

.mobile-form-container.keyboard-visible {
  transition: all var(--mobile-duration-normal) ease;
}

.mobile-form {
  width: 100%;
}

.mobile-form .ant-form-item {
  margin-bottom: var(--mobile-spacing-lg) !important;
}

.mobile-form .ant-form-item-label {
  padding-bottom: var(--mobile-spacing-xs) !important;
}

.mobile-form .ant-form-item-label > label {
  font-size: var(--mobile-font-md) !important;
  font-weight: 500 !important;
  color: #262626 !important;
}

/* 虚拟键盘占位符 */
.keyboard-placeholder {
  width: 100%;
  background: transparent;
  pointer-events: none;
}

/* 触摸输入组件基础样式 */
.touch-input,
.touch-password,
.touch-textarea,
.touch-input-number,
.touch-select,
.touch-datepicker,
.touch-timepicker {
  transition: all var(--mobile-duration-fast) ease !important;
}

/* 触摸目标尺寸 */
.touch-size-min {
  min-height: var(--touch-target-min) !important;
}

.touch-size-comfortable {
  min-height: var(--touch-target-comfortable) !important;
}

.touch-size-large {
  min-height: var(--touch-target-large) !important;
}

/* 触摸反馈效果 */
.touch-feedback:active {
  transform: scale(0.98) !important;
  transition: transform var(--mobile-duration-fast) ease !important;
}

/* 聚焦状态 */
.touch-focused {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 移动端优化的输入框 */
.mobile-optimized.touch-input .ant-input,
.mobile-optimized.touch-password .ant-input,
.mobile-optimized.touch-textarea .ant-input {
  font-size: 16px !important; /* 防止iOS缩放 */
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
  border-radius: var(--mobile-radius-md) !important;
  line-height: 1.4 !important;
}

.mobile-optimized.touch-textarea .ant-input {
  min-height: var(--touch-target-comfortable) !important;
  resize: vertical !important;
}

/* 移动端数字输入框 */
.mobile-optimized.touch-input-number .ant-input-number {
  width: 100% !important;
  font-size: 16px !important;
}

.mobile-optimized.touch-input-number .ant-input-number-input {
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
  height: var(--touch-target-comfortable) !important;
}

.mobile-optimized.touch-input-number .ant-input-number-handler-wrap {
  width: var(--touch-target-min) !important;
}

.mobile-optimized.touch-input-number .ant-input-number-handler {
  height: calc(var(--touch-target-comfortable) / 2) !important;
  border-radius: 0 var(--mobile-radius-md) 0 0 !important;
}

.mobile-optimized.touch-input-number .ant-input-number-handler:last-child {
  border-radius: 0 0 var(--mobile-radius-md) 0 !important;
}

/* 移动端选择器 */
.mobile-optimized.touch-select .ant-select-selector {
  min-height: var(--touch-target-comfortable) !important;
  padding: var(--mobile-spacing-xs) var(--mobile-spacing-md) !important;
  font-size: 16px !important;
  border-radius: var(--mobile-radius-md) !important;
}

.mobile-optimized.touch-select .ant-select-selection-item {
  line-height: calc(var(--touch-target-comfortable) - var(--mobile-spacing-xs) * 2) !important;
}

.mobile-optimized.touch-select .ant-select-arrow {
  font-size: 16px !important;
  margin-right: var(--mobile-spacing-xs) !important;
}

/* 移动端选择器下拉菜单 */
.touch-select-dropdown.mobile-optimized {
  border-radius: var(--mobile-radius-lg) !important;
  box-shadow: var(--mobile-shadow-xl) !important;
  max-height: 60vh !important;
}

.touch-select-dropdown.mobile-optimized .ant-select-item {
  min-height: var(--touch-target-comfortable) !important;
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
  font-size: var(--mobile-font-md) !important;
  line-height: var(--mobile-line-height-normal) !important;
}

.touch-select-dropdown.mobile-optimized .ant-select-item-option-active {
  background-color: #f0f8ff !important;
}

.touch-select-dropdown.mobile-optimized .ant-select-item-option-selected {
  background-color: #1890ff !important;
  color: white !important;
}

/* 移动端日期选择器 */
.mobile-optimized.touch-datepicker .ant-picker {
  width: 100% !important;
  min-height: var(--touch-target-comfortable) !important;
  font-size: 16px !important;
  border-radius: var(--mobile-radius-md) !important;
}

.mobile-optimized.touch-datepicker .ant-picker-input > input {
  font-size: 16px !important;
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
}

.mobile-optimized.touch-datepicker .ant-picker-suffix {
  font-size: 16px !important;
  margin-right: var(--mobile-spacing-xs) !important;
}

/* 移动端日期选择器弹窗 */
.touch-datepicker-popup.mobile-optimized {
  border-radius: var(--mobile-radius-lg) !important;
  box-shadow: var(--mobile-shadow-xl) !important;
}

.touch-datepicker-popup.mobile-optimized .ant-picker-panel {
  border-radius: var(--mobile-radius-lg) !important;
}

.touch-datepicker-popup.mobile-optimized .ant-picker-cell {
  min-height: var(--touch-target-min) !important;
  min-width: var(--touch-target-min) !important;
}

.touch-datepicker-popup.mobile-optimized .ant-picker-cell-inner {
  min-height: calc(var(--touch-target-min) - 4px) !important;
  min-width: calc(var(--touch-target-min) - 4px) !important;
  line-height: calc(var(--touch-target-min) - 4px) !important;
  border-radius: var(--mobile-radius-sm) !important;
}

.touch-datepicker-popup.mobile-optimized .ant-picker-header button {
  min-height: var(--touch-target-min) !important;
  min-width: var(--touch-target-min) !important;
  border-radius: var(--mobile-radius-sm) !important;
}

/* 移动端时间选择器 */
.mobile-optimized.touch-timepicker .ant-picker {
  width: 100% !important;
  min-height: var(--touch-target-comfortable) !important;
  font-size: 16px !important;
  border-radius: var(--mobile-radius-md) !important;
}

.touch-timepicker-popup.mobile-optimized {
  border-radius: var(--mobile-radius-lg) !important;
  box-shadow: var(--mobile-shadow-xl) !important;
}

.touch-timepicker-popup.mobile-optimized .ant-picker-time-panel-column > li {
  min-height: var(--touch-target-min) !important;
  line-height: var(--touch-target-min) !important;
  padding: 0 var(--mobile-spacing-md) !important;
  font-size: var(--mobile-font-md) !important;
}

/* 移动端开关 */
.mobile-optimized.touch-switch .ant-switch {
  min-width: var(--touch-target-min) !important;
  height: calc(var(--touch-target-min) / 2) !important;
}

.mobile-optimized.touch-switch .ant-switch-handle {
  width: calc(var(--touch-target-min) / 2 - 4px) !important;
  height: calc(var(--touch-target-min) / 2 - 4px) !important;
}

/* 移动端滑块 */
.mobile-optimized.touch-slider .ant-slider-rail {
  height: 6px !important;
  border-radius: 3px !important;
}

.mobile-optimized.touch-slider .ant-slider-track {
  height: 6px !important;
  border-radius: 3px !important;
}

.mobile-optimized.touch-slider .ant-slider-handle {
  width: var(--touch-target-min) !important;
  height: var(--touch-target-min) !important;
  margin-top: calc(-1 * var(--touch-target-min) / 2 + 3px) !important;
  border-width: 3px !important;
}

.mobile-optimized.touch-slider .ant-slider-handle:focus {
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2) !important;
}

/* 移动端表单按钮 */
.mobile-form .ant-btn {
  min-height: var(--touch-target-comfortable) !important;
  border-radius: var(--mobile-radius-md) !important;
  font-size: var(--mobile-font-md) !important;
  font-weight: 500 !important;
  transition: all var(--mobile-duration-fast) ease !important;
}

.mobile-form .ant-btn:active {
  transform: scale(0.98) !important;
}

.mobile-form .ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3) !important;
}

.mobile-form .ant-btn-large {
  min-height: var(--touch-target-large) !important;
  padding: 0 var(--mobile-spacing-xl) !important;
  font-size: var(--mobile-font-lg) !important;
}

/* 移动端表单验证消息 */
.mobile-form .ant-form-item-explain-error {
  font-size: var(--mobile-font-sm) !important;
  margin-top: var(--mobile-spacing-xs) !important;
  padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm) !important;
  background: #fff2f0 !important;
  border: 1px solid #ffccc7 !important;
  border-radius: var(--mobile-radius-sm) !important;
  color: #ff4d4f !important;
}

/* 移动端表单成功消息 */
.mobile-form .ant-form-item-explain-success {
  font-size: var(--mobile-font-sm) !important;
  margin-top: var(--mobile-spacing-xs) !important;
  color: #52c41a !important;
}

/* 移动端表单警告消息 */
.mobile-form .ant-form-item-explain-warning {
  font-size: var(--mobile-font-sm) !important;
  margin-top: var(--mobile-spacing-xs) !important;
  color: #faad14 !important;
}

/* 移动端表单必填标记 */
.mobile-form .ant-form-item-required::before {
  font-size: var(--mobile-font-md) !important;
  margin-right: var(--mobile-spacing-xs) !important;
}

/* 移动端表单分组 */
.mobile-form .ant-form-item-group {
  margin-bottom: var(--mobile-spacing-xl) !important;
}

/* 移动端表单分割线 */
.mobile-form .ant-divider {
  margin: var(--mobile-spacing-xl) 0 !important;
}

/* 移动端表单操作区域 */
.mobile-form-actions {
  padding: var(--mobile-spacing-lg) 0 !important;
  border-top: 1px solid #f0f0f0 !important;
  margin-top: var(--mobile-spacing-xl) !important;
}

.mobile-form-actions .ant-btn {
  width: 100% !important;
  margin-bottom: var(--mobile-spacing-md) !important;
}

.mobile-form-actions .ant-btn:last-child {
  margin-bottom: 0 !important;
}

/* 移动端表单步骤器 */
.mobile-form .ant-steps {
  margin-bottom: var(--mobile-spacing-xl) !important;
}

.mobile-form .ant-steps-item-title {
  font-size: var(--mobile-font-sm) !important;
  line-height: var(--mobile-line-height-tight) !important;
}

.mobile-form .ant-steps-item-description {
  font-size: var(--mobile-font-xs) !important;
  margin-top: var(--mobile-spacing-xs) !important;
}

/* 移动端表单上传组件 */
.mobile-form .ant-upload {
  width: 100% !important;
}

.mobile-form .ant-upload-btn {
  min-height: var(--touch-target-comfortable) !important;
  border-radius: var(--mobile-radius-md) !important;
}

.mobile-form .ant-upload-list-item {
  padding: var(--mobile-spacing-sm) !important;
  border-radius: var(--mobile-radius-md) !important;
}

/* 移动端表单标签页 */
.mobile-form .ant-tabs-tab {
  min-height: var(--touch-target-comfortable) !important;
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
  font-size: var(--mobile-font-md) !important;
}

/* 移动端表单折叠面板 */
.mobile-form .ant-collapse-header {
  min-height: var(--touch-target-comfortable) !important;
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
  font-size: var(--mobile-font-md) !important;
}

/* 移动端表单评分组件 */
.mobile-form .ant-rate-star {
  margin-right: var(--mobile-spacing-sm) !important;
  font-size: var(--mobile-font-xl) !important;
}

/* 移动端表单提及组件 */
.mobile-form .ant-mentions {
  min-height: var(--touch-target-comfortable) !important;
  font-size: 16px !important;
  border-radius: var(--mobile-radius-md) !important;
}

/* 移动端表单级联选择器 */
.mobile-form .ant-cascader-picker {
  min-height: var(--touch-target-comfortable) !important;
  font-size: 16px !important;
  border-radius: var(--mobile-radius-md) !important;
}

/* 移动端表单树选择器 */
.mobile-form .ant-tree-select {
  min-height: var(--touch-target-comfortable) !important;
  font-size: 16px !important;
  border-radius: var(--mobile-radius-md) !important;
}

/* 移动端表单自动完成 */
.mobile-form .ant-auto-complete {
  font-size: 16px !important;
}

.mobile-form .ant-auto-complete .ant-select-selector {
  min-height: var(--touch-target-comfortable) !important;
  border-radius: var(--mobile-radius-md) !important;
}

/* 移动端表单颜色选择器 */
.mobile-form .ant-color-picker-trigger {
  min-height: var(--touch-target-comfortable) !important;
  min-width: var(--touch-target-comfortable) !important;
  border-radius: var(--mobile-radius-md) !important;
}

/* 移动端表单响应式布局 */
@media (max-width: 575px) {
  .mobile-form .ant-form-item {
    margin-bottom: var(--mobile-spacing-md) !important;
  }
  
  .mobile-form .ant-form-item-label {
    padding-bottom: 4px !important;
  }
  
  .mobile-form .ant-form-item-label > label {
    font-size: var(--mobile-font-sm) !important;
  }
  
  .touch-size-comfortable {
    min-height: var(--touch-target-min) !important;
  }
  
  .touch-size-large {
    min-height: var(--touch-target-comfortable) !important;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-form .ant-form-item {
    margin-bottom: var(--mobile-spacing-sm) !important;
  }
  
  .touch-size-comfortable,
  .touch-size-large {
    min-height: var(--touch-target-min) !important;
  }
  
  .mobile-form .ant-btn {
    min-height: var(--touch-target-min) !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .mobile-form .ant-form-item-explain-error {
    background: #2a1215 !important;
    border-color: #a8071a !important;
    color: #ff7875 !important;
  }
  
  .touch-focused {
    border-color: #177ddc !important;
    box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2) !important;
  }
  
  .mobile-form .ant-btn-primary {
    box-shadow: 0 2px 4px rgba(23, 125, 220, 0.3) !important;
  }
}

/* 移动端模态框和弹窗样式 */

/* 全屏模态框样式 */
.fullscreen-modal {
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.fullscreen-modal .ant-modal-content {
  height: 100vh !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.fullscreen-modal .ant-modal-body {
  padding: 0 !important;
  height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 全屏模态框头部 */
.fullscreen-modal-header {
  flex-shrink: 0 !important;
  background: white !important;
  border-bottom: 1px solid #f0f0f0 !important;
  z-index: 10 !important;
}

.fullscreen-modal-header .ant-typography {
  margin: 0 !important;
}

/* 全屏模态框内容 */
.fullscreen-modal-body {
  flex: 1 !important;
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 全屏模态框底部 */
.fullscreen-modal-footer {
  flex-shrink: 0 !important;
  background: white !important;
  border-top: 1px solid #f0f0f0 !important;
  z-index: 10 !important;
}

/* 全屏模态框动画 */
.fullscreen-modal-slide-up {
  animation: fullscreenSlideUp var(--mobile-duration-normal) ease-out !important;
}

.fullscreen-modal-slide-down {
  animation: fullscreenSlideDown var(--mobile-duration-normal) ease-out !important;
}

.fullscreen-modal-slide-left {
  animation: fullscreenSlideLeft var(--mobile-duration-normal) ease-out !important;
}

.fullscreen-modal-slide-right {
  animation: fullscreenSlideRight var(--mobile-duration-normal) ease-out !important;
}

.fullscreen-modal-fade {
  animation: fullscreenFade var(--mobile-duration-normal) ease-out !important;
}

.fullscreen-modal-zoom {
  animation: fullscreenZoom var(--mobile-duration-normal) ease-out !important;
}

@keyframes fullscreenSlideUp {
  from {
    transform: translateY(100%) !important;
  }
  to {
    transform: translateY(0) !important;
  }
}

@keyframes fullscreenSlideDown {
  from {
    transform: translateY(-100%) !important;
  }
  to {
    transform: translateY(0) !important;
  }
}

@keyframes fullscreenSlideLeft {
  from {
    transform: translateX(100%) !important;
  }
  to {
    transform: translateX(0) !important;
  }
}

@keyframes fullscreenSlideRight {
  from {
    transform: translateX(-100%) !important;
  }
  to {
    transform: translateX(0) !important;
  }
}

@keyframes fullscreenFade {
  from {
    opacity: 0 !important;
  }
  to {
    opacity: 1 !important;
  }
}

@keyframes fullscreenZoom {
  from {
    transform: scale(0.8) !important;
    opacity: 0 !important;
  }
  to {
    transform: scale(1) !important;
    opacity: 1 !important;
  }
}

/* 桌面端全屏模态框 */
.fullscreen-modal-desktop .ant-modal-content {
  border-radius: var(--mobile-radius-lg) !important;
  overflow: hidden !important;
}

/* 底部抽屉样式 */
.bottom-drawer {
  border-radius: var(--mobile-radius-lg) var(--mobile-radius-lg) 0 0 !important;
}

.bottom-drawer .ant-drawer-content {
  border-radius: var(--mobile-radius-lg) var(--mobile-radius-lg) 0 0 !important;
  overflow: hidden !important;
}

.bottom-drawer .ant-drawer-body {
  padding: 0 !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 底部抽屉拖拽状态 */
.bottom-drawer.dragging {
  transition: none !important;
}

.bottom-drawer.dragging .ant-drawer-content {
  transition: none !important;
}

/* 底部抽屉手柄 */
.bottom-drawer-handle {
  cursor: grab !important;
  user-select: none !important;
  -webkit-user-select: none !important;
}

.bottom-drawer-handle:active {
  cursor: grabbing !important;
}

.bottom-drawer-handle div {
  transition: background-color var(--mobile-duration-fast) ease !important;
}

.bottom-drawer-handle:hover div,
.bottom-drawer-handle:active div {
  background-color: #bfbfbf !important;
}

/* 底部抽屉头部 */
.bottom-drawer-header {
  flex-shrink: 0 !important;
  background: white !important;
}

.bottom-drawer-header .ant-typography {
  margin: 0 !important;
}

/* 底部抽屉内容 */
.bottom-drawer-body {
  flex: 1 !important;
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 移动端优化的模态框 */
.mobile-optimized-modal.mobile-modal {
  top: auto !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.mobile-optimized-modal.mobile-modal .ant-modal-content {
  border-radius: var(--mobile-radius-lg) var(--mobile-radius-lg) 0 0 !important;
  margin: 0 !important;
  max-height: 90vh !important;
  overflow: hidden !important;
}

.mobile-optimized-modal.mobile-modal .ant-modal-header {
  border-radius: var(--mobile-radius-lg) var(--mobile-radius-lg) 0 0 !important;
  padding: var(--mobile-spacing-lg) !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.mobile-optimized-modal.mobile-modal .ant-modal-title {
  font-size: var(--mobile-font-lg) !important;
  font-weight: 600 !important;
  line-height: var(--mobile-line-height-tight) !important;
}

.mobile-optimized-modal.mobile-modal .ant-modal-close {
  top: var(--mobile-spacing-lg) !important;
  right: var(--mobile-spacing-lg) !important;
  width: var(--touch-target-min) !important;
  height: var(--touch-target-min) !important;
  border-radius: var(--mobile-radius-sm) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.mobile-optimized-modal.mobile-modal .ant-modal-close:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

.mobile-optimized-modal.mobile-modal .ant-modal-close:active {
  background-color: rgba(0, 0, 0, 0.08) !important;
  transform: scale(0.95) !important;
}

.mobile-optimized-modal.mobile-modal .ant-modal-body {
  padding: var(--mobile-spacing-lg) !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

.mobile-optimized-modal.mobile-modal .ant-modal-footer {
  padding: var(--mobile-spacing-md) var(--mobile-spacing-lg) var(--mobile-spacing-lg) !important;
  border-top: 1px solid #f0f0f0 !important;
}

.mobile-optimized-modal.mobile-modal .ant-modal-footer .ant-btn {
  min-height: var(--touch-target-comfortable) !important;
  border-radius: var(--mobile-radius-md) !important;
  font-size: var(--mobile-font-md) !important;
  font-weight: 500 !important;
}

/* 平板端模态框 */
.mobile-optimized-modal.tablet-modal .ant-modal-content {
  border-radius: var(--mobile-radius-lg) !important;
  overflow: hidden !important;
}

.mobile-optimized-modal.tablet-modal .ant-modal-header {
  padding: var(--desktop-spacing-lg) !important;
}

.mobile-optimized-modal.tablet-modal .ant-modal-body {
  padding: var(--desktop-spacing-lg) !important;
}

.mobile-optimized-modal.tablet-modal .ant-modal-footer {
  padding: var(--desktop-spacing-md) var(--desktop-spacing-lg) var(--desktop-spacing-lg) !important;
}

/* 移动端确认对话框 */
.ant-modal-confirm.mobile-optimized {
  top: auto !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.ant-modal-confirm.mobile-optimized .ant-modal-content {
  border-radius: var(--mobile-radius-lg) var(--mobile-radius-lg) 0 0 !important;
  padding: var(--mobile-spacing-xl) var(--mobile-spacing-lg) var(--mobile-spacing-lg) !important;
}

.ant-modal-confirm.mobile-optimized .ant-modal-confirm-title {
  font-size: var(--mobile-font-lg) !important;
  font-weight: 600 !important;
  margin-bottom: var(--mobile-spacing-md) !important;
}

.ant-modal-confirm.mobile-optimized .ant-modal-confirm-content {
  font-size: var(--mobile-font-md) !important;
  line-height: var(--mobile-line-height-relaxed) !important;
  margin-bottom: var(--mobile-spacing-lg) !important;
}

.ant-modal-confirm.mobile-optimized .ant-modal-confirm-btns {
  display: flex !important;
  flex-direction: column-reverse !important;
  gap: var(--mobile-spacing-sm) !important;
}

.ant-modal-confirm.mobile-optimized .ant-modal-confirm-btns .ant-btn {
  width: 100% !important;
  min-height: var(--touch-target-comfortable) !important;
  border-radius: var(--mobile-radius-md) !important;
  font-size: var(--mobile-font-md) !important;
  font-weight: 500 !important;
}

/* 抽屉模态框样式 */
.drawer-modal {
  margin: 0 !important;
  padding: 0 !important;
}

.drawer-modal.drawer-right {
  margin-left: auto !important;
  margin-right: 0 !important;
}

.drawer-modal.drawer-left {
  margin-left: 0 !important;
  margin-right: auto !important;
}

.drawer-modal.drawer-top {
  margin-top: 0 !important;
  margin-bottom: auto !important;
}

.drawer-modal.drawer-bottom {
  margin-top: auto !important;
  margin-bottom: 0 !important;
}

.drawer-modal .ant-modal-content {
  height: 100% !important;
  border-radius: 0 !important;
}

/* 移动端模态框遮罩优化 */
.mobile-optimized-modal .ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* 触摸友好的按钮 */
.mobile-optimized-modal .touch-target {
  min-height: var(--touch-target-min) !important;
  min-width: var(--touch-target-min) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all var(--mobile-duration-fast) ease !important;
}

.mobile-optimized-modal .touch-target:active {
  transform: scale(0.95) !important;
}

/* 移动端模态框滚动优化 */
.mobile-optimized-modal .ant-modal-body {
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* 移动端模态框安全区域适配 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .mobile-optimized-modal.mobile-modal .ant-modal-footer {
    padding-bottom: calc(var(--mobile-spacing-lg) + env(safe-area-inset-bottom)) !important;
  }
  
  .fullscreen-modal-footer {
    padding-bottom: calc(var(--mobile-spacing-lg) + env(safe-area-inset-bottom)) !important;
  }
  
  .bottom-drawer-body {
    padding-bottom: env(safe-area-inset-bottom) !important;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-optimized-modal.mobile-modal .ant-modal-content {
    max-height: 95vh !important;
  }
  
  .mobile-optimized-modal.mobile-modal .ant-modal-body {
    max-height: 50vh !important;
  }
  
  .fullscreen-modal-header,
  .bottom-drawer-header {
    padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
  }
  
  .fullscreen-modal-footer,
  .bottom-drawer-footer {
    padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .fullscreen-modal-header,
  .bottom-drawer-header,
  .mobile-optimized-modal .ant-modal-header {
    background: #1f1f1f !important;
    border-color: #303030 !important;
  }
  
  .fullscreen-modal-footer,
  .bottom-drawer-footer,
  .mobile-optimized-modal .ant-modal-footer {
    background: #1f1f1f !important;
    border-color: #303030 !important;
  }
  
  .fullscreen-modal-body,
  .bottom-drawer-body,
  .mobile-optimized-modal .ant-modal-body {
    background: #1f1f1f !important;
  }
  
  .mobile-optimized-modal .ant-modal-content {
    background: #1f1f1f !important;
  }
  
  .mobile-optimized-modal .ant-modal-title {
    color: #ffffff !important;
  }
  
  .mobile-optimized-modal .ant-modal-mask {
    background-color: rgba(0, 0, 0, 0.8) !important;
  }
}

/* 触摸交互增强样式 */

/* 触摸反馈按钮样式 */
.touch-feedback-button {
  position: relative !important;
  overflow: hidden !important;
  -webkit-tap-highlight-color: transparent !important;
  user-select: none !important;
  -webkit-user-select: none !important;
}

.touch-feedback-button::before {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 0 !important;
  height: 0 !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.3) !important;
  transform: translate(-50%, -50%) !important;
  transition: width 0.3s ease, height 0.3s ease !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

.touch-feedback-button.touch-pressed::before {
  width: 200% !important;
  height: 200% !important;
}

/* 触摸反馈强度 */
.touch-feedback-light::before {
  background: rgba(255, 255, 255, 0.1) !important;
}

.touch-feedback-medium::before {
  background: rgba(255, 255, 255, 0.2) !important;
}

.touch-feedback-strong::before {
  background: rgba(255, 255, 255, 0.3) !important;
}

/* 触摸按下状态 */
.touch-pressed {
  transform: scale(0.98) !important;
  transition: transform var(--mobile-duration-fast) ease !important;
}

/* 滑动操作组件样式 */
.swipe-action-container {
  position: relative !important;
  overflow: hidden !important;
  background: white !important;
}

.swipe-action-content {
  position: relative !important;
  z-index: 1 !important;
  background: white !important;
  width: 100% !important;
  transition: transform 0.3s ease-out !important;
}

.swipe-actions {
  position: absolute !important;
  top: 0 !important;
  height: 100% !important;
  display: flex !important;
  align-items: stretch !important;
  z-index: 0 !important;
}

.swipe-actions-left {
  left: 0 !important;
  flex-direction: row !important;
}

.swipe-actions-right {
  right: 0 !important;
  flex-direction: row-reverse !important;
}

.swipe-action-button {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  border-radius: 0 !important;
  color: white !important;
  font-size: var(--mobile-font-xs) !important;
  font-weight: 500 !important;
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-xs) !important;
  cursor: pointer !important;
  transition: background-color var(--mobile-duration-fast) ease !important;
  min-width: 60px !important;
  -webkit-tap-highlight-color: transparent !important;
}

.swipe-action-button:active {
  filter: brightness(0.9) !important;
  transform: scale(0.95) !important;
}

.swipe-action-button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

.swipe-action-icon {
  font-size: 16px !important;
  margin-bottom: var(--mobile-spacing-xs) !important;
}

.swipe-action-text {
  font-size: 11px !important;
  line-height: 1.2 !important;
  text-align: center !important;
  word-break: break-word !important;
}

/* 长按菜单样式 */
.long-press-menu-container {
  position: relative !important;
  display: inline-block !important;
}

.long-press-menu-container.long-pressing {
  transform: scale(0.98) !important;
  transition: transform var(--mobile-duration-fast) ease !important;
}

.long-press-content {
  width: 100% !important;
  height: 100% !important;
}

.long-press-menu {
  border-radius: var(--mobile-radius-lg) !important;
  box-shadow: var(--mobile-shadow-xl) !important;
  overflow: hidden !important;
}

.long-press-menu .ant-menu-item {
  min-height: var(--touch-target-comfortable) !important;
  line-height: var(--touch-target-comfortable) !important;
  padding: 0 var(--mobile-spacing-lg) !important;
  font-size: var(--mobile-font-md) !important;
  display: flex !important;
  align-items: center !important;
}

.long-press-menu .ant-menu-item:hover {
  background-color: #f0f8ff !important;
}

.long-press-menu .ant-menu-item:active {
  background-color: #e6f4ff !important;
  transform: scale(0.98) !important;
}

.long-press-menu .ant-menu-item-icon {
  margin-right: var(--mobile-spacing-sm) !important;
  font-size: 16px !important;
}

.long-press-dropdown {
  z-index: var(--mobile-z-popover) !important;
}

/* 触摸手势指示器 */
.touch-gesture-indicator {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  background: rgba(0, 0, 0, 0.6) !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 18px !important;
  pointer-events: none !important;
  z-index: 1000 !important;
  animation: touchGestureIndicator 0.6s ease-out !important;
}

@keyframes touchGestureIndicator {
  0% {
    opacity: 0 !important;
    transform: translate(-50%, -50%) scale(0.5) !important;
  }
  50% {
    opacity: 1 !important;
    transform: translate(-50%, -50%) scale(1.1) !important;
  }
  100% {
    opacity: 0 !important;
    transform: translate(-50%, -50%) scale(1) !important;
  }
}

/* 触摸涟漪效果 */
.touch-ripple {
  position: absolute !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.6) !important;
  transform: scale(0) !important;
  animation: touchRipple 0.6s linear !important;
  pointer-events: none !important;
}

@keyframes touchRipple {
  to {
    transform: scale(4) !important;
    opacity: 0 !important;
  }
}

/* 触摸拖拽样式 */
.touch-dragging {
  cursor: grabbing !important;
  cursor: -webkit-grabbing !important;
  user-select: none !important;
  -webkit-user-select: none !important;
}

.touch-draggable {
  cursor: grab !important;
  cursor: -webkit-grab !important;
  transition: transform var(--mobile-duration-fast) ease !important;
}

.touch-draggable:active {
  cursor: grabbing !important;
  cursor: -webkit-grabbing !important;
}

/* 触摸缩放样式 */
.touch-pinchable {
  touch-action: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
}

.touch-pinching {
  transition: none !important;
}

/* 触摸旋转样式 */
.touch-rotatable {
  touch-action: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
}

.touch-rotating {
  transition: none !important;
}

/* 双击高亮效果 */
.double-tap-highlight {
  animation: doubleTapHighlight 0.3s ease-out !important;
}

@keyframes doubleTapHighlight {
  0% {
    background-color: transparent !important;
  }
  50% {
    background-color: rgba(24, 144, 255, 0.1) !important;
  }
  100% {
    background-color: transparent !important;
  }
}

/* 长按进度指示器 */
.long-press-progress {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  border-radius: inherit !important;
  overflow: hidden !important;
  pointer-events: none !important;
}

.long-press-progress::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.2) 0%, transparent 100%) !important;
  transform: translateX(-100%) !important;
  animation: longPressProgress var(--long-press-duration, 500ms) linear !important;
}

@keyframes longPressProgress {
  to {
    transform: translateX(0) !important;
  }
}

/* 触摸状态指示器 */
.touch-state-indicator {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm) !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  border-radius: var(--mobile-radius-md) !important;
  font-size: var(--mobile-font-xs) !important;
  z-index: 9999 !important;
  pointer-events: none !important;
  opacity: 0 !important;
  transition: opacity var(--mobile-duration-fast) ease !important;
}

.touch-state-indicator.active {
  opacity: 1 !important;
}

/* 触摸轨迹显示 */
.touch-trail {
  position: absolute !important;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50% !important;
  background: rgba(24, 144, 255, 0.6) !important;
  pointer-events: none !important;
  animation: touchTrail 1s ease-out forwards !important;
}

@keyframes touchTrail {
  0% {
    opacity: 1 !important;
    transform: scale(1) !important;
  }
  100% {
    opacity: 0 !important;
    transform: scale(0.5) !important;
  }
}

/* 移动端卡片触摸增强 */
.mobile-card.touch-enhanced {
  transition: all var(--mobile-duration-fast) ease !important;
  cursor: pointer !important;
}

.mobile-card.touch-enhanced:active {
  transform: scale(0.98) !important;
  box-shadow: var(--mobile-shadow-lg) !important;
}

.mobile-card.touch-enhanced:hover {
  box-shadow: var(--mobile-shadow-md) !important;
}

/* 触摸友好的列表项 */
.touch-list-item {
  min-height: var(--touch-target-comfortable) !important;
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
  display: flex !important;
  align-items: center !important;
  transition: background-color var(--mobile-duration-fast) ease !important;
  cursor: pointer !important;
  -webkit-tap-highlight-color: transparent !important;
}

.touch-list-item:active {
  background-color: rgba(0, 0, 0, 0.04) !important;
  transform: scale(0.99) !important;
}

.touch-list-item:hover {
  background-color: rgba(0, 0, 0, 0.02) !important;
}

/* 触摸友好的开关 */
.touch-switch {
  min-width: var(--touch-target-min) !important;
  min-height: var(--touch-target-min) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 触摸友好的滑块 */
.touch-slider .ant-slider-handle {
  width: var(--touch-target-min) !important;
  height: var(--touch-target-min) !important;
  border-width: 3px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.touch-slider .ant-slider-handle:focus {
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2) !important;
}

/* 触摸友好的标签页 */
.touch-tabs .ant-tabs-tab {
  min-height: var(--touch-target-comfortable) !important;
  padding: var(--mobile-spacing-sm) var(--mobile-spacing-md) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 触摸友好的步骤条 */
.touch-steps .ant-steps-item-icon {
  width: var(--touch-target-min) !important;
  height: var(--touch-target-min) !important;
  line-height: var(--touch-target-min) !important;
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .mobile-fade-in,
  .mobile-slide-up,
  .mobile-slide-down,
  .mobile-slide-left,
  .mobile-slide-right,
  .touch-feedback,
  .mobile-nav-slide-enter-active,
  .mobile-nav-slide-exit-active,
  .mobile-table-scroll-hint,
  .mobile-card,
  .mobile-table-scroll-btn,
  .mobile-table-scroll-thumb,
  .touch-input,
  .touch-password,
  .touch-textarea,
  .touch-input-number,
  .touch-select,
  .touch-datepicker,
  .touch-timepicker,
  .mobile-form .ant-btn,
  .fullscreen-modal-slide-up,
  .fullscreen-modal-slide-down,
  .fullscreen-modal-slide-left,
  .fullscreen-modal-slide-right,
  .fullscreen-modal-fade,
  .fullscreen-modal-zoom,
  .bottom-drawer-handle div,
  .mobile-optimized-modal .touch-target,
  .touch-feedback-button,
  .touch-pressed,
  .swipe-action-content,
  .swipe-action-button,
  .long-press-menu-container,
  .touch-gesture-indicator,
  .touch-ripple,
  .touch-draggable,
  .double-tap-highlight,
  .long-press-progress,
  .touch-state-indicator,
  .touch-trail,
  .mobile-card.touch-enhanced,
  .touch-list-item {
    animation: none !important;
    transition: none !important;
  }
}
/*
 触摸反馈和涟漪效果动画 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 触摸按钮增强样式 */
.touch-button {
  position: relative;
  overflow: hidden;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.touch-button.pressed {
  transform: scale(0.95);
}

.touch-button .ripple-effect {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  pointer-events: none;
  animation: ripple 0.6s linear;
}

/* 触摸卡片增强样式 */
.touch-card {
  position: relative;
  overflow: hidden;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  transition: all 0.15s ease-in-out;
}

.touch-card.pressed {
  transform: scale(0.98);
}

.touch-card .ripple-effect {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  pointer-events: none;
  animation: ripple 0.6s linear;
}

/* 触摸输入框增强样式 */
.touch-input {
  transition: all 0.15s ease-in-out;
}

.touch-input.touch-focused {
  transform: scale(1.02);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.touch-input.touch-size-min {
  min-height: 40px;
}

.touch-input.touch-size-comfortable {
  min-height: 48px;
}

.touch-input.touch-size-large {
  min-height: 56px;
}

/* 长按菜单样式 */
.long-press-menu {
  position: absolute;
  z-index: 1050;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #f0f0f0;
  overflow: hidden;
  animation: mobileSlideUp 0.2s ease-out;
}

.long-press-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  min-height: 44px;
  font-size: 14px;
  color: #262626;
  transition: background-color 0.15s ease;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.long-press-menu-item:hover {
  background-color: #f5f5f5;
}

.long-press-menu-item:active {
  background-color: #e6f7ff;
}

.long-press-menu-item.danger {
  color: #ff4d4f;
}

.long-press-menu-item.danger:hover {
  background-color: #fff2f0;
}

.long-press-menu-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.long-press-menu-item.disabled:hover {
  background-color: transparent;
}

/* 滑动操作样式 */
.swipe-action {
  position: relative;
  overflow: hidden;
  background: white;
}

.swipe-action-content {
  position: relative;
  z-index: 10;
  background: white;
  transition: transform 0.3s ease;
}

.swipe-action-buttons {
  position: absolute;
  top: 0;
  height: 100%;
  display: flex;
  align-items: stretch;
}

.swipe-action-buttons.left {
  left: 0;
}

.swipe-action-buttons.right {
  right: 0;
}

.swipe-action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  padding: 8px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: opacity 0.15s ease;
}

.swipe-action-button:hover {
  opacity: 0.9;
}

.swipe-action-button:active {
  opacity: 0.75;
}

.swipe-action-button.primary {
  background-color: #1890ff;
}

.swipe-action-button.danger {
  background-color: #ff4d4f;
}

.swipe-action-button.warning {
  background-color: #faad14;
}

.swipe-action-button.success {
  background-color: #52c41a;
}

/* 触摸手势反馈 */
.touch-gesture-feedback {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  z-index: 9999;
  pointer-events: none;
  animation: touchFeedback 0.5s ease-out;
}

@keyframes touchFeedback {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 触摸目标尺寸增强 */
.touch-size-small {
  min-height: 40px;
  min-width: 40px;
}

.touch-size-medium {
  min-height: 48px;
  min-width: 48px;
}

.touch-size-large {
  min-height: 56px;
  min-width: 56px;
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
  .touch-size-small {
    min-height: 44px;
    min-width: 44px;
  }
  
  .touch-size-medium {
    min-height: 48px;
    min-width: 48px;
  }
  
  .touch-size-large {
    min-height: 56px;
    min-width: 56px;
  }
}

/* 触摸反馈强度 */
.touch-feedback-light:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.touch-feedback-medium:active {
  transform: scale(0.95);
  opacity: 0.85;
}

.touch-feedback-strong:active {
  transform: scale(0.92);
  opacity: 0.8;
}

/* 防止意外选择 */
.touch-no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* 触摸滚动优化 */
.touch-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 触摸拖拽样式 */
.touch-draggable {
  cursor: grab;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.touch-draggable:active {
  cursor: grabbing;
}

.touch-dragging {
  opacity: 0.8;
  transform: scale(1.05);
  z-index: 1000;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}
/* 性能优化相关样
式 */

/* 硬件加速基础类 */
.performance-optimized {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: auto;
}

/* 虚拟滚动优化 */
.virtual-scroll-enabled {
  contain: layout style paint;
  overflow: hidden;
}

.virtual-scroll-enabled .virtual-list-container {
  will-change: scroll-position;
  transform: translateZ(0);
  contain: strict;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.virtual-list-item {
  contain: layout style paint;
  will-change: transform;
}

/* 懒加载图片优化 */
.lazy-loading-enabled img[data-src] {
  opacity: 0;
  transition: opacity 0.3s ease;
  background: #f0f0f0;
  image-rendering: optimizeSpeed;
}

.lazy-loading-enabled img[data-src].loaded {
  opacity: 1;
}

.lazy-image-wrapper {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}

.lazy-image-wrapper img {
  transition: opacity 0.3s ease;
  transform: translateZ(0);
}

/* 隐藏复杂元素优化 */
[data-hidden="true"] {
  visibility: hidden;
  pointer-events: none;
  contain: layout style paint;
}

[data-complex-render] {
  contain: layout style paint;
}

/* 骨架屏动画优化 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  will-change: background-position;
}

/* 移动端性能优化增强 */
@media (max-width: 768px) {
  /* 优化动画性能 */
  .mobile-animate {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  
  /* 优化滚动性能 */
  .mobile-scroll-container {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    contain: layout style paint;
  }
  
  /* 优化字体渲染 */
  .mobile-text-optimize {
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* 优化图片渲染 */
  .mobile-image-optimize {
    image-rendering: optimizeSpeed;
    transform: translateZ(0);
  }
  
  /* 减少重绘的元素 */
  .mobile-no-repaint {
    contain: layout style paint;
    will-change: auto;
  }
  
  /* 触摸反馈优化 */
  .mobile-touch-feedback {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    transition: transform 0.1s ease;
  }
  
  .mobile-touch-feedback:active {
    transform: scale(0.98);
  }
  
  /* 表格性能优化 */
  .ant-table-tbody {
    contain: layout style paint;
  }
  
  .ant-table-row {
    will-change: auto;
  }
  
  /* 表单性能优化 */
  .ant-form {
    contain: layout style;
  }
  
  /* 卡片性能优化 */
  .ant-card {
    contain: layout style paint;
  }
  
  /* 列表性能优化 */
  .ant-list {
    contain: layout style;
  }
  
  .ant-list-item {
    contain: layout style paint;
  }
  
  /* 分页性能优化 */
  .ant-pagination {
    contain: layout style;
  }
  
  /* 模态框性能优化 */
  .ant-modal {
    transform: translateZ(0);
  }
  
  .ant-modal-content {
    will-change: transform;
  }
  
  /* 抽屉性能优化 */
  .ant-drawer-content-wrapper {
    will-change: transform;
  }
  
  /* 消息提示性能优化 */
  .ant-message-notice {
    transform: translateZ(0);
  }
  
  .ant-notification-notice {
    transform: translateZ(0);
  }
}

/* 性能监控面板 */
.performance-monitor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.2;
  pointer-events: none;
  user-select: none;
  backdrop-filter: blur(4px);
  contain: layout style paint;
}

/* 移动端优化的加载状态 */
.mobile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  contain: layout style paint;
}

.mobile-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  will-change: transform;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动端错误边界优化 */
.mobile-error-boundary {
  padding: var(--mobile-spacing-lg);
  text-align: center;
  contain: layout style paint;
}

/* 移动端无障碍优化 */
@media (max-width: 768px) {
  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .ant-btn {
      border-width: 2px;
    }
    
    .ant-card {
      border-width: 2px;
    }
  }
  
  /* 减少动画模式支持 */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
    
    .skeleton-loading {
      animation: none !important;
      background: #f0f0f0 !important;
    }
  }
  
  /* 焦点可见性优化 */
  .mobile-focus-visible:focus-visible {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
  }
}

/* 首屏加载优化 */
.first-screen-optimized {
  contain: layout style paint;
}

.first-screen-optimized img {
  loading: eager;
}

.below-fold {
  contain: layout style paint;
}

.below-fold img {
  loading: lazy;
}

/* 页面切换优化 */
.page-transition-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms ease, transform 300ms ease;
}

.page-transition-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateX(-20px);
  transition: opacity 300ms ease, transform 300ms ease;
}

/* 资源预加载优化 */
.preload-critical {
  font-display: swap;
}

/* 内存优化 */
.memory-optimized {
  contain: layout style paint;
}

.memory-optimized * {
  will-change: auto;
}

/* 长列表优化 */
.long-list-optimized {
  contain: layout style paint;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.long-list-optimized .list-item {
  contain: layout style paint;
}

/* 图片画廊优化 */
.image-gallery {
  contain: layout style;
}

.image-gallery-item {
  contain: layout style paint;
  will-change: transform;
}

.image-gallery-item:hover {
  will-change: transform;
}

/* 响应式图片优化 */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  image-rendering: optimizeSpeed;
}

@media (min-width: 768px) {
  .responsive-image {
    image-rendering: auto;
  }
}

/* 虚拟表格优化 */
.virtual-table {
  contain: layout style paint;
}

.virtual-table .ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
}

.virtual-table .ant-table-tbody {
  contain: layout style paint;
}

/* 移动端表单优化增强 */
@media (max-width: 768px) {
  .mobile-form-optimized .ant-form-item {
    contain: layout style;
  }
  
  .mobile-form-optimized .ant-input,
  .mobile-form-optimized .ant-select-selector {
    will-change: auto;
    transform: translateZ(0);
  }
  
  .mobile-form-optimized .ant-btn {
    will-change: transform;
  }
}

/* 移动端导航优化增强 */
@media (max-width: 768px) {
  .mobile-nav-optimized .ant-layout-sider {
    will-change: transform;
    contain: layout style paint;
  }
  
  .mobile-nav-optimized .mobile-sider-mask {
    backdrop-filter: blur(2px);
    will-change: opacity;
  }
  
  .mobile-nav-optimized .ant-layout-header {
    contain: layout style paint;
  }
}

/* 移动端模态框优化增强 */
@media (max-width: 768px) {
  .mobile-modal-optimized .ant-modal-mask {
    backdrop-filter: blur(2px);
    will-change: opacity;
  }
  
  .mobile-modal-optimized .ant-modal-wrap {
    contain: layout style;
  }
  
  .mobile-modal-optimized .ant-modal-body {
    contain: layout style paint;
    max-height: 70vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* 移动端抽屉优化增强 */
@media (max-width: 768px) {
  .mobile-drawer-optimized .ant-drawer-mask {
    backdrop-filter: blur(2px);
    will-change: opacity;
  }
  
  .mobile-drawer-optimized .ant-drawer-content-wrapper {
    will-change: transform;
    contain: layout style paint;
  }
  
  .mobile-drawer-optimized .ant-drawer-body {
    contain: layout style paint;
    -webkit-overflow-scrolling: touch;
  }
}