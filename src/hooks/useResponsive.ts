/**
 * useResponsive Hook - 响应式检测Hook
 * 用于检测设备类型和屏幕尺寸变化
 */

import { useState, useEffect, useCallback } from 'react'
import { 
  DeviceDetector, 
  ResponsiveInfo, 
  DeviceInfo, 
  MediaQueryUtils,
  BREAKPOINTS,
  BreakpointKey 
} from '@/lib/responsive'
import { debounce } from '@/lib/utils'

/**
 * 响应式Hook返回值接口
 */
export interface UseResponsiveReturn extends ResponsiveInfo {
  deviceInfo: DeviceInfo
  isBreakpoint: (breakpoint: BreakpointKey) => boolean
  isBreakpointUp: (breakpoint: BreakpointKey) => boolean
  isBreakpointDown: (breakpoint: BreakpointKey) => boolean
  isPortrait: boolean
  isLandscape: boolean
}

/**
 * 响应式Hook配置选项
 */
export interface UseResponsiveOptions {
  /**
   * 防抖延迟时间（毫秒）
   * @default 150
   */
  debounceDelay?: number
  
  /**
   * 是否启用设备信息检测
   * @default true
   */
  enableDeviceInfo?: boolean
  
  /**
   * 是否在服务端渲染时使用默认值
   * @default true
   */
  ssrFallback?: boolean
}

/**
 * 响应式检测Hook
 * 
 * @param options 配置选项
 * @returns 响应式信息和工具函数
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { isMobile, isTablet, screenSize, width } = useResponsive()
 *   
 *   return (
 *     <div>
 *       {isMobile ? '移动端视图' : '桌面端视图'}
 *       <p>屏幕尺寸: {screenSize}</p>
 *       <p>屏幕宽度: {width}px</p>
 *     </div>
 *   )
 * }
 * ```
 */
export function useResponsive(options: UseResponsiveOptions = {}): UseResponsiveReturn {
  const {
    debounceDelay = 150,
    enableDeviceInfo = true,
    ssrFallback = true
  } = options

  // 初始化状态
  const [responsiveInfo, setResponsiveInfo] = useState<ResponsiveInfo>(() => {
    if (typeof window === 'undefined' && ssrFallback) {
      // 服务端渲染时的默认值
      return {
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        screenSize: 'lg',
        width: 1200,
        height: 800
      }
    }
    return DeviceDetector.getResponsiveInfo()
  })

  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => {
    if (typeof window === 'undefined' && ssrFallback) {
      // 服务端渲染时的默认值
      return {
        type: 'desktop',
        os: 'unknown',
        browser: 'other',
        hasTouch: false,
        orientation: 'landscape',
        pixelRatio: 1
      }
    }
    return DeviceDetector.getDeviceInfo()
  })

  // 更新响应式信息的函数
  const updateResponsiveInfo = useCallback(() => {
    const newResponsiveInfo = DeviceDetector.getResponsiveInfo()
    setResponsiveInfo(newResponsiveInfo)

    if (enableDeviceInfo) {
      const newDeviceInfo = DeviceDetector.getDeviceInfo()
      setDeviceInfo(newDeviceInfo)
    }
  }, [enableDeviceInfo])

  // 防抖处理的更新函数
  const debouncedUpdate = useCallback(
    debounce(updateResponsiveInfo, debounceDelay),
    [updateResponsiveInfo, debounceDelay]
  )

  // 监听窗口大小变化
  useEffect(() => {
    if (typeof window === 'undefined') return

    // 初始化时立即更新一次
    updateResponsiveInfo()

    // 监听窗口大小变化
    const handleResize = () => {
      debouncedUpdate()
    }

    // 监听方向变化
    const handleOrientationChange = () => {
      // 方向变化时需要延迟一下，等待浏览器完成布局调整
      setTimeout(() => {
        updateResponsiveInfo()
      }, 100)
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleOrientationChange)

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
    }
  }, [updateResponsiveInfo, debouncedUpdate])

  // 工具函数
  const isBreakpoint = useCallback((breakpoint: BreakpointKey): boolean => {
    if (typeof window === 'undefined') return false
    
    const width = window.innerWidth
    const breakpointValue = BREAKPOINTS[breakpoint]
    const breakpointKeys = Object.keys(BREAKPOINTS) as BreakpointKey[]
    const currentIndex = breakpointKeys.indexOf(breakpoint)
    const nextBreakpoint = breakpointKeys[currentIndex + 1]
    const nextBreakpointValue = nextBreakpoint ? BREAKPOINTS[nextBreakpoint] : Infinity
    
    return width >= breakpointValue && width < nextBreakpointValue
  }, [])

  const isBreakpointUp = useCallback((breakpoint: BreakpointKey): boolean => {
    if (typeof window === 'undefined') return false
    return window.innerWidth >= BREAKPOINTS[breakpoint]
  }, [])

  const isBreakpointDown = useCallback((breakpoint: BreakpointKey): boolean => {
    if (typeof window === 'undefined') return false
    return window.innerWidth < BREAKPOINTS[breakpoint]
  }, [])

  // 计算方向信息
  const isPortrait = responsiveInfo.height > responsiveInfo.width
  const isLandscape = !isPortrait

  return {
    ...responsiveInfo,
    deviceInfo,
    isBreakpoint,
    isBreakpointUp,
    isBreakpointDown,
    isPortrait,
    isLandscape
  }
}

/**
 * 媒体查询Hook
 * 用于监听特定的媒体查询变化
 * 
 * @param query 媒体查询字符串
 * @returns 是否匹配媒体查询
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const isMobile = useMediaQuery('(max-width: 768px)')
 *   const isDarkMode = useMediaQuery('(prefers-color-scheme: dark)')
 *   
 *   return (
 *     <div>
 *       {isMobile ? '移动端' : '桌面端'}
 *       {isDarkMode ? '深色模式' : '浅色模式'}
 *     </div>
 *   )
 * }
 * ```
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState<boolean>(() => {
    if (typeof window === 'undefined') return false
    return MediaQueryUtils.matches(query)
  })

  useEffect(() => {
    if (typeof window === 'undefined') return

    // 初始化时检查一次
    setMatches(MediaQueryUtils.matches(query))

    // 监听媒体查询变化
    const unsubscribe = MediaQueryUtils.addListener(query, setMatches)

    return unsubscribe
  }, [query])

  return matches
}

/**
 * 断点Hook
 * 用于监听特定断点的变化
 * 
 * @param breakpoint 断点名称
 * @param direction 方向 ('up' | 'down' | 'only')
 * @returns 是否匹配断点
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const isMobileUp = useBreakpoint('md', 'up')
 *   const isMobileOnly = useBreakpoint('sm', 'only')
 *   
 *   return (
 *     <div>
 *       {isMobileUp ? '中等屏幕及以上' : '小屏幕'}
 *       {isMobileOnly ? '仅小屏幕' : '其他屏幕'}
 *     </div>
 *   )
 * }
 * ```
 */
export function useBreakpoint(
  breakpoint: BreakpointKey, 
  direction: 'up' | 'down' | 'only' = 'up'
): boolean {
  const query = MediaQueryUtils.createQuery(breakpoint, direction)
  return useMediaQuery(query)
}

/**
 * 屏幕尺寸Hook
 * 返回当前屏幕尺寸分类
 * 
 * @returns 当前屏幕尺寸
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const screenSize = useScreenSize()
 *   
 *   return (
 *     <div>
 *       当前屏幕尺寸: {screenSize}
 *     </div>
 *   )
 * }
 * ```
 */
export function useScreenSize(): BreakpointKey {
  const { screenSize } = useResponsive()
  return screenSize
}

/**
 * 设备类型Hook
 * 返回当前设备类型信息
 * 
 * @returns 设备类型信息
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { isMobile, isTablet, isDesktop } = useDeviceType()
 *   
 *   return (
 *     <div>
 *       {isMobile && '移动设备'}
 *       {isTablet && '平板设备'}
 *       {isDesktop && '桌面设备'}
 *     </div>
 *   )
 * }
 * ```
 */
export function useDeviceType() {
  const { isMobile, isTablet, isDesktop, deviceInfo } = useResponsive()
  
  return {
    isMobile,
    isTablet,
    isDesktop,
    deviceInfo,
    hasTouch: deviceInfo.hasTouch,
    isIOS: deviceInfo.os === 'ios',
    isAndroid: deviceInfo.os === 'android'
  }
}