import { useState, useEffect, useCallback } from 'react'
import { message } from 'antd'
import type { 
  Notification, 
  NotificationListResponse, 
  ApiResponse,
  BatchNotificationRequest 
} from '@/types'

interface UseNotificationsOptions {
  page?: number
  limit?: number
  read?: boolean
  type?: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR'
  autoRefresh?: boolean
  refreshInterval?: number
}

interface UseNotificationsReturn {
  notifications: Notification[]
  total: number
  unreadCount: number
  loading: boolean
  error: string | null
  
  // 操作方法
  refresh: () => Promise<void>
  markAsRead: (id: string) => Promise<void>
  markAsUnread: (id: string) => Promise<void>
  deleteNotification: (id: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  deleteMultiple: (ids: string[]) => Promise<void>
  
  // 分页相关
  currentPage: number
  setPage: (page: number) => void
}

export function useNotifications(options: UseNotificationsOptions = {}): UseNotificationsReturn {
  const {
    page: initialPage = 1,
    limit = 20,
    read,
    type,
    autoRefresh = false,
    refreshInterval = 30000 // 30秒
  } = options

  const [notifications, setNotifications] = useState<Notification[]>([])
  const [total, setTotal] = useState(0)
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(initialPage)

  // 构建查询参数
  const buildQueryParams = useCallback((pageNum: number = currentPage) => {
    const params = new URLSearchParams()
    params.append('page', pageNum.toString())
    params.append('limit', limit.toString())
    
    if (read !== undefined) {
      params.append('read', read.toString())
    }
    if (type) {
      params.append('type', type)
    }
    
    return params.toString()
  }, [currentPage, limit, read, type])

  // 获取通知列表
  const fetchNotifications = useCallback(async (pageNum: number = currentPage) => {
    try {
      setLoading(true)
      setError(null)

      const queryString = buildQueryParams(pageNum)
      const response = await fetch(`/api/notifications?${queryString}`)
      const result: ApiResponse<NotificationListResponse> = await response.json()

      if (result.success && result.data) {
        setNotifications(result.data.notifications)
        setTotal(result.data.total)
        setUnreadCount(result.data.unreadCount)
      } else {
        setError(result.message || '获取通知失败')
      }
    } catch (err) {
      setError('网络错误')
      console.error('获取通知失败:', err)
    } finally {
      setLoading(false)
    }
  }, [currentPage, buildQueryParams])

  // 刷新数据
  const refresh = useCallback(() => fetchNotifications(currentPage), [fetchNotifications, currentPage])

  // 设置页码
  const setPage = useCallback((page: number) => {
    setCurrentPage(page)
    fetchNotifications(page)
  }, [fetchNotifications])

  // 标记为已读
  const markAsRead = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ read: true }),
      })

      const result: ApiResponse<Notification> = await response.json()

      if (result.success) {
        // 更新本地状态
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === id 
              ? { ...notification, read: true }
              : notification
          )
        )
        
        // 更新未读数量
        setUnreadCount(prev => Math.max(0, prev - 1))
        
        message.success('标记已读成功')
      } else {
        message.error(result.message || '标记已读失败')
      }
    } catch (err) {
      message.error('网络错误')
      console.error('标记已读失败:', err)
    }
  }, [])

  // 标记为未读
  const markAsUnread = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ read: false }),
      })

      const result: ApiResponse<Notification> = await response.json()

      if (result.success) {
        // 更新本地状态
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === id 
              ? { ...notification, read: false }
              : notification
          )
        )
        
        // 更新未读数量
        setUnreadCount(prev => prev + 1)
        
        message.success('标记未读成功')
      } else {
        message.error(result.message || '标记未读失败')
      }
    } catch (err) {
      message.error('网络错误')
      console.error('标记未读失败:', err)
    }
  }, [])

  // 删除通知
  const deleteNotification = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}`, {
        method: 'DELETE',
      })

      const result: ApiResponse<null> = await response.json()

      if (result.success) {
        // 移除本地状态中的通知
        setNotifications(prev => {
          const notification = prev.find(n => n.id === id)
          const filtered = prev.filter(n => n.id !== id)
          
          // 如果删除的是未读通知，更新未读数量
          if (notification && !notification.read) {
            setUnreadCount(prevCount => Math.max(0, prevCount - 1))
          }
          
          return filtered
        })
        
        // 更新总数
        setTotal(prev => Math.max(0, prev - 1))
        
        message.success('删除成功')
      } else {
        message.error(result.message || '删除失败')
      }
    } catch (err) {
      message.error('网络错误')
      console.error('删除通知失败:', err)
    }
  }, [])

  // 标记所有为已读
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/batch', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'mark_read' }),
      })

      const result: ApiResponse<{ count: number }> = await response.json()

      if (result.success) {
        // 更新本地状态
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, read: true }))
        )
        setUnreadCount(0)
        
        message.success(result.message || '全部标记已读成功')
      } else {
        message.error(result.message || '操作失败')
      }
    } catch (err) {
      message.error('网络错误')
      console.error('批量标记已读失败:', err)
    }
  }, [])

  // 批量删除
  const deleteMultiple = useCallback(async (ids: string[]) => {
    try {
      const response = await fetch('/api/notifications/batch', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      })

      const result: ApiResponse<{ count: number }> = await response.json()

      if (result.success) {
        // 计算删除的未读通知数量
        const deletedUnreadCount = notifications
          .filter(n => ids.includes(n.id) && !n.read)
          .length

        // 更新本地状态
        setNotifications(prev => prev.filter(n => !ids.includes(n.id)))
        setTotal(prev => Math.max(0, prev - (result.data?.count || 0)))
        setUnreadCount(prev => Math.max(0, prev - deletedUnreadCount))
        
        message.success(result.message || '批量删除成功')
      } else {
        message.error(result.message || '批量删除失败')
      }
    } catch (err) {
      message.error('网络错误')
      console.error('批量删除失败:', err)
    }
  }, [notifications])

  // 初始加载
  useEffect(() => {
    fetchNotifications(currentPage)
  }, []) // 只在组件挂载时执行

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      fetchNotifications(currentPage)
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchNotifications, currentPage])

  return {
    notifications,
    total,
    unreadCount,
    loading,
    error,
    refresh,
    markAsRead,
    markAsUnread,
    deleteNotification,
    markAllAsRead,
    deleteMultiple,
    currentPage,
    setPage,
  }
}

// 简化版Hook - 只获取未读数量
export function useUnreadCount() {
  const [count, setCount] = useState(0)
  const [loading, setLoading] = useState(true)

  const fetchUnreadCount = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/unread-count')
      const result: ApiResponse<{ count: number }> = await response.json()

      if (result.success && result.data) {
        setCount(result.data.count)
      }
    } catch (err) {
      console.error('获取未读数量失败:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchUnreadCount()
    
    // 每30秒刷新一次未读数量
    const interval = setInterval(fetchUnreadCount, 30000)
    return () => clearInterval(interval)
  }, [fetchUnreadCount])

  return { count, loading, refresh: fetchUnreadCount }
}