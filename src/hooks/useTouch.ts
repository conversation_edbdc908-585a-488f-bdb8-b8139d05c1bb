import { useRef, useEffect, useCallback } from 'react'

export interface TouchGestureOptions {
  onTap?: (event: React.TouchEvent) => void
  onLongPress?: (event: React.TouchEvent) => void
  onSwipe?: (direction: 'left' | 'right' | 'up' | 'down', distance: number) => void
  onPinch?: (scale: number) => void
  onDoubleTap?: (event: React.TouchEvent) => void
  longPressDelay?: number
  swipeThreshold?: number
  doubleTapDelay?: number
}

export interface TouchState {
  isPressed: boolean
  isSwiping: boolean
  isPinching: boolean
}

export function useTouch(options: TouchGestureOptions = {}) {
  const {
    onTap,
    onLongPress,
    onSwipe,
    onPinch,
    onDoubleTap,
    longPressDelay = 500,
    swipeThreshold = 50,
    doubleTapDelay = 300
  } = options

  const touchStateRef = useRef<TouchState>({
    isPressed: false,
    isSwiping: false,
    isPinching: false
  })

  const touchStartRef = useRef<{
    x: number
    y: number
    time: number
    touches: number
  } | null>(null)

  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastTapRef = useRef<number>(0)
  const initialDistanceRef = useRef<number>(0)

  const clearLongPressTimer = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current)
      longPressTimerRef.current = null
    }
  }, [])

  const getDistance = useCallback((touch1: Touch, touch2: Touch) => {
    const dx = touch1.clientX - touch2.clientX
    const dy = touch1.clientY - touch2.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    const touch = event.touches[0]
    const now = Date.now()

    touchStateRef.current.isPressed = true
    touchStateRef.current.isSwiping = false
    touchStateRef.current.isPinching = false

    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: now,
      touches: event.touches.length
    }

    // Handle pinch gesture
    if (event.touches.length === 2 && onPinch) {
      touchStateRef.current.isPinching = true
      initialDistanceRef.current = getDistance(event.touches[0], event.touches[1])
    }

    // Start long press timer
    if (onLongPress && event.touches.length === 1) {
      clearLongPressTimer() // Clear any existing timer
      longPressTimerRef.current = setTimeout(() => {
        if (touchStateRef.current.isPressed && !touchStateRef.current.isSwiping) {
          onLongPress(event)
        }
      }, longPressDelay)
    }
  }, [onLongPress, onPinch, longPressDelay, getDistance, clearLongPressTimer])

  const handleTouchMove = useCallback((event: React.TouchEvent) => {
    if (!touchStartRef.current) return

    const touch = event.touches[0]
    const deltaX = touch.clientX - touchStartRef.current.x
    const deltaY = touch.clientY - touchStartRef.current.y
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    // Handle pinch gesture
    if (event.touches.length === 2 && onPinch && touchStateRef.current.isPinching) {
      const currentDistance = getDistance(event.touches[0], event.touches[1])
      const scale = currentDistance / initialDistanceRef.current
      onPinch(scale)
      return
    }

    // Detect swipe
    if (distance > swipeThreshold && !touchStateRef.current.isSwiping) {
      touchStateRef.current.isSwiping = true
      clearLongPressTimer()

      if (onSwipe) {
        let direction: 'left' | 'right' | 'up' | 'down'
        
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
          direction = deltaX > 0 ? 'right' : 'left'
        } else {
          direction = deltaY > 0 ? 'down' : 'up'
        }
        
        onSwipe(direction, distance)
      }
    }
  }, [onSwipe, onPinch, swipeThreshold, clearLongPressTimer, getDistance])

  const handleTouchEnd = useCallback((event: React.TouchEvent) => {
    const now = Date.now()
    
    clearLongPressTimer()

    if (touchStartRef.current && touchStateRef.current.isPressed) {
      const timeDiff = now - touchStartRef.current.time
      
      // Handle tap and double tap
      if (!touchStateRef.current.isSwiping && !touchStateRef.current.isPinching && timeDiff < longPressDelay) {
        const lastTapTime = lastTapRef.current
        lastTapRef.current = now

        // Check for double tap
        if (onDoubleTap && now - lastTapTime < doubleTapDelay) {
          onDoubleTap(event)
        } else if (onTap) {
          // Delay single tap to allow for double tap detection
          setTimeout(() => {
            if (now === lastTapRef.current) {
              onTap(event)
            }
          }, doubleTapDelay)
        }
      }
    }

    // Reset state
    touchStateRef.current = {
      isPressed: false,
      isSwiping: false,
      isPinching: false
    }
    touchStartRef.current = null
  }, [onTap, onDoubleTap, longPressDelay, doubleTapDelay, clearLongPressTimer])

  const touchHandlers = {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    onTouchCancel: handleTouchEnd
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearLongPressTimer()
    }
  }, [clearLongPressTimer])

  return {
    touchHandlers,
    touchState: touchStateRef.current
  }
}