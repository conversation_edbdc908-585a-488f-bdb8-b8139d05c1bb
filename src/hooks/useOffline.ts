'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  offlineRequestManager, 
  cacheManager, 
  userDataManager, 
  settingsManager,
  syncService,
  type OfflineRequest 
} from '@/lib/offlineStorage'

export interface OfflineStatus {
  isOnline: boolean
  isOfflineReady: boolean
  syncInProgress: boolean
  lastSyncTime: number | null
  pendingRequests: number
  cacheSize: number
  error: string | null
}

export interface OfflineActions {
  // 网络状态管理
  checkConnection: () => boolean
  
  // 离线请求管理
  addOfflineRequest: (request: Omit<OfflineRequest, 'id' | 'timestamp' | 'retryCount'>) => Promise<void>
  getPendingRequestsCount: () => Promise<number>
  clearPendingRequests: () => Promise<void>
  
  // 缓存管理
  setCache: (key: string, data: any, ttl?: number) => Promise<void>
  getCache: (key: string) => Promise<any>
  clearCache: () => Promise<void>
  getCacheStats: () => Promise<{ count: number; size: number }>
  
  // 用户数据管理
  saveUserData: (id: string, userId: string, data: any) => Promise<void>
  getUserData: (id: string) => Promise<any>
  
  // 设置管理
  setSetting: (key: string, value: any) => Promise<void>
  getSetting: (key: string, defaultValue?: any) => Promise<any>
  
  // 同步管理
  manualSync: () => Promise<void>
  
  // 错误处理
  clearError: () => void
}

/**
 * 离线状态管理 Hook
 * 提供离线状态监控、数据缓存和同步功能
 */
export function useOffline(): [OfflineStatus, OfflineActions] {
  const [status, setStatus] = useState<OfflineStatus>({
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    isOfflineReady: false,
    syncInProgress: false,
    lastSyncTime: null,
    pendingRequests: 0,
    cacheSize: 0,
    error: null
  })

  // 更新状态
  const updateStatus = useCallback(async (updates: Partial<OfflineStatus>) => {
    setStatus(prev => ({ ...prev, ...updates }))
  }, [])

  // 初始化离线功能
  const initializeOffline = useCallback(async () => {
    try {
      // 初始化各个管理器
      await Promise.all([
        offlineRequestManager.init(),
        cacheManager.init(),
        userDataManager.init(),
        settingsManager.init()
      ])

      // 获取初始状态
      const [pendingRequests, cacheStats, syncStatus] = await Promise.all([
        offlineRequestManager.getPendingRequests(),
        cacheManager.getStats(),
        Promise.resolve(syncService.getSyncStatus())
      ])

      await updateStatus({
        isOfflineReady: true,
        pendingRequests: pendingRequests.length,
        cacheSize: cacheStats.size,
        syncInProgress: syncStatus.syncInProgress,
        lastSyncTime: syncStatus.lastSyncTime
      })
    } catch (error) {
      console.error('[useOffline] Initialization failed:', error)
      await updateStatus({
        error: '离线功能初始化失败',
        isOfflineReady: false
      })
    }
  }, [updateStatus])

  // 监听网络状态变化
  useEffect(() => {
    const handleOnline = async () => {
      await updateStatus({ isOnline: true, error: null })
      
      // 触发同步
      try {
        await syncService.syncWhenOnline()
        const pendingRequests = await offlineRequestManager.getPendingRequests()
        await updateStatus({ 
          pendingRequests: pendingRequests.length,
          syncInProgress: false 
        })
      } catch (error) {
        console.error('[useOffline] Sync failed:', error)
      }
    }

    const handleOffline = async () => {
      await updateStatus({ 
        isOnline: false, 
        syncInProgress: false,
        error: '网络连接已断开，已切换到离线模式' 
      })
    }

    // 添加事件监听器
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // 初始化
    initializeOffline()

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [updateStatus, initializeOffline])

  // 定期更新状态
  useEffect(() => {
    const interval = setInterval(async () => {
      if (!status.isOfflineReady) return

      try {
        const [pendingRequests, cacheStats, syncStatus] = await Promise.all([
          offlineRequestManager.getPendingRequests(),
          cacheManager.getStats(),
          Promise.resolve(syncService.getSyncStatus())
        ])

        await updateStatus({
          pendingRequests: pendingRequests.length,
          cacheSize: cacheStats.size,
          syncInProgress: syncStatus.syncInProgress,
          lastSyncTime: syncStatus.lastSyncTime
        })
      } catch (error) {
        console.error('[useOffline] Status update failed:', error)
      }
    }, 5000) // 每5秒更新一次

    return () => clearInterval(interval)
  }, [status.isOfflineReady, updateStatus])

  // Actions
  const actions: OfflineActions = {
    // 网络状态管理
    checkConnection: () => {
      const isOnline = navigator.onLine
      updateStatus({ isOnline })
      return isOnline
    },

    // 离线请求管理
    addOfflineRequest: async (request) => {
      try {
        await offlineRequestManager.addRequest(request)
        const pendingRequests = await offlineRequestManager.getPendingRequests()
        await updateStatus({ pendingRequests: pendingRequests.length })
      } catch (error) {
        console.error('[useOffline] Add offline request failed:', error)
        await updateStatus({ error: '添加离线请求失败' })
        throw error
      }
    },

    getPendingRequestsCount: async () => {
      try {
        const requests = await offlineRequestManager.getPendingRequests()
        return requests.length
      } catch (error) {
        console.error('[useOffline] Get pending requests failed:', error)
        return 0
      }
    },

    clearPendingRequests: async () => {
      try {
        const requests = await offlineRequestManager.getPendingRequests()
        await Promise.all(requests.map(req => offlineRequestManager.removeRequest(req.id!)))
        await updateStatus({ pendingRequests: 0 })
      } catch (error) {
        console.error('[useOffline] Clear pending requests failed:', error)
        await updateStatus({ error: '清理待处理请求失败' })
        throw error
      }
    },

    // 缓存管理
    setCache: async (key, data, ttl) => {
      try {
        await cacheManager.set(key, data, ttl)
        const stats = await cacheManager.getStats()
        await updateStatus({ cacheSize: stats.size })
      } catch (error) {
        console.error('[useOffline] Set cache failed:', error)
        await updateStatus({ error: '设置缓存失败' })
        throw error
      }
    },

    getCache: async (key) => {
      try {
        return await cacheManager.get(key)
      } catch (error) {
        console.error('[useOffline] Get cache failed:', error)
        return null
      }
    },

    clearCache: async () => {
      try {
        // 获取所有缓存键并删除
        const stats = await cacheManager.getStats()
        // 这里简化处理，实际应该遍历删除所有缓存项
        await updateStatus({ cacheSize: 0 })
      } catch (error) {
        console.error('[useOffline] Clear cache failed:', error)
        await updateStatus({ error: '清理缓存失败' })
        throw error
      }
    },

    getCacheStats: async () => {
      try {
        return await cacheManager.getStats()
      } catch (error) {
        console.error('[useOffline] Get cache stats failed:', error)
        return { count: 0, size: 0 }
      }
    },

    // 用户数据管理
    saveUserData: async (id, userId, data) => {
      try {
        await userDataManager.saveUserData(id, userId, data)
      } catch (error) {
        console.error('[useOffline] Save user data failed:', error)
        await updateStatus({ error: '保存用户数据失败' })
        throw error
      }
    },

    getUserData: async (id) => {
      try {
        const userData = await userDataManager.getUserData(id)
        return userData?.data || null
      } catch (error) {
        console.error('[useOffline] Get user data failed:', error)
        return null
      }
    },

    // 设置管理
    setSetting: async (key, value) => {
      try {
        await settingsManager.set(key, value)
      } catch (error) {
        console.error('[useOffline] Set setting failed:', error)
        await updateStatus({ error: '保存设置失败' })
        throw error
      }
    },

    getSetting: async (key, defaultValue) => {
      try {
        return await settingsManager.get(key, defaultValue)
      } catch (error) {
        console.error('[useOffline] Get setting failed:', error)
        return defaultValue
      }
    },

    // 同步管理
    manualSync: async () => {
      try {
        await updateStatus({ syncInProgress: true, error: null })
        await syncService.manualSync()
        
        const [pendingRequests, syncStatus] = await Promise.all([
          offlineRequestManager.getPendingRequests(),
          Promise.resolve(syncService.getSyncStatus())
        ])
        
        await updateStatus({
          syncInProgress: false,
          pendingRequests: pendingRequests.length,
          lastSyncTime: syncStatus.lastSyncTime
        })
      } catch (error) {
        console.error('[useOffline] Manual sync failed:', error)
        await updateStatus({ 
          syncInProgress: false,
          error: error instanceof Error ? error.message : '同步失败'
        })
        throw error
      }
    },

    // 错误处理
    clearError: () => {
      updateStatus({ error: null })
    }
  }

  return [status, actions]
}

// 导出相关类型
export type { OfflineStatus, OfflineActions }