'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { 
  performanceMonitor, 
  performanceUtils,
  type PerformanceMetrics, 
  type PerformanceWarning, 
  type PerformanceOptimization 
} from '@/lib/performance'

export interface PerformanceState {
  metrics: Partial<PerformanceMetrics>
  warnings: PerformanceWarning[]
  optimizations: PerformanceOptimization[]
  isMonitoring: boolean
  lastUpdate: number
}

export interface PerformanceActions {
  // 监控控制
  startMonitoring: () => void
  stopMonitoring: () => void
  refreshMetrics: () => void
  
  // 性能测量
  startMeasure: (name: string) => void
  endMeasure: (name: string) => number
  measureFunction: <T extends (...args: any[]) => any>(fn: T, name?: string) => T
  
  // 性能优化工具
  debounce: <T extends (...args: any[]) => any>(fn: T, delay: number) => T
  throttle: <T extends (...args: any[]) => any>(fn: T, delay: number) => T
  batchProcess: <T>(items: T[], processor: (item: T) => void, batchSize?: number, delay?: number) => Promise<void>
  
  // 报告生成
  exportReport: () => string
  getPerformanceScore: () => number
  
  // 内存管理
  checkMemoryUsage: () => number
  forceGarbageCollection: () => void
}

/**
 * 性能监控和优化 Hook
 * 提供性能指标监控、分析和优化工具
 */
export function usePerformance(): [PerformanceState, PerformanceActions] {
  const [state, setState] = useState<PerformanceState>({
    metrics: {},
    warnings: [],
    optimizations: [],
    isMonitoring: false,
    lastUpdate: 0
  })

  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const measurementsRef = useRef<Map<string, number>>(new Map())

  // 更新性能状态
  const updatePerformanceState = useCallback(() => {
    const metrics = performanceMonitor.getMetrics()
    const warnings = performanceMonitor.analyzePerformance()
    const optimizations = performanceMonitor.generateOptimizations()

    setState(prev => ({
      ...prev,
      metrics,
      warnings,
      optimizations,
      lastUpdate: Date.now()
    }))
  }, [])

  // 开始监控
  const startMonitoring = useCallback(() => {
    if (intervalRef.current) return

    setState(prev => ({ ...prev, isMonitoring: true }))
    
    // 立即更新一次
    updatePerformanceState()
    
    // 设置定期更新
    intervalRef.current = setInterval(() => {
      updatePerformanceState()
    }, 5000) // 每5秒更新一次
  }, [updatePerformanceState])

  // 停止监控
  const stopMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    
    setState(prev => ({ ...prev, isMonitoring: false }))
  }, [])

  // 刷新指标
  const refreshMetrics = useCallback(() => {
    updatePerformanceState()
  }, [updatePerformanceState])

  // 开始测量
  const startMeasure = useCallback((name: string) => {
    measurementsRef.current.set(name, performance.now())
    performanceMonitor.startMeasure(name)
  }, [])

  // 结束测量
  const endMeasure = useCallback((name: string): number => {
    const startTime = measurementsRef.current.get(name)
    const endTime = performance.now()
    
    if (startTime) {
      const duration = endTime - startTime
      measurementsRef.current.delete(name)
      return duration
    }
    
    return performanceMonitor.endMeasure(name)
  }, [])

  // 测量函数执行时间
  const measureFunction = useCallback(<T extends (...args: any[]) => any>(
    fn: T, 
    name?: string
  ): T => {
    return performanceUtils.measureFunction(fn, name)
  }, [])

  // 防抖函数
  const debounce = useCallback(<T extends (...args: any[]) => any>(
    fn: T, 
    delay: number
  ): T => {
    return performanceUtils.debounce(fn, delay)
  }, [])

  // 节流函数
  const throttle = useCallback(<T extends (...args: any[]) => any>(
    fn: T, 
    delay: number
  ): T => {
    return performanceUtils.throttle(fn, delay)
  }, [])

  // 批量处理
  const batchProcess = useCallback(<T>(
    items: T[], 
    processor: (item: T) => void, 
    batchSize: number = 100, 
    delay: number = 0
  ): Promise<void> => {
    return performanceUtils.batchProcess(items, processor, batchSize, delay)
  }, [])

  // 导出报告
  const exportReport = useCallback((): string => {
    return performanceMonitor.exportReport()
  }, [])

  // 计算性能分数
  const getPerformanceScore = useCallback((): number => {
    const { metrics } = state
    let score = 100
    
    // 基于核心Web指标计算分数
    if (metrics.pageLoad) {
      const { firstContentfulPaint, largestContentfulPaint, firstInputDelay, cumulativeLayoutShift } = metrics.pageLoad
      
      // FCP 评分 (0-25分)
      if (firstContentfulPaint) {
        if (firstContentfulPaint > 3000) score -= 25
        else if (firstContentfulPaint > 1800) score -= 15
        else if (firstContentfulPaint > 1000) score -= 5
      }
      
      // LCP 评分 (0-25分)
      if (largestContentfulPaint) {
        if (largestContentfulPaint > 4000) score -= 25
        else if (largestContentfulPaint > 2500) score -= 15
        else if (largestContentfulPaint > 1500) score -= 5
      }
      
      // FID 评分 (0-25分)
      if (firstInputDelay) {
        if (firstInputDelay > 300) score -= 25
        else if (firstInputDelay > 100) score -= 15
        else if (firstInputDelay > 50) score -= 5
      }
      
      // CLS 评分 (0-25分)
      if (cumulativeLayoutShift) {
        if (cumulativeLayoutShift > 0.25) score -= 25
        else if (cumulativeLayoutShift > 0.1) score -= 15
        else if (cumulativeLayoutShift > 0.05) score -= 5
      }
    }
    
    return Math.max(0, Math.min(100, score))
  }, [state])

  // 检查内存使用
  const checkMemoryUsage = useCallback((): number => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return memory.usedJSHeapSize
    }
    return 0
  }, [])

  // 强制垃圾回收（仅在开发环境）
  const forceGarbageCollection = useCallback(() => {
    if (process.env.NODE_ENV === 'development' && 'gc' in window) {
      (window as any).gc()
    }
  }, [])

  // 组件挂载时自动开始监控
  useEffect(() => {
    startMonitoring()
    
    return () => {
      stopMonitoring()
      performanceMonitor.cleanup()
    }
  }, [startMonitoring, stopMonitoring])

  // 监听页面可见性变化
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopMonitoring()
      } else {
        startMonitoring()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [startMonitoring, stopMonitoring])

  // 监听内存压力
  useEffect(() => {
    const handleMemoryPressure = () => {
      console.warn('[Performance] Memory pressure detected')
      // 可以在这里触发内存清理逻辑
      forceGarbageCollection()
    }

    // 监听内存压力事件（如果支持）
    if ('memory' in navigator) {
      (navigator as any).memory?.addEventListener?.('pressure', handleMemoryPressure)
    }

    return () => {
      if ('memory' in navigator) {
        (navigator as any).memory?.removeEventListener?.('pressure', handleMemoryPressure)
      }
    }
  }, [forceGarbageCollection])

  const actions: PerformanceActions = {
    startMonitoring,
    stopMonitoring,
    refreshMetrics,
    startMeasure,
    endMeasure,
    measureFunction,
    debounce,
    throttle,
    batchProcess,
    exportReport,
    getPerformanceScore,
    checkMemoryUsage,
    forceGarbageCollection
  }

  return [state, actions]
}

// 性能监控组件 HOC
export function withPerformanceMonitoring<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) {
  const WithPerformanceMonitoring = (props: P) => {
    const [, { measureFunction }] = usePerformance()
    
    // 测量组件渲染时间
    useEffect(() => {
      const name = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component'
      performanceMonitor.startMeasure(`${name}-render`)
      
      return () => {
        performanceMonitor.endMeasure(`${name}-render`)
      }
    }, [])

    return React.createElement(WrappedComponent, props)
  }

  WithPerformanceMonitoring.displayName = `withPerformanceMonitoring(${
    componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`

  return WithPerformanceMonitoring
}

// 性能监控装饰器
export function performanceMonitored(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = function (...args: any[]) {
    const methodName = `${target.constructor.name}.${propertyKey}`
    performanceMonitor.startMeasure(methodName)
    
    try {
      const result = originalMethod.apply(this, args)
      
      if (result instanceof Promise) {
        return result.finally(() => {
          performanceMonitor.endMeasure(methodName)
        })
      }
      
      performanceMonitor.endMeasure(methodName)
      return result
    } catch (error) {
      performanceMonitor.endMeasure(methodName)
      throw error
    }
  }

  return descriptor
}

// 导出相关类型
export type { PerformanceState, PerformanceActions }