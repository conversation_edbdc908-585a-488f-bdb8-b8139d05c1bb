/**
 * useMobileForm Hook - 移动端表单优化Hook
 * 提供表单字段自动聚焦、键盘类型优化、表单导航等功能
 */

import { useCallback, useEffect, useRef, useState } from 'react'
import { FormInstance } from 'antd'
import { useResponsive } from './useResponsive'

export interface MobileFormField {
  name: string
  element: HTMLElement
  inputMode?: string
  autoComplete?: string
  type?: string
}

export interface UseMobileFormOptions {
  /**
   * 表单实例
   */
  form?: FormInstance
  
  /**
   * 是否启用自动聚焦
   */
  enableAutoFocus?: boolean
  
  /**
   * 是否启用智能键盘类型
   */
  enableSmartKeyboard?: boolean
  
  /**
   * 是否启用表单导航
   */
  enableFormNavigation?: boolean
  
  /**
   * 自动聚焦延迟时间（毫秒）
   */
  autoFocusDelay?: number
  
  /**
   * 滚动偏移量
   */
  scrollOffset?: number
}

export interface UseMobileFormReturn {
  /**
   * 注册表单字段
   */
  registerField: (name: string, element: HTMLElement) => void
  
  /**
   * 注销表单字段
   */
  unregisterField: (name: string) => void
  
  /**
   * 聚焦到指定字段
   */
  focusField: (name: string) => void
  
  /**
   * 聚焦到下一个字段
   */
  focusNextField: () => void
  
  /**
   * 聚焦到上一个字段
   */
  focusPrevField: () => void
  
  /**
   * 聚焦到第一个错误字段
   */
  focusFirstError: () => void
  
  /**
   * 获取当前聚焦字段
   */
  getCurrentField: () => MobileFormField | null
  
  /**
   * 获取所有字段
   */
  getAllFields: () => MobileFormField[]
  
  /**
   * 优化字段的键盘类型
   */
  optimizeKeyboard: (element: HTMLElement, fieldName: string) => void
}

/**
 * 移动端表单优化Hook
 */
export function useMobileForm(options: UseMobileFormOptions = {}): UseMobileFormReturn {
  const {
    form,
    enableAutoFocus = true,
    enableSmartKeyboard = true,
    enableFormNavigation = true,
    autoFocusDelay = 300,
    scrollOffset = 20
  } = options

  const { isMobile } = useResponsive()
  const fieldsRef = useRef<Map<string, MobileFormField>>(new Map())
  const [currentFieldName, setCurrentFieldName] = useState<string | null>(null)

  // 字段名到键盘类型的映射
  const keyboardTypeMap: Record<string, string> = {
    // 数字相关
    age: 'numeric',
    phone: 'tel',
    mobile: 'tel',
    telephone: 'tel',
    number: 'numeric',
    amount: 'decimal',
    price: 'decimal',
    money: 'decimal',
    quantity: 'numeric',
    count: 'numeric',
    
    // 邮箱相关
    email: 'email',
    mail: 'email',
    
    // 网址相关
    url: 'url',
    website: 'url',
    link: 'url',
    
    // 搜索相关
    search: 'search',
    query: 'search',
    keyword: 'search'
  }

  // 字段名到自动完成类型的映射
  const autoCompleteMap: Record<string, string> = {
    name: 'name',
    username: 'username',
    email: 'email',
    phone: 'tel',
    mobile: 'tel',
    address: 'address-line1',
    city: 'address-level2',
    province: 'address-level1',
    country: 'country',
    zipcode: 'postal-code',
    postcode: 'postal-code',
    organization: 'organization',
    company: 'organization',
    title: 'organization-title',
    birthday: 'bday',
    gender: 'sex'
  }

  // 注册表单字段
  const registerField = useCallback((name: string, element: HTMLElement) => {
    const field: MobileFormField = {
      name,
      element,
      inputMode: getOptimalInputMode(name),
      autoComplete: getOptimalAutoComplete(name),
      type: getOptimalInputType(name)
    }

    fieldsRef.current.set(name, field)

    // 如果启用智能键盘，优化键盘类型
    if (enableSmartKeyboard && isMobile) {
      optimizeKeyboard(element, name)
    }

    // 绑定聚焦事件
    const handleFocus = () => {
      setCurrentFieldName(name)
      
      if (enableAutoFocus && isMobile) {
        setTimeout(() => {
          scrollToField(element)
        }, autoFocusDelay)
      }
    }

    const handleBlur = () => {
      if (currentFieldName === name) {
        setCurrentFieldName(null)
      }
    }

    element.addEventListener('focus', handleFocus)
    element.addEventListener('blur', handleBlur)

    // 返回清理函数
    return () => {
      element.removeEventListener('focus', handleFocus)
      element.removeEventListener('blur', handleBlur)
    }
  }, [enableSmartKeyboard, enableAutoFocus, isMobile, autoFocusDelay, currentFieldName])

  // 注销表单字段
  const unregisterField = useCallback((name: string) => {
    fieldsRef.current.delete(name)
    if (currentFieldName === name) {
      setCurrentFieldName(null)
    }
  }, [currentFieldName])

  // 获取最佳输入模式
  const getOptimalInputMode = (fieldName: string): string => {
    const lowerName = fieldName.toLowerCase()
    
    for (const [key, inputMode] of Object.entries(keyboardTypeMap)) {
      if (lowerName.includes(key)) {
        return inputMode
      }
    }
    
    return 'text'
  }

  // 获取最佳自动完成类型
  const getOptimalAutoComplete = (fieldName: string): string => {
    const lowerName = fieldName.toLowerCase()
    
    for (const [key, autoComplete] of Object.entries(autoCompleteMap)) {
      if (lowerName.includes(key)) {
        return autoComplete
      }
    }
    
    return 'off'
  }

  // 获取最佳输入类型
  const getOptimalInputType = (fieldName: string): string => {
    const lowerName = fieldName.toLowerCase()
    
    if (lowerName.includes('email')) return 'email'
    if (lowerName.includes('password')) return 'password'
    if (lowerName.includes('phone') || lowerName.includes('mobile')) return 'tel'
    if (lowerName.includes('url') || lowerName.includes('website')) return 'url'
    if (lowerName.includes('number') || lowerName.includes('age')) return 'number'
    if (lowerName.includes('date')) return 'date'
    if (lowerName.includes('time')) return 'time'
    
    return 'text'
  }

  // 优化键盘类型
  const optimizeKeyboard = useCallback((element: HTMLElement, fieldName: string) => {
    if (!isMobile) return

    const input = element.querySelector('input, textarea') as HTMLInputElement | HTMLTextAreaElement
    if (!input) return

    const inputMode = getOptimalInputMode(fieldName)
    const autoComplete = getOptimalAutoComplete(fieldName)
    const inputType = getOptimalInputType(fieldName)

    // 设置输入模式
    if (inputMode !== 'text') {
      input.setAttribute('inputmode', inputMode)
    }

    // 设置自动完成
    if (autoComplete !== 'off') {
      input.setAttribute('autocomplete', autoComplete)
    }

    // 设置输入类型（仅对input元素）
    if (input.tagName === 'INPUT' && inputType !== 'text') {
      (input as HTMLInputElement).type = inputType
    }

    // 防止iOS缩放
    if (input.style.fontSize !== '16px') {
      input.style.fontSize = '16px'
    }

    // 设置其他移动端优化属性
    input.setAttribute('autocapitalize', getAutoCapitalize(fieldName))
    input.setAttribute('autocorrect', getAutoCorrect(fieldName))
    input.setAttribute('spellcheck', getSpellCheck(fieldName))
  }, [isMobile])

  // 获取自动大写设置
  const getAutoCapitalize = (fieldName: string): string => {
    const lowerName = fieldName.toLowerCase()
    
    if (lowerName.includes('name') || lowerName.includes('title')) return 'words'
    if (lowerName.includes('sentence') || lowerName.includes('description')) return 'sentences'
    if (lowerName.includes('email') || lowerName.includes('username') || lowerName.includes('password')) return 'none'
    
    return 'off'
  }

  // 获取自动纠错设置
  const getAutoCorrect = (fieldName: string): string => {
    const lowerName = fieldName.toLowerCase()
    
    if (lowerName.includes('email') || lowerName.includes('username') || 
        lowerName.includes('password') || lowerName.includes('phone') ||
        lowerName.includes('number') || lowerName.includes('code')) {
      return 'off'
    }
    
    return 'on'
  }

  // 获取拼写检查设置
  const getSpellCheck = (fieldName: string): string => {
    const lowerName = fieldName.toLowerCase()
    
    if (lowerName.includes('description') || lowerName.includes('content') ||
        lowerName.includes('comment') || lowerName.includes('note')) {
      return 'true'
    }
    
    if (lowerName.includes('email') || lowerName.includes('username') ||
        lowerName.includes('password') || lowerName.includes('phone') ||
        lowerName.includes('number') || lowerName.includes('code')) {
      return 'false'
    }
    
    return 'false'
  }

  // 滚动到字段
  const scrollToField = (element: HTMLElement) => {
    if (!enableAutoFocus) return

    const rect = element.getBoundingClientRect()
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const targetTop = rect.top + scrollTop - scrollOffset

    window.scrollTo({
      top: targetTop,
      behavior: 'smooth'
    })
  }

  // 聚焦到指定字段
  const focusField = useCallback((name: string) => {
    const field = fieldsRef.current.get(name)
    if (!field) return

    const input = field.element.querySelector('input, textarea, select') as HTMLElement
    if (input) {
      input.focus()
    }
  }, [])

  // 获取字段顺序
  const getFieldOrder = useCallback(() => {
    const fields = Array.from(fieldsRef.current.values())
    return fields.sort((a, b) => {
      const aRect = a.element.getBoundingClientRect()
      const bRect = b.element.getBoundingClientRect()
      
      // 按垂直位置排序
      if (Math.abs(aRect.top - bRect.top) > 10) {
        return aRect.top - bRect.top
      }
      
      // 同一行按水平位置排序
      return aRect.left - bRect.left
    })
  }, [])

  // 聚焦到下一个字段
  const focusNextField = useCallback(() => {
    if (!enableFormNavigation || !currentFieldName) return

    const orderedFields = getFieldOrder()
    const currentIndex = orderedFields.findIndex(field => field.name === currentFieldName)
    
    if (currentIndex >= 0 && currentIndex < orderedFields.length - 1) {
      const nextField = orderedFields[currentIndex + 1]
      focusField(nextField.name)
    }
  }, [enableFormNavigation, currentFieldName, getFieldOrder, focusField])

  // 聚焦到上一个字段
  const focusPrevField = useCallback(() => {
    if (!enableFormNavigation || !currentFieldName) return

    const orderedFields = getFieldOrder()
    const currentIndex = orderedFields.findIndex(field => field.name === currentFieldName)
    
    if (currentIndex > 0) {
      const prevField = orderedFields[currentIndex - 1]
      focusField(prevField.name)
    }
  }, [enableFormNavigation, currentFieldName, getFieldOrder, focusField])

  // 聚焦到第一个错误字段
  const focusFirstError = useCallback(() => {
    if (!form) return

    const errors = form.getFieldsError()
    const firstError = errors.find(error => error.errors.length > 0)
    
    if (firstError) {
      focusField(firstError.name[0] as string)
    }
  }, [form, focusField])

  // 获取当前聚焦字段
  const getCurrentField = useCallback(() => {
    if (!currentFieldName) return null
    return fieldsRef.current.get(currentFieldName) || null
  }, [currentFieldName])

  // 获取所有字段
  const getAllFields = useCallback(() => {
    return Array.from(fieldsRef.current.values())
  }, [])

  // 监听表单提交错误，自动聚焦到第一个错误字段
  useEffect(() => {
    if (!form || !enableAutoFocus) return

    const originalValidateFields = form.validateFields
    form.validateFields = async (...args) => {
      try {
        return await originalValidateFields.apply(form, args)
      } catch (error) {
        // 延迟聚焦，等待错误信息渲染
        setTimeout(() => {
          focusFirstError()
        }, 100)
        throw error
      }
    }

    return () => {
      form.validateFields = originalValidateFields
    }
  }, [form, enableAutoFocus, focusFirstError])

  return {
    registerField,
    unregisterField,
    focusField,
    focusNextField,
    focusPrevField,
    focusFirstError,
    getCurrentField,
    getAllFields,
    optimizeKeyboard
  }
}

/**
 * 移动端表单字段Hook
 * 用于单个字段的移动端优化
 */
export function useMobileFormField(name: string, options: {
  autoFocus?: boolean
  optimizeKeyboard?: boolean
} = {}) {
  const { isMobile } = useResponsive()
  const elementRef = useRef<HTMLElement>(null)
  const { autoFocus = true, optimizeKeyboard = true } = options

  useEffect(() => {
    if (!elementRef.current || !isMobile) return

    const element = elementRef.current
    const input = element.querySelector('input, textarea') as HTMLInputElement | HTMLTextAreaElement

    if (!input) return

    // 优化键盘类型
    if (optimizeKeyboard) {
      const inputMode = getInputModeByName(name)
      const autoComplete = getAutoCompleteByName(name)

      if (inputMode !== 'text') {
        input.setAttribute('inputmode', inputMode)
      }

      if (autoComplete !== 'off') {
        input.setAttribute('autocomplete', autoComplete)
      }

      // 防止iOS缩放
      input.style.fontSize = '16px'
    }

    // 自动聚焦处理
    if (autoFocus) {
      const handleFocus = () => {
        setTimeout(() => {
          input.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }, 300)
      }

      input.addEventListener('focus', handleFocus)
      return () => input.removeEventListener('focus', handleFocus)
    }
  }, [name, isMobile, autoFocus, optimizeKeyboard])

  return { elementRef }
}

// 辅助函数
function getInputModeByName(name: string): string {
  const lowerName = name.toLowerCase()
  
  if (lowerName.includes('phone') || lowerName.includes('mobile')) return 'tel'
  if (lowerName.includes('email')) return 'email'
  if (lowerName.includes('number') || lowerName.includes('age') || lowerName.includes('count')) return 'numeric'
  if (lowerName.includes('price') || lowerName.includes('amount') || lowerName.includes('money')) return 'decimal'
  if (lowerName.includes('url') || lowerName.includes('website')) return 'url'
  if (lowerName.includes('search')) return 'search'
  
  return 'text'
}

function getAutoCompleteByName(name: string): string {
  const lowerName = name.toLowerCase()
  
  if (lowerName.includes('name')) return 'name'
  if (lowerName.includes('email')) return 'email'
  if (lowerName.includes('phone') || lowerName.includes('mobile')) return 'tel'
  if (lowerName.includes('address')) return 'address-line1'
  if (lowerName.includes('username')) return 'username'
  
  return 'off'
}