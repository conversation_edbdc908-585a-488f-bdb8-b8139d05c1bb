/**
 * 响应式容器组件
 * Responsive Container Component
 */

import React from 'react'
import { cn } from '@/lib/utils'
import { useResponsive } from '@/hooks/useResponsive'

export interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'full'
  padding?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  as?: keyof JSX.IntrinsicElements
}

/**
 * 响应式容器组件
 * 
 * @param props 组件属性
 * @returns 响应式容器组件
 * 
 * @example
 * ```tsx
 * <ResponsiveContainer maxWidth="lg" padding="md">
 *   <div>内容</div>
 * </ResponsiveContainer>
 * ```
 */
export function ResponsiveContainer({
  children,
  className,
  maxWidth = 'xl',
  padding = 'md',
  as: Component = 'div'
}: ResponsiveContainerProps) {
  const { isMobile, isTablet } = useResponsive()

  // 根据设备类型和配置生成内边距类名
  const getPaddingClasses = () => {
    if (padding === 'none') return 'p-0'
    
    if (isMobile) {
      switch (padding) {
        case 'xs': return 'px-1 py-1'
        case 'sm': return 'px-2 py-2'
        case 'md': return 'px-3 py-3'
        case 'lg': return 'px-4 py-4'
        case 'xl': return 'px-6 py-6'
        default: return 'px-3 py-3'
      }
    }
    
    if (isTablet) {
      switch (padding) {
        case 'xs': return 'px-2 py-2'
        case 'sm': return 'px-3 py-3'
        case 'md': return 'px-4 py-4'
        case 'lg': return 'px-6 py-6'
        case 'xl': return 'px-8 py-8'
        default: return 'px-4 py-4'
      }
    }
    
    // 桌面端
    switch (padding) {
      case 'xs': return 'px-2 py-2'
      case 'sm': return 'px-4 py-4'
      case 'md': return 'px-6 py-6'
      case 'lg': return 'px-8 py-8'
      case 'xl': return 'px-12 py-12'
      default: return 'px-6 py-6'
    }
  }

  // 根据设备类型和配置生成类名
  const containerClasses = cn(
    'responsive-container',
    'w-full mx-auto',
    
    // 最大宽度设置
    {
      'max-w-none': maxWidth === 'full',
      'max-w-sm': maxWidth === 'xs',
      'max-w-md': maxWidth === 'sm',
      'max-w-2xl': maxWidth === 'md',
      'max-w-4xl': maxWidth === 'lg',
      'max-w-6xl': maxWidth === 'xl',
      'max-w-7xl': maxWidth === 'xxl',
    },
    
    // 内边距类名
    getPaddingClasses(),
    
    className
  )

  return (
    <Component className={containerClasses}>
      {children}
    </Component>
  )
}

export default ResponsiveContainer