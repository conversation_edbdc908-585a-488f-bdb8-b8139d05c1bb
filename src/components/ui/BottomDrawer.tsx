'use client'

import React, { useEffect, useState, useRef, useCallback } from 'react'
import { Drawer, Button, Typography } from 'antd'
import { CloseOutlined, MoreOutlined } from '@ant-design/icons'
import type { DrawerProps } from 'antd'
import { useResponsive } from '@/hooks/useResponsive'
import { cn } from '@/lib/utils'

const { Title, Text } = Typography

export interface BottomDrawerProps extends Omit<DrawerProps, 'placement' | 'height'> {
  /**
   * 抽屉高度配置
   */
  height?: {
    /**
     * 初始高度（vh单位或像素）
     */
    initial?: string | number
    
    /**
     * 最小高度
     */
    min?: string | number
    
    /**
     * 最大高度
     */
    max?: string | number
    
    /**
     * 是否可调整高度
     */
    resizable?: boolean
  }
  
  /**
   * 头部配置
   */
  header?: {
    /**
     * 是否显示头部
     */
    show?: boolean
    
    /**
     * 标题
     */
    title?: React.ReactNode
    
    /**
     * 副标题
     */
    subtitle?: React.ReactNode
    
    /**
     * 是否显示拖拽指示器
     */
    showHandle?: boolean
    
    /**
     * 是否显示关闭按钮
     */
    showCloseButton?: boolean
    
    /**
     * 额外内容
     */
    extra?: React.ReactNode
    
    /**
     * 头部样式
     */
    style?: React.CSSProperties
    
    /**
     * 头部类名
     */
    className?: string
  }
  
  /**
   * 内容配置
   */
  body?: {
    /**
     * 是否可滚动
     */
    scrollable?: boolean
    
    /**
     * 内容样式
     */
    style?: React.CSSProperties
    
    /**
     * 内容类名
     */
    className?: string
    
    /**
     * 内容padding
     */
    padding?: boolean | string | number
  }
  
  /**
   * 手势配置
   */
  gesture?: {
    /**
     * 是否启用拖拽调整高度
     */
    enableResize?: boolean
    
    /**
     * 是否启用下拉关闭
     */
    enableSwipeToClose?: boolean
    
    /**
     * 关闭阈值（像素）
     */
    closeThreshold?: number
    
    /**
     * 调整高度的阻尼系数
     */
    damping?: number
  }
  
  /**
   * 快照点配置（预设高度）
   */
  snapPoints?: Array<{
    /**
     * 高度值
     */
    height: string | number
    
    /**
     * 标签
     */
    label?: string
    
    /**
     * 是否为默认点
     */
    default?: boolean
  }>
  
  /**
   * 高度变化回调
   */
  onHeightChange?: (height: number) => void
  
  /**
   * 快照点变化回调
   */
  onSnapPointChange?: (snapPoint: { height: string | number; label?: string }) => void
}

/**
 * 底部抽屉组件
 * 支持手势拖拽、高度调整、快照点等移动端交互
 */
export function BottomDrawer({
  height = {
    initial: '50vh',
    min: '20vh',
    max: '90vh',
    resizable: true
  },
  header = {
    show: true,
    showHandle: true,
    showCloseButton: true
  },
  body = {
    scrollable: true,
    padding: true
  },
  gesture = {
    enableResize: true,
    enableSwipeToClose: true,
    closeThreshold: 100,
    damping: 0.8
  },
  snapPoints = [],
  onHeightChange,
  onSnapPointChange,
  className,
  children,
  onClose,
  ...drawerProps
}: BottomDrawerProps) {
  const { isMobile } = useResponsive()
  const [currentHeight, setCurrentHeight] = useState<string | number>(height.initial || '50vh')
  const [isDragging, setIsDragging] = useState(false)
  const [dragStartY, setDragStartY] = useState(0)
  const [dragStartHeight, setDragStartHeight] = useState(0)
  const drawerRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  // 转换高度值为像素
  const convertHeightToPixels = useCallback((heightValue: string | number): number => {
    if (typeof heightValue === 'number') return heightValue
    
    if (heightValue.endsWith('vh')) {
      const vh = parseFloat(heightValue)
      return (window.innerHeight * vh) / 100
    }
    
    if (heightValue.endsWith('px')) {
      return parseFloat(heightValue)
    }
    
    return parseFloat(heightValue)
  }, [])

  // 获取最近的快照点
  const getNearestSnapPoint = useCallback((targetHeight: number) => {
    if (snapPoints.length === 0) return null

    let nearest = snapPoints[0]
    let minDistance = Math.abs(convertHeightToPixels(nearest.height) - targetHeight)

    snapPoints.forEach(point => {
      const distance = Math.abs(convertHeightToPixels(point.height) - targetHeight)
      if (distance < minDistance) {
        minDistance = distance
        nearest = point
      }
    })

    return nearest
  }, [snapPoints, convertHeightToPixels])

  // 处理拖拽开始
  const handleDragStart = useCallback((clientY: number) => {
    if (!gesture.enableResize && !gesture.enableSwipeToClose) return

    setIsDragging(true)
    setDragStartY(clientY)
    setDragStartHeight(convertHeightToPixels(currentHeight))
  }, [gesture.enableResize, gesture.enableSwipeToClose, currentHeight, convertHeightToPixels])

  // 处理拖拽移动
  const handleDragMove = useCallback((clientY: number) => {
    if (!isDragging) return

    const deltaY = dragStartY - clientY
    const damping = gesture.damping || 0.8
    const newHeight = dragStartHeight + (deltaY * damping)

    // 限制高度范围
    const minHeight = convertHeightToPixels(height.min || '20vh')
    const maxHeight = convertHeightToPixels(height.max || '90vh')
    const clampedHeight = Math.max(minHeight, Math.min(maxHeight, newHeight))

    setCurrentHeight(`${clampedHeight}px`)
    onHeightChange?.(clampedHeight)
  }, [isDragging, dragStartY, dragStartHeight, gesture.damping, height.min, height.max, convertHeightToPixels, onHeightChange])

  // 处理拖拽结束
  const handleDragEnd = useCallback((clientY: number) => {
    if (!isDragging) return

    setIsDragging(false)
    
    const deltaY = dragStartY - clientY
    const currentHeightPx = convertHeightToPixels(currentHeight)

    // 检查是否应该关闭
    if (gesture.enableSwipeToClose && deltaY < -(gesture.closeThreshold || 100)) {
      onClose?.({} as any)
      return
    }

    // 查找最近的快照点
    const nearestSnapPoint = getNearestSnapPoint(currentHeightPx)
    if (nearestSnapPoint) {
      setCurrentHeight(nearestSnapPoint.height)
      onSnapPointChange?.(nearestSnapPoint)
      onHeightChange?.(convertHeightToPixels(nearestSnapPoint.height))
    }
  }, [isDragging, dragStartY, currentHeight, gesture.enableSwipeToClose, gesture.closeThreshold, onClose, getNearestSnapPoint, onSnapPointChange, onHeightChange, convertHeightToPixels])

  // 鼠标事件处理
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    handleDragStart(e.clientY)
  }

  const handleMouseMove = useCallback((e: MouseEvent) => {
    handleDragMove(e.clientY)
  }, [handleDragMove])

  const handleMouseUp = useCallback((e: MouseEvent) => {
    handleDragEnd(e.clientY)
  }, [handleDragEnd])

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0]
    handleDragStart(touch.clientY)
  }

  const handleTouchMove = useCallback((e: TouchEvent) => {
    e.preventDefault()
    const touch = e.touches[0]
    handleDragMove(touch.clientY)
  }, [handleDragMove])

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    const touch = e.changedTouches[0]
    handleDragEnd(touch.clientY)
  }, [handleDragEnd])

  // 绑定全局事件
  useEffect(() => {
    if (!isDragging) return

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.addEventListener('touchmove', handleTouchMove, { passive: false })
    document.addEventListener('touchend', handleTouchEnd)

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('touchmove', handleTouchMove)
      document.removeEventListener('touchend', handleTouchEnd)
    }
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd])

  // 渲染拖拽手柄
  const renderHandle = () => {
    if (!header.show || !header.showHandle) return null

    return (
      <div 
        className="bottom-drawer-handle flex justify-center py-2 cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
      >
        <div className="w-10 h-1 bg-gray-300 rounded-full" />
      </div>
    )
  }

  // 渲染头部
  const renderHeader = () => {
    if (!header.show) return null

    return (
      <div 
        className={cn(
          'bottom-drawer-header',
          'px-4 py-3 border-b border-gray-200 bg-white',
          header.className
        )}
        style={header.style}
      >
        {renderHandle()}
        
        {(header.title || header.subtitle || header.extra || header.showCloseButton) && (
          <div className="flex items-center justify-between">
            {/* 左侧：标题区域 */}
            <div className="flex-1 min-w-0">
              {header.title && (
                <div className="mb-1">
                  {typeof header.title === 'string' ? (
                    <Title level={4} className="mb-0">
                      {header.title}
                    </Title>
                  ) : (
                    header.title
                  )}
                </div>
              )}
              
              {header.subtitle && (
                <div>
                  {typeof header.subtitle === 'string' ? (
                    <Text type="secondary" className="text-sm">
                      {header.subtitle}
                    </Text>
                  ) : (
                    header.subtitle
                  )}
                </div>
              )}
            </div>

            {/* 右侧：额外内容和关闭按钮 */}
            <div className="flex items-center space-x-2">
              {header.extra}
              
              {header.showCloseButton && (
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  onClick={() => onClose?.({} as any)}
                  className="touch-target"
                  aria-label="关闭"
                />
              )}
            </div>
          </div>
        )}
      </div>
    )
  }

  // 渲染内容
  const renderBody = () => {
    const bodyPadding = typeof body.padding === 'boolean' 
      ? (body.padding ? 'p-4' : '') 
      : typeof body.padding === 'string' 
        ? body.padding 
        : body.padding 
          ? `p-${body.padding}` 
          : 'p-4'

    return (
      <div 
        ref={contentRef}
        className={cn(
          'bottom-drawer-body',
          'flex-1',
          body.scrollable ? 'overflow-y-auto' : 'overflow-hidden',
          bodyPadding,
          body.className
        )}
        style={body.style}
      >
        {children}
      </div>
    )
  }

  return (
    <Drawer
      {...drawerProps}
      placement="bottom"
      height={currentHeight}
      className={cn(
        'bottom-drawer',
        isDragging ? 'dragging' : '',
        className
      )}
      onClose={onClose}
      closeIcon={null}
      styles={{
        body: { 
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
          height: '100%'
        },
        ...drawerProps.styles
      }}
    >
      <div 
        ref={drawerRef}
        className="flex flex-col h-full bg-white"
      >
        {renderHeader()}
        {renderBody()}
      </div>
    </Drawer>
  )
}

// 导出相关类型
export type { BottomDrawerProps }