'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Table, TableProps, Typography, Button } from 'antd'
import { LeftOutlined, RightOutlined } from '@ant-design/icons'
import { useResponsive } from '@/hooks/useResponsive'
import { debounce } from '@/lib/utils'

const { Text } = Typography

interface MobileTableProps extends TableProps<any> {
  /**
   * 是否显示滚动提示
   */
  showScrollHint?: boolean
  
  /**
   * 是否显示滚动指示器
   */
  showScrollIndicator?: boolean
  
  /**
   * 是否启用触摸友好的操作按钮
   */
  touchFriendlyActions?: boolean
  
  /**
   * 滚动性能优化配置
   */
  scrollConfig?: {
    /**
     * 防抖延迟时间（毫秒）
     */
    debounceDelay?: number
    
    /**
     * 是否启用平滑滚动
     */
    smoothScroll?: boolean
    
    /**
     * 滚动步长（像素）
     */
    scrollStep?: number
  }
}

export function MobileTable({ 
  showScrollHint = true,
  showScrollIndicator = true,
  touchFriendlyActions = true,
  scrollConfig = {
    debounceDelay: 100,
    smoothScroll: true,
    scrollStep: 200
  },
  className = '', 
  ...props 
}: MobileTableProps) {
  const { isMobile, isTablet } = useResponsive()
  const tableRef = useRef<HTMLDivElement>(null)
  const scrollContainerRef = useRef<HTMLElement | null>(null)
  
  // 滚动状态
  const [scrollState, setScrollState] = useState({
    isScrolled: false,
    isScrolledToRight: false,
    canScrollLeft: false,
    canScrollRight: false,
    scrollPercentage: 0
  })

  // 防抖的滚动处理函数
  const debouncedHandleScroll = useCallback(
    debounce(() => {
      if (!scrollContainerRef.current) return

      const container = scrollContainerRef.current
      const { scrollLeft, scrollWidth, clientWidth } = container
      
      const maxScrollLeft = scrollWidth - clientWidth
      const scrollPercentage = maxScrollLeft > 0 ? (scrollLeft / maxScrollLeft) * 100 : 0
      
      setScrollState({
        isScrolled: scrollLeft > 0,
        isScrolledToRight: scrollLeft >= maxScrollLeft - 1,
        canScrollLeft: scrollLeft > 0,
        canScrollRight: scrollLeft < maxScrollLeft - 1,
        scrollPercentage
      })
    }, scrollConfig.debounceDelay || 100),
    [scrollConfig.debounceDelay]
  )

  // 初始化滚动监听
  useEffect(() => {
    const tableWrapper = tableRef.current?.querySelector('.ant-table-body') as HTMLElement
    if (!tableWrapper) return

    scrollContainerRef.current = tableWrapper
    
    // 初始检查
    debouncedHandleScroll()

    // 添加滚动监听
    tableWrapper.addEventListener('scroll', debouncedHandleScroll, { passive: true })
    
    // 添加窗口大小变化监听
    const handleResize = debounce(() => {
      debouncedHandleScroll()
    }, 200)
    
    window.addEventListener('resize', handleResize)

    return () => {
      tableWrapper.removeEventListener('scroll', debouncedHandleScroll)
      window.removeEventListener('resize', handleResize)
    }
  }, [debouncedHandleScroll])

  // 滚动控制函数
  const scrollTo = useCallback((direction: 'left' | 'right') => {
    if (!scrollContainerRef.current) return

    const container = scrollContainerRef.current
    const scrollStep = scrollConfig.scrollStep || 200
    const targetScrollLeft = direction === 'left' 
      ? Math.max(0, container.scrollLeft - scrollStep)
      : Math.min(container.scrollWidth - container.clientWidth, container.scrollLeft + scrollStep)

    if (scrollConfig.smoothScroll) {
      container.scrollTo({
        left: targetScrollLeft,
        behavior: 'smooth'
      })
    } else {
      container.scrollLeft = targetScrollLeft
    }
  }, [scrollConfig.smoothScroll, scrollConfig.scrollStep])

  // 构建表格类名
  const tableClassName = [
    'mobile-table-enhanced',
    scrollState.isScrolled ? 'scrolled' : '',
    scrollState.isScrolledToRight ? 'scrolled-to-right' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  // 渲染滚动指示器
  const renderScrollIndicator = () => {
    if (!showScrollIndicator || !isMobile) return null

    return (
      <div className="mobile-table-scroll-indicator">
        {/* 左滚动按钮 */}
        <Button
          type="text"
          icon={<LeftOutlined />}
          size="small"
          className={`mobile-table-scroll-btn mobile-table-scroll-left ${
            !scrollState.canScrollLeft ? 'disabled' : ''
          }`}
          disabled={!scrollState.canScrollLeft}
          onClick={() => scrollTo('left')}
        />
        
        {/* 滚动进度条 */}
        <div className="mobile-table-scroll-progress">
          <div className="mobile-table-scroll-track">
            <div 
              className="mobile-table-scroll-thumb"
              style={{ width: `${Math.max(10, scrollState.scrollPercentage)}%` }}
            />
          </div>
        </div>
        
        {/* 右滚动按钮 */}
        <Button
          type="text"
          icon={<RightOutlined />}
          size="small"
          className={`mobile-table-scroll-btn mobile-table-scroll-right ${
            !scrollState.canScrollRight ? 'disabled' : ''
          }`}
          disabled={!scrollState.canScrollRight}
          onClick={() => scrollTo('right')}
        />
      </div>
    )
  }

  // 渲染滚动提示
  const renderScrollHint = () => {
    if (!showScrollHint || !isMobile || scrollState.isScrolled) return null

    return (
      <div className="mobile-table-scroll-hint">
        <Text type="secondary" className="text-xs">
          ← 左右滑动查看更多内容 →
        </Text>
      </div>
    )
  }

  return (
    <div ref={tableRef} className="mobile-table-container">
      {/* 滚动指示器 */}
      {renderScrollIndicator()}
      
      {/* 表格主体 */}
      <Table
        {...props}
        className={tableClassName}
        scroll={{
          x: 'max-content',
          scrollToFirstRowOnChange: true,
          ...props.scroll
        }}
        pagination={
          isMobile && props.pagination
            ? {
                ...props.pagination,
                size: 'small',
                showSizeChanger: false,
                showQuickJumper: false,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} / ${total}`,
                className: 'mobile-table-pagination'
              }
            : props.pagination
        }
      />
      
      {/* 滚动提示 */}
      {renderScrollHint()}
    </div>
  )
}
