'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Form, FormProps, FormInstance } from 'antd'
import { useResponsive } from '@/hooks/useResponsive'

export interface MobileFormProps extends FormProps {
  /**
   * 是否启用虚拟键盘适配
   */
  enableKeyboardAdaptation?: boolean
  
  /**
   * 是否启用自动滚动到聚焦字段
   */
  enableAutoScroll?: boolean
  
  /**
   * 虚拟键盘弹出时的额外偏移量
   */
  keyboardOffset?: number
  
  /**
   * 滚动动画持续时间
   */
  scrollDuration?: number
  
  /**
   * 是否启用表单字段间的智能导航
   */
  enableSmartNavigation?: boolean
  
  /**
   * 虚拟键盘状态变化回调
   */
  onKeyboardToggle?: (visible: boolean, height: number) => void
  
  /**
   * 表单实例引用
   */
  formRef?: React.RefObject<FormInstance>
}

/**
 * 移动端优化的表单组件
 * 处理虚拟键盘弹出、视口调整、自动滚动等移动端特有问题
 */
export function MobileForm({
  enableKeyboardAdaptation = true,
  enableAutoScroll = true,
  keyboardOffset = 20,
  scrollDuration = 300,
  enableSmartNavigation = true,
  onKeyboardToggle,
  formRef,
  className = '',
  children,
  ...formProps
}: MobileFormProps) {
  const { isMobile, deviceInfo } = useResponsive()
  const [form] = Form.useForm(formProps.form)
  const formContainerRef = useRef<HTMLDivElement>(null)
  const activeFieldRef = useRef<HTMLElement | null>(null)
  
  // 虚拟键盘状态
  const [keyboardState, setKeyboardState] = useState({
    visible: false,
    height: 0,
    initialViewportHeight: 0
  })

  // 获取初始视口高度
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setKeyboardState(prev => ({
        ...prev,
        initialViewportHeight: window.innerHeight
      }))
    }
  }, [])

  // 检测虚拟键盘状态
  const detectKeyboard = useCallback(() => {
    if (!isMobile || !enableKeyboardAdaptation) return

    const currentHeight = window.innerHeight
    const heightDiff = keyboardState.initialViewportHeight - currentHeight
    const threshold = 150 // 键盘高度阈值
    
    const isKeyboardVisible = heightDiff > threshold
    const keyboardHeight = isKeyboardVisible ? heightDiff : 0

    if (isKeyboardVisible !== keyboardState.visible) {
      setKeyboardState(prev => ({
        ...prev,
        visible: isKeyboardVisible,
        height: keyboardHeight
      }))
      
      onKeyboardToggle?.(isKeyboardVisible, keyboardHeight)
    }
  }, [isMobile, enableKeyboardAdaptation, keyboardState.initialViewportHeight, keyboardState.visible, onKeyboardToggle])

  // 监听视口变化
  useEffect(() => {
    if (!isMobile || !enableKeyboardAdaptation) return

    let timeoutId: NodeJS.Timeout

    const handleResize = () => {
      // 防抖处理，避免频繁触发
      clearTimeout(timeoutId)
      timeoutId = setTimeout(detectKeyboard, 100)
    }

    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        const heightDiff = keyboardState.initialViewportHeight - window.visualViewport.height
        const isKeyboardVisible = heightDiff > 150
        const keyboardHeight = isKeyboardVisible ? heightDiff : 0

        setKeyboardState(prev => ({
          ...prev,
          visible: isKeyboardVisible,
          height: keyboardHeight
        }))
        
        onKeyboardToggle?.(isKeyboardVisible, keyboardHeight)
      }
    }

    // 优先使用 Visual Viewport API
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleVisualViewportChange)
    } else {
      window.addEventListener('resize', handleResize)
    }

    return () => {
      clearTimeout(timeoutId)
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleVisualViewportChange)
      } else {
        window.removeEventListener('resize', handleResize)
      }
    }
  }, [isMobile, enableKeyboardAdaptation, detectKeyboard, keyboardState.initialViewportHeight, onKeyboardToggle])

  // 滚动到指定元素
  const scrollToElement = useCallback((element: HTMLElement) => {
    if (!enableAutoScroll || !element) return

    const container = formContainerRef.current
    if (!container) return

    const elementRect = element.getBoundingClientRect()
    const containerRect = container.getBoundingClientRect()
    
    // 计算需要滚动的距离
    const scrollTop = elementRect.top - containerRect.top - keyboardOffset
    
    // 如果有虚拟键盘，需要额外考虑键盘高度
    const adjustedScrollTop = keyboardState.visible 
      ? scrollTop - (keyboardState.height / 2)
      : scrollTop

    // 平滑滚动
    container.scrollTo({
      top: container.scrollTop + adjustedScrollTop,
      behavior: 'smooth'
    })
  }, [enableAutoScroll, keyboardOffset, keyboardState.visible, keyboardState.height])

  // 处理字段聚焦
  const handleFieldFocus = useCallback((event: FocusEvent) => {
    const target = event.target as HTMLElement
    activeFieldRef.current = target

    // 延迟滚动，等待虚拟键盘完全弹出
    setTimeout(() => {
      scrollToElement(target)
    }, 300)
  }, [scrollToElement])

  // 处理字段失焦
  const handleFieldBlur = useCallback(() => {
    activeFieldRef.current = null
  }, [])

  // 绑定表单字段事件
  useEffect(() => {
    if (!isMobile || !formContainerRef.current) return

    const container = formContainerRef.current
    const inputs = container.querySelectorAll('input, textarea, select')

    inputs.forEach(input => {
      input.addEventListener('focus', handleFieldFocus as EventListener)
      input.addEventListener('blur', handleFieldBlur)
    })

    return () => {
      inputs.forEach(input => {
        input.removeEventListener('focus', handleFieldFocus as EventListener)
        input.removeEventListener('blur', handleFieldBlur)
      })
    }
  }, [isMobile, handleFieldFocus, handleFieldBlur, children])

  // 智能导航 - 下一个/上一个字段
  const navigateToField = useCallback((direction: 'next' | 'prev') => {
    if (!enableSmartNavigation || !formContainerRef.current) return

    const container = formContainerRef.current
    const inputs = Array.from(container.querySelectorAll('input, textarea, select')) as HTMLElement[]
    const currentIndex = activeFieldRef.current 
      ? inputs.indexOf(activeFieldRef.current)
      : -1

    let targetIndex = -1
    if (direction === 'next') {
      targetIndex = currentIndex < inputs.length - 1 ? currentIndex + 1 : 0
    } else {
      targetIndex = currentIndex > 0 ? currentIndex - 1 : inputs.length - 1
    }

    if (targetIndex >= 0 && inputs[targetIndex]) {
      inputs[targetIndex].focus()
    }
  }, [enableSmartNavigation])

  // 暴露导航方法
  useEffect(() => {
    if (formRef && formRef.current) {
      // 扩展表单实例的方法
      (formRef.current as any).navigateToNextField = () => navigateToField('next')
      ;(formRef.current as any).navigateToPrevField = () => navigateToField('prev')
    }
  }, [formRef, navigateToField])

  // 构建容器类名
  const containerClassName = [
    'mobile-form-container',
    keyboardState.visible ? 'keyboard-visible' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  // 构建表单样式
  const formStyle: React.CSSProperties = {
    ...formProps.style,
    ...(keyboardState.visible && isMobile ? {
      paddingBottom: keyboardOffset
    } : {})
  }

  return (
    <div 
      ref={formContainerRef}
      className={containerClassName}
      style={{
        position: 'relative',
        ...(keyboardState.visible && isMobile ? {
          minHeight: `calc(100vh - ${keyboardState.height}px)`
        } : {})
      }}
    >
      <Form
        {...formProps}
        form={formRef?.current || form}
        className={`mobile-form ${formProps.className || ''}`}
        style={formStyle}
        layout={isMobile ? 'vertical' : formProps.layout}
        scrollToFirstError={enableAutoScroll}
      >
        {children}
      </Form>
      
      {/* 虚拟键盘占位符 */}
      {keyboardState.visible && isMobile && (
        <div 
          className="keyboard-placeholder"
          style={{ height: keyboardState.height }}
        />
      )}
    </div>
  )
}

// 导出相关类型和工具函数
export type { MobileFormProps }

/**
 * 移动端表单工具函数
 */
export const MobileFormUtils = {
  /**
   * 检测是否支持 Visual Viewport API
   */
  supportsVisualViewport: () => {
    return typeof window !== 'undefined' && 'visualViewport' in window
  },
  
  /**
   * 获取安全区域信息
   */
  getSafeAreaInsets: () => {
    if (typeof window === 'undefined') return { top: 0, bottom: 0, left: 0, right: 0 }
    
    const style = getComputedStyle(document.documentElement)
    return {
      top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
      bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0'),
      right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0')
    }
  },
  
  /**
   * 防止iOS缩放
   */
  preventIOSZoom: () => {
    if (typeof document === 'undefined') return
    
    const viewport = document.querySelector('meta[name="viewport"]')
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      )
    }
  }
}