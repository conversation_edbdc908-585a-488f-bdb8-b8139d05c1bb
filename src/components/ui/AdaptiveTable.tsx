'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Table, Card, Space, Button, Typography, Divider } from 'antd'
import { TableOutlined, AppstoreOutlined, FilterOutlined } from '@ant-design/icons'
import type { TableProps, ColumnsType } from 'antd/es/table'
import { useResponsive } from '@/hooks/useResponsive'
import { MobileCard } from './MobileCard'

const { Text } = Typography

export interface AdaptiveTableColumn<T = any> extends Omit<ColumnsType<T>[0], 'render'> {
  /**
   * 移动端显示优先级 (1-5, 1为最高优先级)
   */
  mobilePriority?: 1 | 2 | 3 | 4 | 5
  
  /**
   * 是否在移动端卡片视图中显示
   */
  showInCard?: boolean
  
  /**
   * 移动端卡片中的显示标签
   */
  cardLabel?: string
  
  /**
   * 自定义渲染函数
   */
  render?: (value: any, record: T, index: number) => React.ReactNode
  
  /**
   * 移动端卡片中的自定义渲染函数
   */
  cardRender?: (value: any, record: T, index: number) => React.ReactNode
}

export interface AdaptiveTableProps<T = any> extends Omit<TableProps<T>, 'columns'> {
  /**
   * 表格列配置
   */
  columns: AdaptiveTableColumn<T>[]
  
  /**
   * 是否允许用户切换视图模式
   */
  allowViewToggle?: boolean
  
  /**
   * 默认视图模式 ('auto' | 'table' | 'card')
   */
  defaultViewMode?: 'auto' | 'table' | 'card'
  
  /**
   * 移动端卡片配置
   */
  cardConfig?: {
    /**
     * 卡片标题字段
     */
    titleField?: string
    
    /**
     * 卡片副标题字段
     */
    subtitleField?: string
    
    /**
     * 卡片描述字段
     */
    descriptionField?: string
    
    /**
     * 自定义卡片渲染函数
     */
    customRender?: (record: T, index: number) => React.ReactNode
  }
  
  /**
   * 表格滚动配置
   */
  scrollConfig?: {
    /**
     * 是否显示滚动提示
     */
    showScrollHint?: boolean
    
    /**
     * 是否显示滚动指示器
     */
    showScrollIndicator?: boolean
  }
}

/**
 * 自适应表格组件
 * 根据屏幕尺寸自动切换表格和卡片视图
 */
export function AdaptiveTable<T extends Record<string, any>>({
  columns,
  dataSource = [],
  allowViewToggle = true,
  defaultViewMode = 'auto',
  cardConfig,
  scrollConfig = {
    showScrollHint: true,
    showScrollIndicator: true
  },
  className = '',
  ...tableProps
}: AdaptiveTableProps<T>) {
  const { isMobile, isTablet } = useResponsive()
  const [viewMode, setViewMode] = useState<'table' | 'card'>(
    defaultViewMode === 'auto' ? (isMobile ? 'card' : 'table') : 
    defaultViewMode === 'card' ? 'card' : 'table'
  )

  // 根据屏幕尺寸自动调整视图模式
  useEffect(() => {
    if (defaultViewMode === 'auto') {
      setViewMode(isMobile ? 'card' : 'table')
    }
  }, [isMobile, defaultViewMode])

  // 处理表格列的移动端优化
  const optimizedColumns = useMemo(() => {
    if (viewMode === 'card') return columns

    // 根据屏幕尺寸和优先级过滤列
    let visibleColumns = [...columns]

    if (isMobile) {
      // 移动端只显示优先级1-2的列
      visibleColumns = columns.filter(col => 
        !col.mobilePriority || col.mobilePriority <= 2
      )
    } else if (isTablet) {
      // 平板端显示优先级1-3的列
      visibleColumns = columns.filter(col => 
        !col.mobilePriority || col.mobilePriority <= 3
      )
    }

    // 确保至少显示一列
    if (visibleColumns.length === 0) {
      visibleColumns = [columns[0]].filter(Boolean)
    }

    return visibleColumns
  }, [columns, viewMode, isMobile, isTablet])

  // 渲染视图切换按钮
  const renderViewToggle = () => {
    if (!allowViewToggle || defaultViewMode !== 'auto') return null

    return (
      <Space.Compact>
        <Button
          type={viewMode === 'table' ? 'primary' : 'default'}
          icon={<TableOutlined />}
          onClick={() => setViewMode('table')}
          size="small"
        >
          表格
        </Button>
        <Button
          type={viewMode === 'card' ? 'primary' : 'default'}
          icon={<AppstoreOutlined />}
          onClick={() => setViewMode('card')}
          size="small"
        >
          卡片
        </Button>
      </Space.Compact>
    )
  }

  // 渲染表格视图
  const renderTableView = () => {
    return (
      <div className={`adaptive-table-wrapper ${className}`}>
        <Table
          {...tableProps}
          columns={optimizedColumns}
          dataSource={dataSource}
          className={`adaptive-table ${isMobile ? 'mobile-optimized' : ''}`}
          scroll={{
            x: isMobile ? 'max-content' : undefined,
            scrollToFirstRowOnChange: true,
            ...tableProps.scroll
          }}
          pagination={
            isMobile
              ? {
                  ...tableProps.pagination,
                  size: 'small',
                  showSizeChanger: false,
                  showQuickJumper: false,
                  showTotal: (total, range) => 
                    `${range[0]}-${range[1]} / ${total}`
                }
              : tableProps.pagination
          }
        />
        
        {/* 移动端滚动提示 */}
        {isMobile && scrollConfig.showScrollHint && (
          <div className="mobile-scroll-hint">
            <Text type="secondary" className="text-xs">
              ← 左右滑动查看更多内容 →
            </Text>
          </div>
        )}
      </div>
    )
  }

  // 渲染卡片视图
  const renderCardView = () => {
    if (!dataSource || dataSource.length === 0) {
      return (
        <div className="adaptive-table-empty">
          <Text type="secondary">暂无数据</Text>
        </div>
      )
    }

    return (
      <div className={`adaptive-card-view ${className}`}>
        <Space direction="vertical" size="middle" className="w-full">
          {dataSource.map((record, index) => (
            <MobileCard
              key={record.key || index}
              data={record}
              columns={columns}
              config={cardConfig}
              index={index}
            />
          ))}
        </Space>
        
        {/* 分页器 */}
        {tableProps.pagination && (
          <div className="mt-4 text-center">
            <div className="inline-block">
              {/* 这里可以添加自定义的移动端分页组件 */}
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="adaptive-table-container">
      {/* 工具栏 */}
      {(allowViewToggle || tableProps.title) && (
        <div className="adaptive-table-toolbar mb-4">
          <div className="flex justify-between items-center">
            <div>
              {typeof tableProps.title === 'function' 
                ? tableProps.title(dataSource) 
                : tableProps.title
              }
            </div>
            <div>
              {renderViewToggle()}
            </div>
          </div>
        </div>
      )}

      {/* 主要内容 */}
      {viewMode === 'table' ? renderTableView() : renderCardView()}
    </div>
  )
}