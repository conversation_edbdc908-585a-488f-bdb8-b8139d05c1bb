'use client'

import React, { useState } from 'react'
import { Card, Space, Typography, Divider, message } from 'antd'
import { 
  HeartOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  ShareAltOutlined,
  MoreOutlined,
  StarOutlined,
  MessageOutlined
} from '@ant-design/icons'
import { TouchButton, TouchCard } from './TouchButton'
import { SwipeAction } from './SwipeAction'
import { LongPressMenu } from './LongPressMenu'
import { useResponsive } from '@/hooks/useResponsive'

const { Title, Text } = Typography

export function TouchInteractionDemo() {
  const { isMobile } = useResponsive()
  const [feedback, setFeedback] = useState<string>('')

  const showFeedback = (msg: string) => {
    setFeedback(msg)
    message.info(msg)
    setTimeout(() => setFeedback(''), 2000)
  }

  const cardActions = [
    {
      key: 'edit',
      label: '编辑',
      icon: <EditOutlined />,
      onClick: () => showFeedback('编辑操作')
    },
    {
      key: 'share',
      label: '分享',
      icon: <ShareAltOutlined />,
      onClick: () => showFeedback('分享操作')
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => showFeedback('删除操作')
    }
  ]

  const swipeLeftActions = [
    {
      key: 'star',
      label: '收藏',
      icon: <StarOutlined />,
      color: 'warning' as const,
      onClick: () => showFeedback('已收藏')
    },
    {
      key: 'share',
      label: '分享',
      icon: <ShareAltOutlined />,
      color: 'primary' as const,
      onClick: () => showFeedback('已分享')
    }
  ]

  const swipeRightActions = [
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      color: 'danger' as const,
      onClick: () => showFeedback('已删除')
    }
  ]

  return (
    <div className="p-4 space-y-6">
      <Title level={3}>触摸交互演示</Title>
      
      {feedback && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-blue-700">
          反馈: {feedback}
        </div>
      )}

      {/* 触摸按钮演示 */}
      <Card title="触摸按钮" size="small">
        <Space direction="vertical" className="w-full">
          <Text>不同尺寸的触摸按钮:</Text>
          <Space wrap>
            <TouchButton 
              touchSize="small"
              onClick={() => showFeedback('小按钮点击')}
              onLongPress={() => showFeedback('小按钮长按')}
              onDoubleClick={() => showFeedback('小按钮双击')}
              enableLongPress
            >
              小按钮
            </TouchButton>
            
            <TouchButton 
              touchSize="medium"
              type="primary"
              onClick={() => showFeedback('中按钮点击')}
              onLongPress={() => showFeedback('中按钮长按')}
              onDoubleClick={() => showFeedback('中按钮双击')}
              enableLongPress
            >
              中按钮
            </TouchButton>
            
            <TouchButton 
              touchSize="large"
              type="primary"
              danger
              onClick={() => showFeedback('大按钮点击')}
              onLongPress={() => showFeedback('大按钮长按')}
              onDoubleClick={() => showFeedback('大按钮双击')}
              enableLongPress
            >
              大按钮
            </TouchButton>
          </Space>
          
          <Text type="secondary">
            {isMobile ? '在移动端' : '在桌面端'}尝试点击、长按和双击按钮
          </Text>
        </Space>
      </Card>

      {/* 触摸反馈强度演示 */}
      <Card title="触摸反馈强度" size="small">
        <Space wrap>
          <TouchButton 
            feedbackIntensity="light"
            onClick={() => showFeedback('轻微反馈')}
          >
            轻微反馈
          </TouchButton>
          
          <TouchButton 
            feedbackIntensity="medium"
            type="primary"
            onClick={() => showFeedback('中等反馈')}
          >
            中等反馈
          </TouchButton>
          
          <TouchButton 
            feedbackIntensity="strong"
            type="primary"
            danger
            onClick={() => showFeedback('强烈反馈')}
          >
            强烈反馈
          </TouchButton>
        </Space>
      </Card>

      {/* 触摸卡片演示 */}
      <Card title="触摸卡片" size="small">
        <Space direction="vertical" className="w-full">
          <TouchCard
            className="p-4 border border-gray-200 rounded-lg"
            onClick={() => showFeedback('卡片点击')}
            onLongPress={() => showFeedback('卡片长按')}
            onDoubleClick={() => showFeedback('卡片双击')}
            feedbackIntensity="light"
          >
            <div className="flex items-center space-x-3">
              <HeartOutlined className="text-red-500 text-xl" />
              <div>
                <div className="font-medium">可交互卡片</div>
                <div className="text-sm text-gray-500">支持点击、长按和双击</div>
              </div>
            </div>
          </TouchCard>
          
          <Text type="secondary">
            尝试点击、长按或双击上面的卡片
          </Text>
        </Space>
      </Card>

      {/* 长按菜单演示 */}
      <Card title="长按菜单" size="small">
        <Space direction="vertical" className="w-full">
          <LongPressMenu
            actions={cardActions}
            onAction={(action) => showFeedback(`菜单操作: ${action}`)}
            className="inline-block"
          >
            <TouchCard
              className="p-4 border border-gray-200 rounded-lg cursor-pointer"
              feedbackIntensity="light"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <MessageOutlined className="text-blue-500 text-xl" />
                  <div>
                    <div className="font-medium">长按显示菜单</div>
                    <div className="text-sm text-gray-500">长按此卡片显示操作菜单</div>
                  </div>
                </div>
                <MoreOutlined className="text-gray-400" />
              </div>
            </TouchCard>
          </LongPressMenu>
          
          <Text type="secondary">
            长按上面的卡片显示操作菜单
          </Text>
        </Space>
      </Card>

      {/* 滑动操作演示 */}
      <Card title="滑动操作" size="small">
        <Space direction="vertical" className="w-full">
          <Text>向左滑动显示收藏和分享，向右滑动显示删除:</Text>
          
          <SwipeAction
            leftActions={swipeLeftActions}
            rightActions={swipeRightActions}
            onAction={(action) => showFeedback(`滑动操作: ${action}`)}
            className="border border-gray-200 rounded-lg overflow-hidden"
          >
            <div className="p-4 bg-white">
              <div className="flex items-center space-x-3">
                <StarOutlined className="text-yellow-500 text-xl" />
                <div>
                  <div className="font-medium">可滑动的项目</div>
                  <div className="text-sm text-gray-500">
                    {isMobile ? '左右滑动' : '使用鼠标拖拽'}查看操作选项
                  </div>
                </div>
              </div>
            </div>
          </SwipeAction>

          <SwipeAction
            leftActions={swipeLeftActions}
            rightActions={swipeRightActions}
            onAction={(action) => showFeedback(`滑动操作: ${action}`)}
            className="border border-gray-200 rounded-lg overflow-hidden"
          >
            <div className="p-4 bg-white">
              <div className="flex items-center space-x-3">
                <HeartOutlined className="text-red-500 text-xl" />
                <div>
                  <div className="font-medium">另一个可滑动项目</div>
                  <div className="text-sm text-gray-500">
                    支持多个滑动操作项目
                  </div>
                </div>
              </div>
            </div>
          </SwipeAction>
          
          <Text type="secondary">
            {isMobile 
              ? '在移动设备上左右滑动项目查看操作选项' 
              : '在桌面端可以使用鼠标拖拽模拟滑动操作'
            }
          </Text>
        </Space>
      </Card>

      <Divider />
      
      <div className="text-center text-gray-500 text-sm">
        <p>触摸交互增强功能演示完成</p>
        <p>包含: 触摸按钮、触摸卡片、长按菜单、滑动操作</p>
      </div>
    </div>
  )
}