'use client'

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import { useResponsive } from '@/hooks/useResponsive'

interface VirtualListProps<T = any> {
  items: T[]
  itemHeight: number | ((index: number, item: T) => number)
  renderItem: (item: T, index: number) => React.ReactNode
  containerHeight?: number
  overscan?: number
  className?: string
  onScroll?: (scrollTop: number) => void
  loading?: boolean
  loadingComponent?: React.ReactNode
  emptyComponent?: React.ReactNode
}

export function VirtualList<T>({
  items,
  itemHeight,
  renderItem,
  containerHeight = 400,
  overscan = 5,
  className = '',
  onScroll,
  loading = false,
  loadingComponent,
  emptyComponent
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)
  const { isMobile } = useResponsive()

  // 计算项目高度
  const getItemHeight = useCallback((index: number, item: T): number => {
    return typeof itemHeight === 'function' ? itemHeight(index, item) : itemHeight
  }, [itemHeight])

  // 计算总高度
  const totalHeight = useMemo(() => {
    return items.reduce((total, item, index) => {
      return total + getItemHeight(index, item)
    }, 0)
  }, [items, getItemHeight])

  // 计算可见范围
  const visibleRange = useMemo(() => {
    if (items.length === 0) return { start: 0, end: 0 }

    let start = 0
    let end = 0
    let accumulatedHeight = 0

    // 找到开始索引
    for (let i = 0; i < items.length; i++) {
      const height = getItemHeight(i, items[i])
      if (accumulatedHeight + height > scrollTop) {
        start = Math.max(0, i - overscan)
        break
      }
      accumulatedHeight += height
    }

    // 找到结束索引
    accumulatedHeight = 0
    for (let i = 0; i < items.length; i++) {
      const height = getItemHeight(i, items[i])
      accumulatedHeight += height
      if (accumulatedHeight > scrollTop + containerHeight) {
        end = Math.min(items.length - 1, i + overscan)
        break
      }
    }

    if (end === 0) end = items.length - 1

    return { start, end }
  }, [items, scrollTop, containerHeight, overscan, getItemHeight])

  // 计算偏移量
  const offsetY = useMemo(() => {
    let offset = 0
    for (let i = 0; i < visibleRange.start; i++) {
      offset += getItemHeight(i, items[i])
    }
    return offset
  }, [visibleRange.start, items, getItemHeight])

  // 处理滚动事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop
    setScrollTop(scrollTop)
    onScroll?.(scrollTop)
  }, [onScroll])

  // 移动端优化的容器高度
  const adaptiveHeight = isMobile ? Math.min(containerHeight, window.innerHeight * 0.6) : containerHeight

  if (loading) {
    return (
      <div 
        className={`virtual-list-container ${className}`}
        style={{ height: adaptiveHeight }}
      >
        {loadingComponent || (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>
    )
  }

  if (items.length === 0) {
    return (
      <div 
        className={`virtual-list-container ${className}`}
        style={{ height: adaptiveHeight }}
      >
        {emptyComponent || (
          <div className="flex items-center justify-center h-full text-gray-500">
            暂无数据
          </div>
        )}
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={`virtual-list-container overflow-auto ${className}`}
      style={{ height: adaptiveHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {items.slice(visibleRange.start, visibleRange.end + 1).map((item, index) => {
            const actualIndex = visibleRange.start + index
            return (
              <div
                key={actualIndex}
                style={{ height: getItemHeight(actualIndex, item) }}
              >
                {renderItem(item, actualIndex)}
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

// 虚拟表格组件
interface VirtualTableProps {
  dataSource: any[]
  columns: Array<{
    key: string
    title: string
    dataIndex: string
    render?: (value: any, record: any, index: number) => React.ReactNode
    width?: number
  }>
  rowHeight?: number
  containerHeight?: number
  loading?: boolean
  className?: string
  onRow?: (record: any, index: number) => React.HTMLAttributes<HTMLTableRowElement>
}

export function VirtualTable({
  dataSource,
  columns,
  rowHeight = 48,
  containerHeight = 400,
  loading = false,
  className = '',
  onRow
}: VirtualTableProps) {
  const { isMobile } = useResponsive()

  const renderRow = useCallback((record: any, index: number) => {
    const rowProps = onRow?.(record, index) || {}
    
    return (
      <div
        {...rowProps}
        className={`flex border-b border-gray-200 hover:bg-gray-50 ${rowProps.className || ''}`}
        style={{ minHeight: rowHeight, ...rowProps.style }}
      >
        {columns.map((column) => {
          const value = record[column.dataIndex]
          const content = column.render ? column.render(value, record, index) : value
          
          return (
            <div
              key={column.key}
              className="flex items-center px-4 py-2 flex-1"
              style={{ 
                width: column.width,
                minWidth: isMobile ? 100 : column.width 
              }}
            >
              {content}
            </div>
          )
        })}
      </div>
    )
  }, [columns, rowHeight, onRow, isMobile])

  return (
    <div className={`virtual-table ${className}`}>
      {/* 表头 */}
      <div className="flex bg-gray-50 border-b-2 border-gray-200 font-medium">
        {columns.map((column) => (
          <div
            key={column.key}
            className="flex items-center px-4 py-3 flex-1"
            style={{ 
              width: column.width,
              minWidth: isMobile ? 100 : column.width 
            }}
          >
            {column.title}
          </div>
        ))}
      </div>
      
      {/* 虚拟列表内容 */}
      <VirtualList
        items={dataSource}
        itemHeight={rowHeight}
        renderItem={renderRow}
        containerHeight={containerHeight}
        loading={loading}
        className="border border-gray-200"
      />
    </div>
  )
}