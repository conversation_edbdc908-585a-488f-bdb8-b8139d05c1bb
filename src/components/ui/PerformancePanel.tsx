'use client'

import React, { useState, useEffect } from 'react'
import { Card, Progress, Button, Tabs, Alert, Tag, Tooltip } from 'antd'
import { 
  ThunderboltOutlined, 
  EyeOutlined, 
  ClockCircleOutlined, 
  MemoryOutlined,
  WifiOutlined,
  ReloadOutlined,
  DownloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import { usePerformance } from '@/hooks/usePerformance'
import { useResponsive } from '@/hooks/useResponsive'

const { TabPane } = Tabs

interface PerformancePanelProps {
  className?: string
  showOptimizations?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

export function PerformancePanel({
  className = '',
  showOptimizations = true,
  autoRefresh = true,
  refreshInterval = 5000
}: PerformancePanelProps) {
  const [performanceState, performanceActions] = usePerformance()
  const { isMobile } = useResponsive()
  const [activeTab, setActiveTab] = useState('metrics')

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      performanceActions.refreshMetrics()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, performanceActions])

  // 获取性能分数颜色
  const getScoreColor = (score: number) => {
    if (score >= 90) return '#52c41a'
    if (score >= 70) return '#faad14'
    if (score >= 50) return '#fa8c16'
    return '#f5222d'
  }

  // 获取性能分数状态
  const getScoreStatus = (score: number): 'success' | 'normal' | 'exception' => {
    if (score >= 90) return 'success'
    if (score >= 50) return 'normal'
    return 'exception'
  }

  // 格式化字节大小
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化时间
  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`
    return `${(ms / 1000).toFixed(2)}s`
  }

  // 渲染性能指标
  const renderMetrics = () => {
    const { metrics } = performanceState
    const score = performanceActions.getPerformanceScore()

    return (
      <div className="space-y-4">
        {/* 总体性能分数 */}
        <Card size="small" title="总体性能分数">
          <div className="flex items-center justify-between">
            <Progress
              type="circle"
              percent={score}
              size={isMobile ? 80 : 120}
              strokeColor={getScoreColor(score)}
              status={getScoreStatus(score)}
            />
            <div className="ml-4 flex-1">
              <div className="text-lg font-semibold">{score}/100</div>
              <div className="text-sm text-gray-500">
                {score >= 90 ? '优秀' : score >= 70 ? '良好' : score >= 50 ? '需要改进' : '较差'}
              </div>
            </div>
          </div>
        </Card>

        {/* 核心Web指标 */}
        <Card size="small" title="核心Web指标">
          <div className="space-y-3">
            {metrics.pageLoad?.firstContentfulPaint && (
              <div className="flex justify-between items-center">
                <span className="flex items-center">
                  <EyeOutlined className="mr-2" />
                  首次内容绘制 (FCP)
                </span>
                <Tag color={metrics.pageLoad.firstContentfulPaint > 1800 ? 'red' : 'green'}>
                  {formatTime(metrics.pageLoad.firstContentfulPaint)}
                </Tag>
              </div>
            )}

            {metrics.pageLoad?.largestContentfulPaint && (
              <div className="flex justify-between items-center">
                <span className="flex items-center">
                  <ThunderboltOutlined className="mr-2" />
                  最大内容绘制 (LCP)
                </span>
                <Tag color={metrics.pageLoad.largestContentfulPaint > 2500 ? 'red' : 'green'}>
                  {formatTime(metrics.pageLoad.largestContentfulPaint)}
                </Tag>
              </div>
            )}

            {metrics.pageLoad?.firstInputDelay && (
              <div className="flex justify-between items-center">
                <span className="flex items-center">
                  <ClockCircleOutlined className="mr-2" />
                  首次输入延迟 (FID)
                </span>
                <Tag color={metrics.pageLoad.firstInputDelay > 100 ? 'red' : 'green'}>
                  {formatTime(metrics.pageLoad.firstInputDelay)}
                </Tag>
              </div>
            )}

            {metrics.pageLoad?.cumulativeLayoutShift && (
              <div className="flex justify-between items-center">
                <span className="flex items-center">
                  <InfoCircleOutlined className="mr-2" />
                  累积布局偏移 (CLS)
                </span>
                <Tag color={metrics.pageLoad.cumulativeLayoutShift > 0.1 ? 'red' : 'green'}>
                  {metrics.pageLoad.cumulativeLayoutShift.toFixed(3)}
                </Tag>
              </div>
            )}
          </div>
        </Card>

        {/* 资源性能 */}
        <Card size="small" title="资源性能">
          <div className="space-y-3">
            {metrics.resources && (
              <>
                <div className="flex justify-between items-center">
                  <span>总资源大小</span>
                  <span>{formatBytes(metrics.resources.totalSize)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>缓存命中率</span>
                  <span>{metrics.resources.cacheHitRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>最长加载时间</span>
                  <span>{formatTime(metrics.resources.loadTime)}</span>
                </div>
              </>
            )}
          </div>
        </Card>

        {/* 内存使用 */}
        {metrics.runtime && (
          <Card size="small" title="内存使用">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="flex items-center">
                  <MemoryOutlined className="mr-2" />
                  JS堆内存
                </span>
                <span>{formatBytes(metrics.runtime.memoryUsage)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>总堆大小</span>
                <span>{formatBytes(metrics.runtime.jsHeapSize)}</span>
              </div>
              <Progress
                percent={Math.round((metrics.runtime.memoryUsage / metrics.runtime.jsHeapSize) * 100)}
                size="small"
                status={metrics.runtime.memoryUsage > 50 * 1024 * 1024 ? 'exception' : 'normal'}
              />
            </div>
          </Card>
        )}

        {/* 网络信息 */}
        {metrics.network && (
          <Card size="small" title="网络信息">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="flex items-center">
                  <WifiOutlined className="mr-2" />
                  连接类型
                </span>
                <Tag>{metrics.network.connectionType}</Tag>
              </div>
              <div className="flex justify-between items-center">
                <span>有效类型</span>
                <Tag>{metrics.network.effectiveType}</Tag>
              </div>
              {metrics.network.downlink > 0 && (
                <div className="flex justify-between items-center">
                  <span>下行速度</span>
                  <span>{metrics.network.downlink} Mbps</span>
                </div>
              )}
              {metrics.network.rtt > 0 && (
                <div className="flex justify-between items-center">
                  <span>往返时间</span>
                  <span>{metrics.network.rtt}ms</span>
                </div>
              )}
            </div>
          </Card>
        )}
      </div>
    )
  }

  // 渲染性能警告
  const renderWarnings = () => {
    const { warnings } = performanceState

    if (warnings.length === 0) {
      return (
        <Alert
          message="性能状态良好"
          description="当前没有发现性能问题"
          type="success"
          showIcon
        />
      )
    }

    return (
      <div className="space-y-3">
        {warnings.map((warning, index) => (
          <Alert
            key={index}
            message={warning.message}
            description={warning.suggestion}
            type={warning.level === 'error' ? 'error' : warning.level === 'warning' ? 'warning' : 'info'}
            showIcon
            closable
          />
        ))}
      </div>
    )
  }

  // 渲染优化建议
  const renderOptimizations = () => {
    const { optimizations } = performanceState

    if (optimizations.length === 0) {
      return (
        <Alert
          message="暂无优化建议"
          description="当前性能表现良好，无需额外优化"
          type="info"
          showIcon
        />
      )
    }

    return (
      <div className="space-y-4">
        {optimizations.map((optimization, index) => (
          <Card
            key={index}
            size="small"
            title={
              <div className="flex items-center justify-between">
                <span>{optimization.title}</span>
                <Tag color={
                  optimization.priority === 'high' ? 'red' : 
                  optimization.priority === 'medium' ? 'orange' : 'blue'
                }>
                  {optimization.priority === 'high' ? '高优先级' : 
                   optimization.priority === 'medium' ? '中优先级' : '低优先级'}
                </Tag>
              </div>
            }
          >
            <div className="space-y-2">
              <p className="text-sm text-gray-600">{optimization.description}</p>
              <div className="text-xs text-gray-500">
                <strong>影响：</strong>{optimization.impact}
              </div>
              <div className="text-xs">
                <strong>实施方案：</strong>
                <ul className="mt-1 ml-4">
                  {optimization.implementation.map((item, idx) => (
                    <li key={idx} className="list-disc">{item}</li>
                  ))}
                </ul>
              </div>
            </div>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className={`performance-panel ${className}`}>
      <Card
        title="性能监控面板"
        size={isMobile ? 'small' : 'default'}
        extra={
          <div className="flex items-center space-x-2">
            <Tooltip title="刷新数据">
              <Button
                icon={<ReloadOutlined />}
                size="small"
                onClick={performanceActions.refreshMetrics}
                loading={!performanceState.isMonitoring}
              />
            </Tooltip>
            <Tooltip title="导出报告">
              <Button
                icon={<DownloadOutlined />}
                size="small"
                onClick={() => {
                  const report = performanceActions.exportReport()
                  const blob = new Blob([report], { type: 'application/json' })
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement('a')
                  a.href = url
                  a.download = `performance-report-${Date.now()}.json`
                  a.click()
                  URL.revokeObjectURL(url)
                }}
              />
            </Tooltip>
          </div>
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          size={isMobile ? 'small' : 'default'}
        >
          <TabPane tab="性能指标" key="metrics">
            {renderMetrics()}
          </TabPane>
          
          <TabPane tab="性能警告" key="warnings">
            {renderWarnings()}
          </TabPane>
          
          {showOptimizations && (
            <TabPane tab="优化建议" key="optimizations">
              {renderOptimizations()}
            </TabPane>
          )}
        </Tabs>
      </Card>
    </div>
  )
}