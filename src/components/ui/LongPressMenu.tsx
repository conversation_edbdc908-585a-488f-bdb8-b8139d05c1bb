'use client'

import React, { useState, useRef, useCallback } from 'react'
import { useTouch } from '@/hooks/useTouch'

export interface MenuAction {
  key: string
  label: string
  icon?: React.ReactNode
  disabled?: boolean
  danger?: boolean
  onClick?: () => void
}

export interface LongPressMenuProps {
  children: React.ReactNode
  actions: MenuAction[]
  onAction?: (action: string) => void
  className?: string
  disabled?: boolean
  longPressDelay?: number
  menuClassName?: string
}

export function LongPressMenu({
  children,
  actions,
  onAction,
  className = '',
  disabled = false,
  longPressDelay = 500,
  menuClassName = ''
}: LongPressMenuProps) {
  const [isMenuVisible, setIsMenuVisible] = useState(false)
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 })
  const containerRef = useRef<HTMLDivElement>(null)

  const handleLongPress = useCallback((event: React.TouchEvent) => {
    if (disabled || actions.length === 0) return

    // Prevent default context menu
    event.preventDefault()

    const touch = event.touches[0] || event.changedTouches[0]
    const rect = containerRef.current?.getBoundingClientRect()
    
    if (rect) {
      // Calculate menu position relative to the container
      const x = touch.clientX - rect.left
      const y = touch.clientY - rect.top
      
      setMenuPosition({ x, y })
      setIsMenuVisible(true)
    }
  }, [disabled, actions.length])

  const handleDoubleTap = useCallback((event: React.TouchEvent) => {
    // Close menu on double tap
    if (isMenuVisible) {
      setIsMenuVisible(false)
    }
  }, [isMenuVisible])

  const handleActionClick = useCallback((action: MenuAction) => {
    if (action.disabled) return
    
    action.onClick?.()
    onAction?.(action.key)
    setIsMenuVisible(false)
  }, [onAction])

  const handleClickOutside = useCallback((event: React.MouseEvent) => {
    if (isMenuVisible && event.target === event.currentTarget) {
      setIsMenuVisible(false)
    }
  }, [isMenuVisible])

  const { touchHandlers } = useTouch({
    onLongPress: handleLongPress,
    onDoubleTap: handleDoubleTap,
    longPressDelay
  })

  return (
    <div 
      ref={containerRef}
      className={`relative ${className}`}
      {...touchHandlers}
      onClick={handleClickOutside}
    >
      {children}
      
      {/* Long Press Menu */}
      {isMenuVisible && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40 bg-black bg-opacity-20"
            onClick={() => setIsMenuVisible(false)}
          />
          
          {/* Menu */}
          <div
            className={`absolute z-50 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-48 ${menuClassName}`}
            style={{
              left: menuPosition.x,
              top: menuPosition.y,
              transform: 'translate(-50%, -100%)'
            }}
          >
            {actions.map((action, index) => (
              <button
                key={action.key}
                className={`w-full px-4 py-3 text-left flex items-center space-x-3 hover:bg-gray-50 active:bg-gray-100 transition-colors ${
                  action.disabled 
                    ? 'opacity-50 cursor-not-allowed' 
                    : action.danger 
                      ? 'text-red-600 hover:bg-red-50' 
                      : 'text-gray-700'
                }`}
                onClick={() => handleActionClick(action)}
                disabled={action.disabled}
              >
                {action.icon && (
                  <div className="flex-shrink-0">
                    {action.icon}
                  </div>
                )}
                <span className="flex-1 text-sm font-medium">
                  {action.label}
                </span>
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  )
}