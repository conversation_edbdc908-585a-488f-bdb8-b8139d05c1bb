'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { useResponsive } from '@/hooks/useResponsive'

interface LazyImageProps extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src' | 'srcSet'> {
  src: string
  srcSet?: string
  sizes?: string
  alt: string
  placeholder?: string | React.ReactNode
  fallback?: string | React.ReactNode
  threshold?: number
  rootMargin?: string
  loading?: 'lazy' | 'eager'
  onLoad?: () => void
  onError?: () => void
  className?: string
  wrapperClassName?: string
  aspectRatio?: number
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
}

export function LazyImage({
  src,
  srcSet,
  sizes,
  alt,
  placeholder,
  fallback,
  threshold = 0.1,
  rootMargin = '50px',
  loading = 'lazy',
  onLoad,
  onError,
  className = '',
  wrapperClassName = '',
  aspectRatio,
  objectFit = 'cover',
  ...props
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isError, setIsError] = useState(false)
  const [isInView, setIsInView] = useState(loading === 'eager')
  const imgRef = useRef<HTMLImageElement>(null)
  const wrapperRef = useRef<HTMLDivElement>(null)
  const { isMobile } = useResponsive()

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (loading === 'eager' || !wrapperRef.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        threshold,
        rootMargin
      }
    )

    observer.observe(wrapperRef.current)

    return () => observer.disconnect()
  }, [threshold, rootMargin, loading])

  const handleLoad = useCallback(() => {
    setIsLoaded(true)
    onLoad?.()
  }, [onLoad])

  const handleError = useCallback(() => {
    setIsError(true)
    onError?.()
  }, [onError])

  // 生成响应式图片的 srcSet
  const generateSrcSet = useCallback((baseSrc: string) => {
    if (srcSet) return srcSet

    // 如果是相对路径或者包含我们的域名，生成响应式版本
    if (baseSrc.startsWith('/') || baseSrc.includes(window.location.hostname)) {
      const ext = baseSrc.split('.').pop()
      const basePath = baseSrc.replace(`.${ext}`, '')
      
      return [
        `${basePath}_400w.${ext} 400w`,
        `${basePath}_800w.${ext} 800w`,
        `${basePath}_1200w.${ext} 1200w`,
        `${basePath}_1600w.${ext} 1600w`
      ].join(', ')
    }

    return undefined
  }, [srcSet])

  // 生成 sizes 属性
  const generateSizes = useCallback(() => {
    if (sizes) return sizes

    return isMobile 
      ? '(max-width: 576px) 100vw, (max-width: 768px) 50vw, 33vw'
      : '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
  }, [sizes, isMobile])

  const renderPlaceholder = () => {
    if (typeof placeholder === 'string') {
      return (
        <img
          src={placeholder}
          alt=""
          className={`w-full h-full object-${objectFit} ${className}`}
          style={{ filter: 'blur(5px)' }}
        />
      )
    }

    if (placeholder) {
      return placeholder
    }

    // 默认骨架屏占位符
    return (
      <div className={`w-full h-full bg-gray-200 animate-pulse flex items-center justify-center ${className}`}>
        <svg
          className="w-8 h-8 text-gray-400"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
            clipRule="evenodd"
          />
        </svg>
      </div>
    )
  }

  const renderFallback = () => {
    if (typeof fallback === 'string') {
      return (
        <img
          src={fallback}
          alt={alt}
          className={`w-full h-full object-${objectFit} ${className}`}
        />
      )
    }

    if (fallback) {
      return fallback
    }

    // 默认错误占位符
    return (
      <div className={`w-full h-full bg-gray-100 flex items-center justify-center ${className}`}>
        <div className="text-center text-gray-500">
          <svg
            className="w-8 h-8 mx-auto mb-2"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
          <p className="text-xs">加载失败</p>
        </div>
      </div>
    )
  }

  const wrapperStyle: React.CSSProperties = {
    position: 'relative',
    overflow: 'hidden',
    ...(aspectRatio && {
      aspectRatio: aspectRatio.toString(),
      width: '100%'
    })
  }

  return (
    <div
      ref={wrapperRef}
      className={`lazy-image-wrapper ${wrapperClassName}`}
      style={wrapperStyle}
    >
      {isError ? (
        renderFallback()
      ) : !isInView ? (
        renderPlaceholder()
      ) : (
        <>
          {!isLoaded && renderPlaceholder()}
          <img
            ref={imgRef}
            src={src}
            srcSet={generateSrcSet(src)}
            sizes={generateSizes()}
            alt={alt}
            onLoad={handleLoad}
            onError={handleError}
            className={`w-full h-full object-${objectFit} transition-opacity duration-300 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            } ${className}`}
            style={{
              position: isLoaded ? 'static' : 'absolute',
              top: 0,
              left: 0
            }}
            {...props}
          />
        </>
      )}
    </div>
  )
}

// 响应式图片组件
interface ResponsiveImageProps extends LazyImageProps {
  breakpoints?: {
    mobile?: string
    tablet?: string
    desktop?: string
  }
  quality?: number
  format?: 'webp' | 'avif' | 'jpg' | 'png'
}

export function ResponsiveImage({
  src,
  breakpoints,
  quality = 80,
  format = 'webp',
  ...props
}: ResponsiveImageProps) {
  const { isMobile, isTablet } = useResponsive()

  // 根据设备类型选择合适的图片源
  const getOptimizedSrc = useCallback(() => {
    if (breakpoints) {
      if (isMobile && breakpoints.mobile) return breakpoints.mobile
      if (isTablet && breakpoints.tablet) return breakpoints.tablet
      if (breakpoints.desktop) return breakpoints.desktop
    }

    // 如果没有指定断点，使用原始图片
    return src
  }, [src, breakpoints, isMobile, isTablet])

  // 生成优化的图片URL（如果使用图片优化服务）
  const getOptimizedUrl = useCallback((originalSrc: string) => {
    // 这里可以集成图片优化服务，如 Cloudinary, ImageKit 等
    // 示例：return `https://res.cloudinary.com/your-cloud/image/fetch/f_${format},q_${quality}/${encodeURIComponent(originalSrc)}`
    
    // 对于本地图片，可以使用 Next.js Image Optimization
    if (originalSrc.startsWith('/')) {
      const params = new URLSearchParams({
        url: originalSrc,
        w: isMobile ? '400' : isTablet ? '800' : '1200',
        q: quality.toString()
      })
      return `/_next/image?${params.toString()}`
    }

    return originalSrc
  }, [format, quality, isMobile, isTablet])

  return (
    <LazyImage
      {...props}
      src={getOptimizedUrl(getOptimizedSrc())}
    />
  )
}

// 图片画廊组件
interface ImageGalleryProps {
  images: Array<{
    src: string
    alt: string
    caption?: string
    thumbnail?: string
  }>
  columns?: number
  gap?: number
  className?: string
  onImageClick?: (index: number) => void
}

export function ImageGallery({
  images,
  columns = 3,
  gap = 8,
  className = '',
  onImageClick
}: ImageGalleryProps) {
  const { isMobile, isTablet } = useResponsive()

  // 响应式列数
  const responsiveColumns = isMobile ? 2 : isTablet ? 3 : columns

  return (
    <div
      className={`image-gallery grid ${className}`}
      style={{
        gridTemplateColumns: `repeat(${responsiveColumns}, 1fr)`,
        gap: `${gap}px`
      }}
    >
      {images.map((image, index) => (
        <div
          key={index}
          className="image-gallery-item cursor-pointer group"
          onClick={() => onImageClick?.(index)}
        >
          <LazyImage
            src={image.thumbnail || image.src}
            alt={image.alt}
            aspectRatio={1}
            className="group-hover:scale-105 transition-transform duration-200"
            wrapperClassName="rounded-lg overflow-hidden"
          />
          {image.caption && (
            <p className="mt-2 text-sm text-gray-600 text-center">
              {image.caption}
            </p>
          )}
        </div>
      ))}
    </div>
  )
}