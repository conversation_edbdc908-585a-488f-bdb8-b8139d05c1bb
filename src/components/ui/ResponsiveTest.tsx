'use client'

import React, { useState, useMemo } from 'react'
import { Card, Button, Space, Typography, Divider, Row, Col, Switch, Slider } from 'antd'
import { 
  MobileOutlined, 
  TabletOutlined, 
  DesktopOutlined,
  ThunderboltOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { useResponsive } from '@/hooks/useResponsive'
import { usePerformance } from '@/hooks/usePerformance'
import { VirtualList, VirtualTable } from './VirtualList'
import { Skeleton, TableSkeleton, CardSkeleton, ListSkeleton, MobileLoading } from './Skeleton'
import { LazyImage, ResponsiveImage, ImageGallery } from './LazyImage'
import { PerformanceOptimizer, PerformanceMonitor } from './PerformanceOptimizer'
import { PerformancePanel } from './PerformancePanel'

const { Title, Paragraph, Text } = Typography

// 生成测试数据
const generateTestData = (count: number) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: `测试项目 ${index + 1}`,
    description: `这是第 ${index + 1} 个测试项目的描述信息，用于演示虚拟滚动的性能优化效果。`,
    value: Math.floor(Math.random() * 1000),
    status: ['active', 'inactive', 'pending'][Math.floor(Math.random() * 3)],
    date: new Date(Date.now() - Math.random() * 10000000000).toLocaleDateString()
  }))
}

const generateImageData = (count: number) => {
  return Array.from({ length: count }, (_, index) => ({
    src: `https://picsum.photos/400/300?random=${index}`,
    alt: `测试图片 ${index + 1}`,
    caption: `图片 ${index + 1}`,
    thumbnail: `https://picsum.photos/200/150?random=${index}`
  }))
}

export function ResponsiveTest() {
  const { isMobile, isTablet, isDesktop, screenSize } = useResponsive()
  const [performanceState, performanceActions] = usePerformance()
  
  // 测试状态
  const [showSkeleton, setShowSkeleton] = useState(false)
  const [showPerformancePanel, setShowPerformancePanel] = useState(false)
  const [virtualListCount, setVirtualListCount] = useState(1000)
  const [enableOptimizations, setEnableOptimizations] = useState(true)

  // 生成测试数据
  const testData = useMemo(() => generateTestData(virtualListCount), [virtualListCount])
  const imageData = useMemo(() => generateImageData(12), [])

  // 表格列定义
  const tableColumns = [
    {
      key: 'id',
      title: 'ID',
      dataIndex: 'id',
      width: 80
    },
    {
      key: 'name',
      title: '名称',
      dataIndex: 'name',
      width: 150
    },
    {
      key: 'description',
      title: '描述',
      dataIndex: 'description',
      width: 200
    },
    {
      key: 'value',
      title: '数值',
      dataIndex: 'value',
      width: 100,
      render: (value: number) => value.toLocaleString()
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status: string) => (
        <span className={`px-2 py-1 rounded text-xs ${
          status === 'active' ? 'bg-green-100 text-green-800' :
          status === 'inactive' ? 'bg-red-100 text-red-800' :
          'bg-yellow-100 text-yellow-800'
        }`}>
          {status === 'active' ? '活跃' : status === 'inactive' ? '非活跃' : '待定'}
        </span>
      )
    },
    {
      key: 'date',
      title: '日期',
      dataIndex: 'date',
      width: 120
    }
  ]

  // 渲染虚拟列表项
  const renderVirtualListItem = (item: any, index: number) => (
    <div className="p-4 border-b border-gray-200 hover:bg-gray-50">
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h4 className="font-medium text-gray-900">{item.name}</h4>
          <p className="text-sm text-gray-600 mt-1">{item.description}</p>
          <div className="flex items-center mt-2 space-x-4">
            <span className="text-sm text-gray-500">数值: {item.value.toLocaleString()}</span>
            <span className={`px-2 py-1 rounded text-xs ${
              item.status === 'active' ? 'bg-green-100 text-green-800' :
              item.status === 'inactive' ? 'bg-red-100 text-red-800' :
              'bg-yellow-100 text-yellow-800'
            }`}>
              {item.status === 'active' ? '活跃' : item.status === 'inactive' ? '非活跃' : '待定'}
            </span>
          </div>
        </div>
        <div className="text-sm text-gray-500">{item.date}</div>
      </div>
    </div>
  )

  return (
    <PerformanceOptimizer
      enableVirtualScrolling={enableOptimizations}
      enableImageLazyLoading={enableOptimizations}
      enableResourcePreloading={enableOptimizations}
      enableMemoryOptimization={enableOptimizations}
      className="responsive-test-container"
    >
      <div className="p-4 space-y-6">
        {/* 性能监控器 */}
        <PerformanceMonitor showMetrics={process.env.NODE_ENV === 'development'} />

        {/* 页面标题 */}
        <Card>
          <Title level={2}>
            <ThunderboltOutlined className="mr-2" />
            移动端性能优化演示
          </Title>
          <Paragraph>
            这个页面展示了移动端性能优化的各种技术，包括虚拟滚动、懒加载、骨架屏、响应式图片等。
          </Paragraph>
          
          {/* 设备信息 */}
          <div className="flex items-center space-x-4 mb-4">
            <div className="flex items-center">
              {isMobile && <MobileOutlined className="text-blue-500 mr-1" />}
              {isTablet && <TabletOutlined className="text-green-500 mr-1" />}
              {isDesktop && <DesktopOutlined className="text-purple-500 mr-1" />}
              <Text>
                当前设备: {isMobile ? '移动端' : isTablet ? '平板端' : '桌面端'} 
                ({screenSize.width}x{screenSize.height})
              </Text>
            </div>
          </div>

          {/* 控制面板 */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <Title level={4}>测试控制</Title>
            <Space direction={isMobile ? 'vertical' : 'horizontal'} size="large" className="w-full">
              <div className="flex items-center space-x-2">
                <Text>显示骨架屏:</Text>
                <Switch checked={showSkeleton} onChange={setShowSkeleton} />
              </div>
              <div className="flex items-center space-x-2">
                <Text>性能面板:</Text>
                <Switch checked={showPerformancePanel} onChange={setShowPerformancePanel} />
              </div>
              <div className="flex items-center space-x-2">
                <Text>启用优化:</Text>
                <Switch checked={enableOptimizations} onChange={setEnableOptimizations} />
              </div>
            </Space>
            
            <div className="mt-4">
              <Text>虚拟列表数据量: {virtualListCount.toLocaleString()}</Text>
              <Slider
                min={100}
                max={10000}
                step={100}
                value={virtualListCount}
                onChange={setVirtualListCount}
                className="mt-2"
              />
            </div>
          </div>
        </Card>

        {/* 性能面板 */}
        {showPerformancePanel && (
          <PerformancePanel
            showOptimizations={true}
            autoRefresh={true}
            refreshInterval={3000}
          />
        )}

        {/* 骨架屏演示 */}
        <Card title="骨架屏演示">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Skeleton loading={showSkeleton} avatar title paragraph={{ rows: 3 }}>
                <Card size="small">
                  <Card.Meta
                    avatar={<div className="w-10 h-10 bg-blue-500 rounded-full" />}
                    title="用户信息卡片"
                    description="这是一个用户信息卡片的示例，展示了骨架屏的加载效果。"
                  />
                </Card>
              </Skeleton>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <CardSkeleton loading={showSkeleton}>
                <Card size="small" title="功能卡片">
                  <p>这是一个功能卡片的内容区域。</p>
                  <Button type="primary" size="small">操作按钮</Button>
                </Card>
              </CardSkeleton>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <ListSkeleton loading={showSkeleton} rows={3}>
                <div className="space-y-2">
                  {[1, 2, 3].map(i => (
                    <div key={i} className="flex items-center space-x-3 p-2 border rounded">
                      <div className="w-8 h-8 bg-green-500 rounded-full" />
                      <div>
                        <div className="font-medium">列表项 {i}</div>
                        <div className="text-sm text-gray-500">描述信息</div>
                      </div>
                    </div>
                  ))}
                </div>
              </ListSkeleton>
            </Col>
          </Row>
        </Card>

        {/* 虚拟滚动演示 */}
        <Card title={`虚拟滚动演示 (${testData.length.toLocaleString()} 条数据)`}>
          <Paragraph>
            虚拟滚动技术只渲染可见区域的元素，大大提升长列表的性能。
          </Paragraph>
          
          <div className="space-y-4">
            {/* 虚拟列表 */}
            <div>
              <Title level={4}>虚拟列表</Title>
              <VirtualList
                items={testData}
                itemHeight={120}
                renderItem={renderVirtualListItem}
                containerHeight={isMobile ? 300 : 400}
                className="border border-gray-200 rounded"
              />
            </div>

            {/* 虚拟表格 */}
            <div>
              <Title level={4}>虚拟表格</Title>
              <VirtualTable
                dataSource={testData}
                columns={tableColumns}
                rowHeight={48}
                containerHeight={isMobile ? 300 : 400}
                className="border border-gray-200 rounded"
              />
            </div>
          </div>
        </Card>

        {/* 懒加载图片演示 */}
        <Card title="懒加载图片演示">
          <Paragraph>
            图片懒加载技术只在图片进入视口时才开始加载，减少初始页面加载时间。
          </Paragraph>
          
          <div className="space-y-4">
            {/* 单张响应式图片 */}
            <div>
              <Title level={4}>响应式图片</Title>
              <ResponsiveImage
                src="https://picsum.photos/800/400?random=demo"
                alt="演示图片"
                aspectRatio={2}
                className="rounded-lg"
                quality={80}
                format="webp"
              />
            </div>

            {/* 图片画廊 */}
            <div>
              <Title level={4}>图片画廊</Title>
              <ImageGallery
                images={imageData}
                columns={isMobile ? 2 : isTablet ? 3 : 4}
                gap={16}
                onImageClick={(index) => {
                  console.log('点击图片:', index)
                }}
              />
            </div>
          </div>
        </Card>

        {/* 加载状态演示 */}
        <Card title="加载状态演示">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Card size="small" title="旋转加载">
                <MobileLoading type="spinner" size="medium" text="加载中..." />
              </Card>
            </Col>
            
            <Col xs={24} sm={8}>
              <Card size="small" title="点状加载">
                <MobileLoading type="dots" size="medium" text="处理中..." />
              </Card>
            </Col>
            
            <Col xs={24} sm={8}>
              <Card size="small" title="条状加载">
                <MobileLoading type="bars" size="medium" text="同步中..." />
              </Card>
            </Col>
          </Row>
        </Card>

        {/* 表格骨架屏演示 */}
        <Card title="表格骨架屏演示">
          <TableSkeleton loading={showSkeleton} rows={5} columns={4}>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-200 p-2 text-left">ID</th>
                    <th className="border border-gray-200 p-2 text-left">名称</th>
                    <th className="border border-gray-200 p-2 text-left">状态</th>
                    <th className="border border-gray-200 p-2 text-left">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {testData.slice(0, 5).map(item => (
                    <tr key={item.id}>
                      <td className="border border-gray-200 p-2">{item.id}</td>
                      <td className="border border-gray-200 p-2">{item.name}</td>
                      <td className="border border-gray-200 p-2">{item.status}</td>
                      <td className="border border-gray-200 p-2">
                        <Button size="small">编辑</Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TableSkeleton>
        </Card>

        {/* 性能提示 */}
        <Card>
          <Title level={4}>
            <EyeOutlined className="mr-2" />
            性能优化提示
          </Title>
          <div className="space-y-2 text-sm">
            <p>• 虚拟滚动适用于大量数据的列表和表格</p>
            <p>• 图片懒加载可以显著减少初始页面加载时间</p>
            <p>• 骨架屏提供更好的加载体验</p>
            <p>• 响应式图片根据设备自动选择合适的尺寸</p>
            <p>• 性能监控帮助识别和解决性能问题</p>
          </div>
        </Card>
      </div>
    </PerformanceOptimizer>
  )
}