'use client'

import React from 'react'
import { useResponsive } from '@/hooks/useResponsive'

interface SkeletonProps {
  loading?: boolean
  children?: React.ReactNode
  className?: string
  rows?: number
  avatar?: boolean
  title?: boolean
  paragraph?: boolean | { rows?: number; width?: string | string[] }
  active?: boolean
}

export function Skeleton({
  loading = true,
  children,
  className = '',
  rows = 3,
  avatar = false,
  title = true,
  paragraph = true,
  active = true
}: SkeletonProps) {
  const { isMobile } = useResponsive()

  if (!loading && children) {
    return <>{children}</>
  }

  const baseClass = `animate-pulse ${active ? 'animate-pulse' : ''}`
  const skeletonClass = `bg-gray-200 rounded ${baseClass}`

  const paragraphConfig = typeof paragraph === 'object' ? paragraph : { rows }
  const paragraphRows = paragraphConfig.rows || rows
  const paragraphWidths = Array.isArray(paragraphConfig.width) 
    ? paragraphConfig.width 
    : new Array(paragraphRows).fill(paragraphConfig.width || '100%')

  return (
    <div className={`skeleton ${className}`}>
      <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'space-x-4'}`}>
        {avatar && (
          <div className={`${skeletonClass} ${isMobile ? 'w-12 h-12 mx-auto' : 'w-12 h-12'} rounded-full flex-shrink-0`} />
        )}
        
        <div className="flex-1 space-y-3">
          {title && (
            <div className={`${skeletonClass} h-4 ${isMobile ? 'w-3/4' : 'w-1/2'}`} />
          )}
          
          {paragraph && (
            <div className="space-y-2">
              {Array.from({ length: paragraphRows }).map((_, index) => (
                <div
                  key={index}
                  className={`${skeletonClass} h-3`}
                  style={{ width: paragraphWidths[index] || '100%' }}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// 表格骨架屏
interface TableSkeletonProps {
  rows?: number
  columns?: number
  loading?: boolean
  children?: React.ReactNode
  className?: string
}

export function TableSkeleton({
  rows = 5,
  columns = 4,
  loading = true,
  children,
  className = ''
}: TableSkeletonProps) {
  const { isMobile } = useResponsive()

  if (!loading && children) {
    return <>{children}</>
  }

  return (
    <div className={`table-skeleton ${className}`}>
      {/* 表头骨架 */}
      <div className={`flex ${isMobile ? 'space-x-2' : 'space-x-4'} mb-4 p-4 bg-gray-50 rounded`}>
        {Array.from({ length: columns }).map((_, index) => (
          <div
            key={index}
            className="animate-pulse bg-gray-300 h-4 rounded flex-1"
          />
        ))}
      </div>

      {/* 表格行骨架 */}
      <div className="space-y-3">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div
            key={rowIndex}
            className={`flex ${isMobile ? 'space-x-2' : 'space-x-4'} p-4 border border-gray-200 rounded`}
          >
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div
                key={colIndex}
                className="animate-pulse bg-gray-200 h-4 rounded flex-1"
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}

// 卡片骨架屏
interface CardSkeletonProps {
  loading?: boolean
  children?: React.ReactNode
  className?: string
  avatar?: boolean
  title?: boolean
  content?: boolean
  actions?: boolean
}

export function CardSkeleton({
  loading = true,
  children,
  className = '',
  avatar = true,
  title = true,
  content = true,
  actions = true
}: CardSkeletonProps) {
  const { isMobile } = useResponsive()

  if (!loading && children) {
    return <>{children}</>
  }

  return (
    <div className={`card-skeleton p-4 border border-gray-200 rounded-lg ${className}`}>
      <div className="animate-pulse">
        {/* 头部区域 */}
        {(avatar || title) && (
          <div className={`flex items-center ${isMobile ? 'space-x-3' : 'space-x-4'} mb-4`}>
            {avatar && (
              <div className="bg-gray-300 rounded-full w-10 h-10 flex-shrink-0" />
            )}
            {title && (
              <div className="flex-1">
                <div className="bg-gray-300 h-4 rounded w-3/4 mb-2" />
                <div className="bg-gray-200 h-3 rounded w-1/2" />
              </div>
            )}
          </div>
        )}

        {/* 内容区域 */}
        {content && (
          <div className="space-y-2 mb-4">
            <div className="bg-gray-200 h-3 rounded w-full" />
            <div className="bg-gray-200 h-3 rounded w-5/6" />
            <div className="bg-gray-200 h-3 rounded w-4/6" />
          </div>
        )}

        {/* 操作区域 */}
        {actions && (
          <div className={`flex ${isMobile ? 'space-x-2' : 'space-x-3'}`}>
            <div className="bg-gray-300 h-8 rounded w-16" />
            <div className="bg-gray-300 h-8 rounded w-16" />
          </div>
        )}
      </div>
    </div>
  )
}

// 列表骨架屏
interface ListSkeletonProps {
  rows?: number
  loading?: boolean
  children?: React.ReactNode
  className?: string
  avatar?: boolean
}

export function ListSkeleton({
  rows = 5,
  loading = true,
  children,
  className = '',
  avatar = true
}: ListSkeletonProps) {
  if (!loading && children) {
    return <>{children}</>
  }

  return (
    <div className={`list-skeleton space-y-3 ${className}`}>
      {Array.from({ length: rows }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4 p-3 border border-gray-200 rounded">
          <div className="animate-pulse flex items-center space-x-4 w-full">
            {avatar && (
              <div className="bg-gray-300 rounded-full w-8 h-8 flex-shrink-0" />
            )}
            <div className="flex-1 space-y-2">
              <div className="bg-gray-300 h-4 rounded w-3/4" />
              <div className="bg-gray-200 h-3 rounded w-1/2" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

// 移动端优化的加载组件
interface MobileLoadingProps {
  loading?: boolean
  children?: React.ReactNode
  type?: 'spinner' | 'dots' | 'bars'
  size?: 'small' | 'medium' | 'large'
  text?: string
  className?: string
}

export function MobileLoading({
  loading = true,
  children,
  type = 'spinner',
  size = 'medium',
  text,
  className = ''
}: MobileLoadingProps) {
  if (!loading && children) {
    return <>{children}</>
  }

  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-8 h-8',
    large: 'w-12 h-12'
  }

  const renderLoader = () => {
    switch (type) {
      case 'spinner':
        return (
          <div className={`animate-spin rounded-full border-2 border-blue-500 border-t-transparent ${sizeClasses[size]}`} />
        )
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={`bg-blue-500 rounded-full animate-bounce ${size === 'small' ? 'w-2 h-2' : size === 'large' ? 'w-4 h-4' : 'w-3 h-3'}`}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        )
      case 'bars':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2, 3].map((i) => (
              <div
                key={i}
                className={`bg-blue-500 animate-pulse ${size === 'small' ? 'w-1 h-4' : size === 'large' ? 'w-2 h-8' : 'w-1.5 h-6'}`}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className={`mobile-loading flex flex-col items-center justify-center p-8 ${className}`}>
      {renderLoader()}
      {text && (
        <p className="mt-4 text-gray-600 text-sm text-center">{text}</p>
      )}
    </div>
  )
}