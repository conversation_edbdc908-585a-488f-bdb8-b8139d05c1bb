'use client'

import React, { useState, useRef, useCallback } from 'react'
import { Button } from 'antd'
import type { ButtonProps } from 'antd'
import { useTouch } from '@/hooks/useTouch'
import { useResponsive } from '@/hooks/useResponsive'

export interface TouchButtonProps extends ButtonProps {
  /**
   * 触摸目标大小
   */
  touchSize?: 'small' | 'medium' | 'large'
  
  /**
   * 是否启用触摸反馈动画
   */
  enableTouchFeedback?: boolean
  
  /**
   * 是否启用长按功能
   */
  enableLongPress?: boolean
  
  /**
   * 长按回调
   */
  onLongPress?: () => void
  
  /**
   * 双击回调
   */
  onDoubleClick?: () => void
  
  /**
   * 长按延迟时间（毫秒）
   */
  longPressDelay?: number
  
  /**
   * 触摸反馈强度
   */
  feedbackIntensity?: 'light' | 'medium' | 'strong'
}

export function TouchButton({
  touchSize = 'medium',
  enableTouchFeedback = true,
  enableLongPress = false,
  onLongPress,
  onDoubleClick,
  longPressDelay = 500,
  feedbackIntensity = 'medium',
  className = '',
  children,
  onClick,
  ...props
}: TouchButtonProps) {
  const { isMobile } = useResponsive()
  const [isPressed, setIsPressed] = useState(false)
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([])
  const buttonRef = useRef<HTMLButtonElement>(null)
  const rippleIdRef = useRef(0)

  // 获取触摸目标尺寸
  const getTouchSize = () => {
    switch (touchSize) {
      case 'small':
        return isMobile ? 'min-h-[40px] min-w-[40px]' : 'min-h-[32px] min-w-[32px]'
      case 'large':
        return isMobile ? 'min-h-[56px] min-w-[56px]' : 'min-h-[48px] min-w-[48px]'
      case 'medium':
      default:
        return isMobile ? 'min-h-[48px] min-w-[48px]' : 'min-h-[40px] min-w-[40px]'
    }
  }

  // 获取反馈强度样式
  const getFeedbackIntensity = () => {
    switch (feedbackIntensity) {
      case 'light':
        return 'active:scale-[0.98] active:opacity-90'
      case 'strong':
        return 'active:scale-[0.92] active:opacity-80'
      case 'medium':
      default:
        return 'active:scale-[0.95] active:opacity-85'
    }
  }

  // 创建涟漪效果
  const createRipple = useCallback((event: React.MouseEvent | React.TouchEvent) => {
    if (!enableTouchFeedback || !buttonRef.current) return

    const rect = buttonRef.current.getBoundingClientRect()
    const clientX = 'touches' in event ? event.touches[0]?.clientX : event.clientX
    const clientY = 'touches' in event ? event.touches[0]?.clientY : event.clientY
    
    const x = clientX - rect.left
    const y = clientY - rect.top

    const newRipple = {
      id: rippleIdRef.current++,
      x,
      y
    }

    setRipples(prev => [...prev, newRipple])

    // 移除涟漪效果
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id))
    }, 600)
  }, [enableTouchFeedback])

  // 处理点击事件
  const handleClick = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    if (props.disabled) return
    
    createRipple(event)
    onClick?.(event)
  }, [onClick, props.disabled, createRipple])

  // 处理触摸事件
  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (props.disabled) return
    
    setIsPressed(true)
    createRipple(event)
  }, [props.disabled, createRipple])

  const handleTouchEnd = useCallback(() => {
    setIsPressed(false)
  }, [])

  const handleLongPress = useCallback(() => {
    if (enableLongPress && onLongPress && !props.disabled) {
      // 触觉反馈（如果支持）
      if ('vibrate' in navigator) {
        navigator.vibrate(50)
      }
      onLongPress()
    }
  }, [enableLongPress, onLongPress, props.disabled])

  const handleDoubleTap = useCallback(() => {
    if (onDoubleClick && !props.disabled) {
      onDoubleClick()
    }
  }, [onDoubleClick, props.disabled])

  const { touchHandlers } = useTouch({
    onLongPress: handleLongPress,
    onDoubleTap: handleDoubleTap,
    longPressDelay
  })

  // 构建样式类名
  const buttonClassName = [
    'touch-button',
    'relative',
    'overflow-hidden',
    'transition-all',
    'duration-150',
    'ease-in-out',
    getTouchSize(),
    enableTouchFeedback ? getFeedbackIntensity() : '',
    enableTouchFeedback ? 'select-none' : '',
    isPressed && enableTouchFeedback ? 'pressed' : '',
    className
  ].filter(Boolean).join(' ')

  return (
    <Button
      {...props}
      ref={buttonRef}
      className={buttonClassName}
      onClick={handleClick}
      {...touchHandlers}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      style={{
        WebkitTapHighlightColor: 'transparent',
        ...props.style
      }}
    >
      {/* 涟漪效果 */}
      {enableTouchFeedback && ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute pointer-events-none bg-white bg-opacity-30 rounded-full animate-ping"
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
            animation: 'ripple 0.6s linear'
          }}
        />
      ))}
      
      {children}
    </Button>
  )
}

// 触摸卡片组件
export interface TouchCardProps {
  children: React.ReactNode
  className?: string
  onClick?: () => void
  onLongPress?: () => void
  onDoubleClick?: () => void
  enableTouchFeedback?: boolean
  feedbackIntensity?: 'light' | 'medium' | 'strong'
  disabled?: boolean
}

export function TouchCard({
  children,
  className = '',
  onClick,
  onLongPress,
  onDoubleClick,
  enableTouchFeedback = true,
  feedbackIntensity = 'light',
  disabled = false
}: TouchCardProps) {
  const [isPressed, setIsPressed] = useState(false)
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([])
  const cardRef = useRef<HTMLDivElement>(null)
  const rippleIdRef = useRef(0)

  // 获取反馈强度样式
  const getFeedbackIntensity = () => {
    switch (feedbackIntensity) {
      case 'light':
        return 'active:scale-[0.99] active:opacity-95'
      case 'strong':
        return 'active:scale-[0.96] active:opacity-85'
      case 'medium':
      default:
        return 'active:scale-[0.98] active:opacity-90'
    }
  }

  // 创建涟漪效果
  const createRipple = useCallback((event: React.MouseEvent | React.TouchEvent) => {
    if (!enableTouchFeedback || !cardRef.current || disabled) return

    const rect = cardRef.current.getBoundingClientRect()
    const clientX = 'touches' in event ? event.touches[0]?.clientX : event.clientX
    const clientY = 'touches' in event ? event.touches[0]?.clientY : event.clientY
    
    const x = clientX - rect.left
    const y = clientY - rect.top

    const newRipple = {
      id: rippleIdRef.current++,
      x,
      y
    }

    setRipples(prev => [...prev, newRipple])

    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id))
    }, 600)
  }, [enableTouchFeedback, disabled])

  const handleClick = useCallback((event: React.MouseEvent) => {
    if (disabled) return
    
    createRipple(event)
    onClick?.()
  }, [onClick, disabled, createRipple])

  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (disabled) return
    
    setIsPressed(true)
    createRipple(event)
  }, [disabled, createRipple])

  const handleTouchEnd = useCallback(() => {
    setIsPressed(false)
  }, [])

  const handleLongPress = useCallback(() => {
    if (onLongPress && !disabled) {
      if ('vibrate' in navigator) {
        navigator.vibrate(50)
      }
      onLongPress()
    }
  }, [onLongPress, disabled])

  const handleDoubleTap = useCallback(() => {
    if (onDoubleClick && !disabled) {
      onDoubleClick()
    }
  }, [onDoubleClick, disabled])

  const { touchHandlers } = useTouch({
    onLongPress: handleLongPress,
    onDoubleTap: handleDoubleTap
  })

  const cardClassName = [
    'touch-card',
    'relative',
    'overflow-hidden',
    'transition-all',
    'duration-150',
    'ease-in-out',
    'cursor-pointer',
    enableTouchFeedback ? getFeedbackIntensity() : '',
    enableTouchFeedback ? 'select-none' : '',
    disabled ? 'opacity-50 cursor-not-allowed' : '',
    isPressed && enableTouchFeedback ? 'pressed' : '',
    className
  ].filter(Boolean).join(' ')

  return (
    <div
      ref={cardRef}
      className={cardClassName}
      onClick={handleClick}
      {...touchHandlers}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      style={{
        WebkitTapHighlightColor: 'transparent'
      }}
    >
      {/* 涟漪效果 */}
      {enableTouchFeedback && ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute pointer-events-none bg-gray-400 bg-opacity-20 rounded-full animate-ping"
          style={{
            left: ripple.x - 15,
            top: ripple.y - 15,
            width: 30,
            height: 30,
            animation: 'ripple 0.6s linear'
          }}
        />
      ))}
      
      {children}
    </div>
  )
}