'use client'

import React, { useEffect, useState, useRef } from 'react'
import { Modal, Button, Typography } from 'antd'
import { CloseOutlined, ArrowLeftOutlined } from '@ant-design/icons'
import type { ModalProps } from 'antd'
import { useResponsive } from '@/hooks/useResponsive'
import { cn } from '@/lib/utils'

const { Title } = Typography

export interface FullScreenModalProps extends Omit<ModalProps, 'width' | 'height' | 'style' | 'centered'> {
  /**
   * 是否强制全屏显示（即使在桌面端）
   */
  forceFullScreen?: boolean
  
  /**
   * 头部配置
   */
  header?: {
    /**
     * 标题
     */
    title?: React.ReactNode
    
    /**
     * 是否显示返回按钮
     */
    showBackButton?: boolean
    
    /**
     * 返回按钮点击事件
     */
    onBack?: () => void
    
    /**
     * 是否显示关闭按钮
     */
    showCloseButton?: boolean
    
    /**
     * 自定义头部内容
     */
    extra?: React.ReactNode
    
    /**
     * 头部样式
     */
    style?: React.CSSProperties
    
    /**
     * 头部类名
     */
    className?: string
  }
  
  /**
   * 底部配置
   */
  footer?: {
    /**
     * 是否显示底部
     */
    show?: boolean
    
    /**
     * 底部内容
     */
    content?: React.ReactNode
    
    /**
     * 底部样式
     */
    style?: React.CSSProperties
    
    /**
     * 底部类名
     */
    className?: string
  }
  
  /**
   * 内容区域配置
   */
  body?: {
    /**
     * 是否启用滚动
     */
    scrollable?: boolean
    
    /**
     * 内容样式
     */
    style?: React.CSSProperties
    
    /**
     * 内容类名
     */
    className?: string
    
    /**
     * 内容区域padding
     */
    padding?: boolean | string | number
  }
  
  /**
   * 动画配置
   */
  animation?: {
    /**
     * 进入动画类型
     */
    enter?: 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right' | 'fade' | 'zoom'
    
    /**
     * 退出动画类型
     */
    exit?: 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right' | 'fade' | 'zoom'
    
    /**
     * 动画持续时间（毫秒）
     */
    duration?: number
  }
  
  /**
   * 是否启用手势关闭
   */
  enableSwipeToClose?: boolean
  
  /**
   * 手势关闭方向
   */
  swipeDirection?: 'up' | 'down' | 'left' | 'right'
  
  /**
   * 手势关闭阈值（像素）
   */
  swipeThreshold?: number
}

/**
 * 全屏模态框组件
 * 在移动端自动全屏显示，在桌面端可选择全屏或普通模式
 */
export function FullScreenModal({
  forceFullScreen = false,
  header = {
    showBackButton: false,
    showCloseButton: true
  },
  footer = {
    show: false
  },
  body = {
    scrollable: true,
    padding: true
  },
  animation = {
    enter: 'slide-up',
    exit: 'slide-down',
    duration: 300
  },
  enableSwipeToClose = true,
  swipeDirection = 'down',
  swipeThreshold = 100,
  className,
  children,
  onCancel,
  ...modalProps
}: FullScreenModalProps) {
  const { isMobile, deviceInfo } = useResponsive()
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const modalRef = useRef<HTMLDivElement>(null)
  const touchStartRef = useRef<{ x: number; y: number } | null>(null)
  const touchMoveRef = useRef<{ x: number; y: number } | null>(null)

  // 是否应该全屏显示
  const shouldFullScreen = isMobile || forceFullScreen

  // 处理模态框显示状态
  useEffect(() => {
    if (modalProps.open) {
      setIsVisible(true)
      setIsAnimating(true)
      
      // 动画结束后设置状态
      const timer = setTimeout(() => {
        setIsAnimating(false)
      }, animation.duration || 300)
      
      return () => clearTimeout(timer)
    } else {
      setIsAnimating(true)
      
      // 动画结束后隐藏
      const timer = setTimeout(() => {
        setIsVisible(false)
        setIsAnimating(false)
      }, animation.duration || 300)
      
      return () => clearTimeout(timer)
    }
  }, [modalProps.open, animation.duration])

  // 处理关闭事件
  const handleClose = (e?: React.MouseEvent) => {
    if (onCancel) {
      onCancel(e || ({} as React.MouseEvent))
    }
  }

  // 处理返回事件
  const handleBack = () => {
    if (header.onBack) {
      header.onBack()
    } else {
      handleClose()
    }
  }

  // 处理触摸开始
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!enableSwipeToClose) return
    
    const touch = e.touches[0]
    touchStartRef.current = { x: touch.clientX, y: touch.clientY }
  }

  // 处理触摸移动
  const handleTouchMove = (e: React.TouchEvent) => {
    if (!enableSwipeToClose || !touchStartRef.current) return
    
    const touch = e.touches[0]
    touchMoveRef.current = { x: touch.clientX, y: touch.clientY }
  }

  // 处理触摸结束
  const handleTouchEnd = () => {
    if (!enableSwipeToClose || !touchStartRef.current || !touchMoveRef.current) {
      touchStartRef.current = null
      touchMoveRef.current = null
      return
    }

    const deltaX = touchMoveRef.current.x - touchStartRef.current.x
    const deltaY = touchMoveRef.current.y - touchStartRef.current.y
    const threshold = swipeThreshold

    let shouldClose = false

    switch (swipeDirection) {
      case 'up':
        shouldClose = deltaY < -threshold
        break
      case 'down':
        shouldClose = deltaY > threshold
        break
      case 'left':
        shouldClose = deltaX < -threshold
        break
      case 'right':
        shouldClose = deltaX > threshold
        break
    }

    if (shouldClose) {
      handleClose()
    }

    touchStartRef.current = null
    touchMoveRef.current = null
  }

  // 渲染头部
  const renderHeader = () => {
    if (!header.title && !header.showBackButton && !header.showCloseButton && !header.extra) {
      return null
    }

    return (
      <div 
        className={cn(
          'fullscreen-modal-header',
          'flex items-center justify-between',
          'px-4 py-3 border-b border-gray-200',
          'bg-white sticky top-0 z-10',
          header.className
        )}
        style={header.style}
      >
        {/* 左侧：返回按钮 + 标题 */}
        <div className="flex items-center flex-1 min-w-0">
          {header.showBackButton && (
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              className="mr-2 touch-target"
              aria-label="返回"
            />
          )}
          
          {header.title && (
            <div className="flex-1 min-w-0">
              {typeof header.title === 'string' ? (
                <Title level={4} className="mb-0 truncate">
                  {header.title}
                </Title>
              ) : (
                header.title
              )}
            </div>
          )}
        </div>

        {/* 右侧：额外内容 + 关闭按钮 */}
        <div className="flex items-center space-x-2">
          {header.extra}
          
          {header.showCloseButton && (
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleClose}
              className="touch-target"
              aria-label="关闭"
            />
          )}
        </div>
      </div>
    )
  }

  // 渲染底部
  const renderFooter = () => {
    if (!footer.show || !footer.content) return null

    return (
      <div 
        className={cn(
          'fullscreen-modal-footer',
          'px-4 py-3 border-t border-gray-200',
          'bg-white sticky bottom-0 z-10',
          footer.className
        )}
        style={footer.style}
      >
        {footer.content}
      </div>
    )
  }

  // 渲染内容
  const renderBody = () => {
    const bodyPadding = typeof body.padding === 'boolean' 
      ? (body.padding ? 'p-4' : '') 
      : typeof body.padding === 'string' 
        ? body.padding 
        : body.padding 
          ? `p-${body.padding}` 
          : 'p-4'

    return (
      <div 
        className={cn(
          'fullscreen-modal-body',
          'flex-1',
          body.scrollable ? 'overflow-y-auto' : 'overflow-hidden',
          bodyPadding,
          body.className
        )}
        style={body.style}
      >
        {children}
      </div>
    )
  }

  // 构建动画类名
  const getAnimationClass = () => {
    if (!isAnimating) return ''
    
    const enterAnimation = animation.enter || 'slide-up'
    const exitAnimation = animation.exit || 'slide-down'
    const currentAnimation = modalProps.open ? enterAnimation : exitAnimation
    
    return `fullscreen-modal-${currentAnimation}`
  }

  // 如果不应该全屏，使用普通模态框
  if (!shouldFullScreen) {
    return (
      <Modal
        {...modalProps}
        className={cn('fullscreen-modal-desktop', className)}
        width="90vw"
        style={{ maxWidth: '1200px', ...modalProps.style }}
        onCancel={handleClose}
      >
        <div className="flex flex-col max-h-[80vh]">
          {renderHeader()}
          {renderBody()}
          {renderFooter()}
        </div>
      </Modal>
    )
  }

  // 移动端全屏模态框
  return (
    <Modal
      {...modalProps}
      width="100vw"
      height="100vh"
      style={{
        top: 0,
        paddingBottom: 0,
        maxWidth: 'none'
      }}
      className={cn(
        'fullscreen-modal',
        getAnimationClass(),
        className
      )}
      onCancel={handleClose}
      closeIcon={null}
      footer={null}
      bodyStyle={{ 
        padding: 0, 
        height: '100vh',
        display: 'flex',
        flexDirection: 'column'
      }}
      maskStyle={{ 
        backgroundColor: shouldFullScreen ? 'transparent' : 'rgba(0, 0, 0, 0.45)' 
      }}
    >
      <div 
        ref={modalRef}
        className="flex flex-col h-full bg-white mobile-safe-area-all"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {renderHeader()}
        {renderBody()}
        {renderFooter()}
      </div>
    </Modal>
  )
}

// 导出相关类型
export type { FullScreenModalProps }