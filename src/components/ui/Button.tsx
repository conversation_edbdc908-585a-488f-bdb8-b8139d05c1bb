'use client'

import { Button as AntButton, ButtonProps as AntButtonProps } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'
import { cn } from '@/lib/utils'
import { useResponsive } from '@/hooks/useResponsive'
import { useTouch } from '@/hooks/useTouch'
import { useState, useRef } from 'react'

interface ButtonProps extends Omit<AntButtonProps, 'loading' | 'variant'> {
  loading?: boolean
  loadingText?: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  fullWidth?: boolean
  /**
   * 是否启用触摸反馈效果
   */
  enableTouchFeedback?: boolean
  /**
   * 触摸反馈强度 ('light' | 'medium' | 'strong')
   */
  touchFeedbackIntensity?: 'light' | 'medium' | 'strong'
  /**
   * 是否启用长按功能
   */
  enableLongPress?: boolean
  /**
   * 长按事件回调
   */
  onLongPress?: () => void
  /**
   * 长按时间阈值（毫秒）
   */
  longPressThreshold?: number
}

export function Button({
  children,
  className,
  loading = false,
  loadingText,
  variant = 'primary',
  fullWidth = false,
  disabled,
  enableTouchFeedback = true,
  touchFeedbackIntensity = 'medium',
  enableLongPress = false,
  onLongPress,
  longPressThreshold = 500,
  ...props
}: ButtonProps) {
  const { isMobile } = useResponsive()
  const buttonRef = useRef<HTMLElement>(null)
  const [isPressed, setIsPressed] = useState(false)

  // 触摸手势处理
  const { touchHandlers } = useTouch(
    {
      enabled: isMobile && enableTouchFeedback,
      longPress: {
        enabled: enableLongPress,
        threshold: longPressThreshold
      }
    },
    {
      onTouchStart: () => {
        if (enableTouchFeedback && !disabled && !loading) {
          setIsPressed(true)
        }
      },
      onTouchEnd: () => {
        if (enableTouchFeedback) {
          setIsPressed(false)
        }
      },
      onLongPress: () => {
        if (enableLongPress && onLongPress && !disabled && !loading) {
          onLongPress()
        }
      }
    }
  )

  // 根据 variant 设置 Ant Design 的 type
  const getAntType = () => {
    switch (variant) {
      case 'primary':
        return 'primary'
      case 'danger':
        return 'primary'
      case 'outline':
        return 'default'
      case 'ghost':
        return 'text'
      case 'secondary':
      default:
        return 'default'
    }
  }

  // 根据 variant 设置额外的样式类
  const getVariantClass = () => {
    switch (variant) {
      case 'danger':
        return 'bg-red-500 border-red-500 hover:bg-red-600 hover:border-red-600'
      case 'outline':
        return 'border-primary-500 text-primary-500 hover:bg-primary-50'
      case 'ghost':
        return 'text-primary-500 hover:bg-primary-50'
      case 'secondary':
        return 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
      default:
        return ''
    }
  }

  // 获取触摸反馈样式类
  const getTouchFeedbackClass = () => {
    if (!enableTouchFeedback || !isMobile) return ''
    
    const baseClass = 'touch-feedback-button'
    const intensityClass = `touch-feedback-${touchFeedbackIntensity}`
    const pressedClass = isPressed ? 'touch-pressed' : ''
    
    return `${baseClass} ${intensityClass} ${pressedClass}`
  }

  return (
    <AntButton
      {...props}
      ref={buttonRef}
      type={getAntType()}
      disabled={disabled || loading}
      loading={loading}
      className={cn(
        'flex items-center justify-center transition-all duration-200',
        getVariantClass(),
        getTouchFeedbackClass(),
        fullWidth && 'w-full',
        isMobile && 'touch-target',
        className
      )}
      icon={loading ? <LoadingOutlined /> : props.icon}
      {...(isMobile ? touchHandlers : {})}
    >
      {loading && loadingText ? loadingText : children}
    </AntButton>
  )
}

// 预设的按钮组合
export function PrimaryButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="primary" />
}

export function SecondaryButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="secondary" />
}

export function OutlineButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="outline" />
}

export function GhostButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="ghost" />
}

export function DangerButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="danger" />
}

// 按钮组
interface ButtonGroupProps {
  children: React.ReactNode
  className?: string
  size?: 'small' | 'middle' | 'large'
}

export function ButtonGroup({
  children,
  className,
  size = 'middle',
}: ButtonGroupProps) {
  return (
    <AntButton.Group size={size} className={cn('flex', className)}>
      {children}
    </AntButton.Group>
  )
}
