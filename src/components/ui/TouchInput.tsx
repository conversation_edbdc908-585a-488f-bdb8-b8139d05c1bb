'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Input, InputNumber, Select, DatePicker, TimePicker, Switch, Slider } from 'antd'
import type { InputProps, InputNumberProps, SelectProps, DatePickerProps, TimePickerProps, SwitchProps, SliderSingleProps } from 'antd'
import { useResponsive } from '@/hooks/useResponsive'

const { TextArea, Password } = Input
const { Option } = Select

// 基础触摸输入组件属性
interface TouchInputBaseProps {
  /**
   * 触摸目标大小 ('min' | 'comfortable' | 'large')
   */
  touchSize?: 'min' | 'comfortable' | 'large'
  
  /**
   * 是否启用触摸反馈
   */
  enableTouchFeedback?: boolean
  
  /**
   * 是否启用自动聚焦优化
   */
  enableAutoFocus?: boolean
  
  /**
   * 键盘类型（移动端）
   */
  inputMode?: 'text' | 'numeric' | 'decimal' | 'tel' | 'email' | 'url' | 'search'
  
  /**
   * 自动完成类型
   */
  autoComplete?: string
}

// 触摸文本输入框
export interface TouchInputProps extends InputProps, TouchInputBaseProps {}

export function TouchInput({
  touchSize = 'comfortable',
  enableTouchFeedback = true,
  enableAutoFocus = true,
  inputMode,
  className = '',
  ...props
}: TouchInputProps) {
  const { isMobile } = useResponsive()
  const inputRef = useRef<any>(null)
  const [isFocused, setIsFocused] = useState(false)

  // 构建类名
  const inputClassName = [
    'touch-input',
    `touch-size-${touchSize}`,
    enableTouchFeedback ? 'touch-feedback' : '',
    isFocused ? 'touch-focused' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  // 处理聚焦
  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true)
    props.onFocus?.(e)
    
    // 移动端自动滚动到视图中
    if (isMobile && enableAutoFocus) {
      setTimeout(() => {
        e.target.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        })
      }, 300)
    }
  }

  // 处理失焦
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false)
    props.onBlur?.(e)
  }

  return (
    <Input
      {...props}
      ref={inputRef}
      className={inputClassName}
      onFocus={handleFocus}
      onBlur={handleBlur}
      inputMode={inputMode}
      style={{
        fontSize: isMobile ? '16px' : undefined, // 防止iOS缩放
        ...props.style
      }}
    />
  )
}

// 触摸密码输入框
export interface TouchPasswordProps extends InputProps, TouchInputBaseProps {}

export function TouchPassword({
  touchSize = 'comfortable',
  enableTouchFeedback = true,
  enableAutoFocus = true,
  className = '',
  ...props
}: TouchPasswordProps) {
  const { isMobile } = useResponsive()
  const [isFocused, setIsFocused] = useState(false)

  const inputClassName = [
    'touch-input',
    'touch-password',
    `touch-size-${touchSize}`,
    enableTouchFeedback ? 'touch-feedback' : '',
    isFocused ? 'touch-focused' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true)
    props.onFocus?.(e)
    
    if (isMobile && enableAutoFocus) {
      setTimeout(() => {
        e.target.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        })
      }, 300)
    }
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false)
    props.onBlur?.(e)
  }

  return (
    <Password
      {...props}
      className={inputClassName}
      onFocus={handleFocus}
      onBlur={handleBlur}
      style={{
        fontSize: isMobile ? '16px' : undefined,
        ...props.style
      }}
    />
  )
}

// 触摸文本域
export interface TouchTextAreaProps extends React.ComponentProps<typeof TextArea>, TouchInputBaseProps {}

export function TouchTextArea({
  touchSize = 'comfortable',
  enableTouchFeedback = true,
  enableAutoFocus = true,
  className = '',
  ...props
}: TouchTextAreaProps) {
  const { isMobile } = useResponsive()
  const [isFocused, setIsFocused] = useState(false)

  const textAreaClassName = [
    'touch-textarea',
    `touch-size-${touchSize}`,
    enableTouchFeedback ? 'touch-feedback' : '',
    isFocused ? 'touch-focused' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setIsFocused(true)
    props.onFocus?.(e)
    
    if (isMobile && enableAutoFocus) {
      setTimeout(() => {
        e.target.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        })
      }, 300)
    }
  }

  const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setIsFocused(false)
    props.onBlur?.(e)
  }

  return (
    <TextArea
      {...props}
      className={textAreaClassName}
      onFocus={handleFocus}
      onBlur={handleBlur}
      style={{
        fontSize: isMobile ? '16px' : undefined,
        minHeight: isMobile ? '44px' : undefined,
        ...props.style
      }}
    />
  )
}

// 触摸数字输入框
export interface TouchInputNumberProps extends InputNumberProps, TouchInputBaseProps {}

export function TouchInputNumber({
  touchSize = 'comfortable',
  enableTouchFeedback = true,
  enableAutoFocus = true,
  className = '',
  ...props
}: TouchInputNumberProps) {
  const { isMobile } = useResponsive()
  const [isFocused, setIsFocused] = useState(false)

  const inputClassName = [
    'touch-input-number',
    `touch-size-${touchSize}`,
    enableTouchFeedback ? 'touch-feedback' : '',
    isFocused ? 'touch-focused' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true)
    props.onFocus?.(e)
    
    if (isMobile && enableAutoFocus) {
      setTimeout(() => {
        e.target.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        })
      }, 300)
    }
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false)
    props.onBlur?.(e)
  }

  return (
    <InputNumber
      {...props}
      className={inputClassName}
      onFocus={handleFocus}
      onBlur={handleBlur}
      style={{
        width: '100%',
        fontSize: isMobile ? '16px' : undefined,
        ...props.style
      }}
    />
  )
}

// 触摸选择器
export interface TouchSelectProps extends SelectProps, TouchInputBaseProps {}

export function TouchSelect({
  touchSize = 'comfortable',
  enableTouchFeedback = true,
  enableAutoFocus = true,
  className = '',
  children,
  ...props
}: TouchSelectProps) {
  const { isMobile } = useResponsive()
  const [isFocused, setIsFocused] = useState(false)

  const selectClassName = [
    'touch-select',
    `touch-size-${touchSize}`,
    enableTouchFeedback ? 'touch-feedback' : '',
    isFocused ? 'touch-focused' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  const handleFocus = () => {
    setIsFocused(true)
    props.onFocus?.()
  }

  const handleBlur = () => {
    setIsFocused(false)
    props.onBlur?.()
  }

  return (
    <Select
      {...props}
      className={selectClassName}
      onFocus={handleFocus}
      onBlur={handleBlur}
      dropdownClassName={`touch-select-dropdown ${isMobile ? 'mobile-optimized' : ''}`}
      style={{
        fontSize: isMobile ? '16px' : undefined,
        ...props.style
      }}
    >
      {children}
    </Select>
  )
}

// 触摸日期选择器
export interface TouchDatePickerProps extends DatePickerProps, TouchInputBaseProps {}

export function TouchDatePicker({
  touchSize = 'comfortable',
  enableTouchFeedback = true,
  enableAutoFocus = true,
  className = '',
  ...props
}: TouchDatePickerProps) {
  const { isMobile } = useResponsive()
  const [isFocused, setIsFocused] = useState(false)

  const datePickerClassName = [
    'touch-datepicker',
    `touch-size-${touchSize}`,
    enableTouchFeedback ? 'touch-feedback' : '',
    isFocused ? 'touch-focused' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  const handleFocus = () => {
    setIsFocused(true)
    props.onFocus?.()
  }

  const handleBlur = () => {
    setIsFocused(false)
    props.onBlur?.()
  }

  return (
    <DatePicker
      {...props}
      className={datePickerClassName}
      onFocus={handleFocus}
      onBlur={handleBlur}
      popupClassName={`touch-datepicker-popup ${isMobile ? 'mobile-optimized' : ''}`}
      style={{
        width: '100%',
        fontSize: isMobile ? '16px' : undefined,
        ...props.style
      }}
    />
  )
}

// 触摸时间选择器
export interface TouchTimePickerProps extends TimePickerProps, TouchInputBaseProps {}

export function TouchTimePicker({
  touchSize = 'comfortable',
  enableTouchFeedback = true,
  enableAutoFocus = true,
  className = '',
  ...props
}: TouchTimePickerProps) {
  const { isMobile } = useResponsive()
  const [isFocused, setIsFocused] = useState(false)

  const timePickerClassName = [
    'touch-timepicker',
    `touch-size-${touchSize}`,
    enableTouchFeedback ? 'touch-feedback' : '',
    isFocused ? 'touch-focused' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  const handleFocus = () => {
    setIsFocused(true)
    props.onFocus?.()
  }

  const handleBlur = () => {
    setIsFocused(false)
    props.onBlur?.()
  }

  return (
    <TimePicker
      {...props}
      className={timePickerClassName}
      onFocus={handleFocus}
      onBlur={handleBlur}
      popupClassName={`touch-timepicker-popup ${isMobile ? 'mobile-optimized' : ''}`}
      style={{
        width: '100%',
        fontSize: isMobile ? '16px' : undefined,
        ...props.style
      }}
    />
  )
}

// 触摸开关
export interface TouchSwitchProps extends SwitchProps, TouchInputBaseProps {}

export function TouchSwitch({
  touchSize = 'comfortable',
  enableTouchFeedback = true,
  className = '',
  ...props
}: TouchSwitchProps) {
  const { isMobile } = useResponsive()

  const switchClassName = [
    'touch-switch',
    `touch-size-${touchSize}`,
    enableTouchFeedback ? 'touch-feedback' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  return (
    <Switch
      {...props}
      className={switchClassName}
      size={isMobile ? 'default' : props.size}
    />
  )
}

// 触摸滑块
export interface TouchSliderProps extends SliderSingleProps, TouchInputBaseProps {}

export function TouchSlider({
  touchSize = 'comfortable',
  enableTouchFeedback = true,
  className = '',
  ...props
}: TouchSliderProps) {
  const { isMobile } = useResponsive()

  const sliderClassName = [
    'touch-slider',
    `touch-size-${touchSize}`,
    enableTouchFeedback ? 'touch-feedback' : '',
    isMobile ? 'mobile-optimized' : '',
    className
  ].filter(Boolean).join(' ')

  return (
    <Slider
      {...props}
      className={sliderClassName}
    />
  )
}

// 导出所有组件和类型
export type {
  TouchInputBaseProps,
  TouchInputProps,
  TouchPasswordProps,
  TouchTextAreaProps,
  TouchInputNumberProps,
  TouchSelectProps,
  TouchDatePickerProps,
  TouchTimePickerProps,
  TouchSwitchProps,
  TouchSliderProps
}