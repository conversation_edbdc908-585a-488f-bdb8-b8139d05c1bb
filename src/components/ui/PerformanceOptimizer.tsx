'use client'

import React, { useEffect, useCallback, useRef } from 'react'
import { useResponsive } from '@/hooks/useResponsive'

interface PerformanceOptimizerProps {
  children: React.ReactNode
  enableVirtualScrolling?: boolean
  enableImageLazyLoading?: boolean
  enableResourcePreloading?: boolean
  enableMemoryOptimization?: boolean
  className?: string
}

export function PerformanceOptimizer({
  children,
  enableVirtualScrolling = true,
  enableImageLazyLoading = true,
  enableResourcePreloading = true,
  enableMemoryOptimization = true,
  className = ''
}: PerformanceOptimizerProps) {
  const { isMobile } = useResponsive()
  const containerRef = useRef<HTMLDivElement>(null)
  const preloadedResources = useRef<Set<string>>(new Set())
  const intersectionObserver = useRef<IntersectionObserver | null>(null)

  // 预加载关键资源
  const preloadCriticalResources = useCallback(() => {
    if (!enableResourcePreloading) return

    // 预加载关键CSS
    const criticalCSS = [
      '/styles/mobile.css',
      // 添加其他关键CSS文件
    ]

    criticalCSS.forEach(href => {
      if (!preloadedResources.current.has(href)) {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.as = 'style'
        link.href = href
        document.head.appendChild(link)
        preloadedResources.current.add(href)
      }
    })

    // 预加载关键字体
    const criticalFonts = [
      // 添加关键字体文件
    ]

    criticalFonts.forEach(href => {
      if (!preloadedResources.current.has(href)) {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.as = 'font'
        link.type = 'font/woff2'
        link.crossOrigin = 'anonymous'
        link.href = href
        document.head.appendChild(link)
        preloadedResources.current.add(href)
      }
    })
  }, [enableResourcePreloading])

  // 优化图片加载
  const optimizeImageLoading = useCallback(() => {
    if (!enableImageLazyLoading || !containerRef.current) return

    // 创建 Intersection Observer 用于懒加载
    intersectionObserver.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            
            // 加载图片
            if (img.dataset.src) {
              img.src = img.dataset.src
              img.removeAttribute('data-src')
            }

            // 加载 srcset
            if (img.dataset.srcset) {
              img.srcset = img.dataset.srcset
              img.removeAttribute('data-srcset')
            }

            // 停止观察已加载的图片
            intersectionObserver.current?.unobserve(img)
          }
        })
      },
      {
        rootMargin: isMobile ? '50px' : '100px', // 移动端提前加载距离更小
        threshold: 0.1
      }
    )

    // 观察所有懒加载图片
    const lazyImages = containerRef.current.querySelectorAll('img[data-src]')
    lazyImages.forEach(img => {
      intersectionObserver.current?.observe(img)
    })
  }, [enableImageLazyLoading, isMobile])

  // 内存优化
  const optimizeMemory = useCallback(() => {
    if (!enableMemoryOptimization) return

    // 清理未使用的事件监听器
    const cleanupEventListeners = () => {
      // 移除已分离的DOM元素的事件监听器
      const elements = document.querySelectorAll('[data-cleanup-listeners]')
      elements.forEach(element => {
        if (!element.isConnected) {
          // 清理事件监听器的逻辑
          element.removeAttribute('data-cleanup-listeners')
        }
      })
    }

    // 清理大型对象缓存
    const cleanupCache = () => {
      // 清理过期的缓存数据
      if ('caches' in window) {
        caches.keys().then(cacheNames => {
          cacheNames.forEach(cacheName => {
            if (cacheName.includes('old-') || cacheName.includes('temp-')) {
              caches.delete(cacheName)
            }
          })
        })
      }
    }

    // 定期执行内存清理
    const memoryCleanupInterval = setInterval(() => {
      cleanupEventListeners()
      cleanupCache()
      
      // 在移动设备上更频繁地清理
      if (isMobile && 'gc' in window && process.env.NODE_ENV === 'development') {
        (window as any).gc()
      }
    }, isMobile ? 30000 : 60000) // 移动端30秒，桌面端60秒

    return () => clearInterval(memoryCleanupInterval)
  }, [enableMemoryOptimization, isMobile])

  // 优化滚动性能
  const optimizeScrolling = useCallback(() => {
    if (!enableVirtualScrolling || !containerRef.current) return

    let ticking = false

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          // 滚动优化逻辑
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop
          
          // 隐藏不在视口中的复杂元素
          const complexElements = document.querySelectorAll('[data-complex-render]')
          complexElements.forEach(element => {
            const rect = element.getBoundingClientRect()
            const isVisible = rect.top < window.innerHeight && rect.bottom > 0
            
            if (isVisible) {
              element.removeAttribute('data-hidden')
            } else {
              element.setAttribute('data-hidden', 'true')
            }
          })

          ticking = false
        })
        ticking = true
      }
    }

    // 使用被动事件监听器优化滚动性能
    window.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [enableVirtualScrolling])

  // 预加载下一页内容
  const preloadNextPage = useCallback(() => {
    if (!enableResourcePreloading) return

    // 检测用户滚动到页面底部附近时预加载下一页
    const handleScrollForPreload = () => {
      const scrollHeight = document.documentElement.scrollHeight
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const clientHeight = window.innerHeight

      // 当滚动到页面80%时开始预加载
      if (scrollTop + clientHeight >= scrollHeight * 0.8) {
        // 预加载下一页的逻辑
        const nextPageLinks = document.querySelectorAll('a[data-preload]')
        nextPageLinks.forEach(link => {
          const href = (link as HTMLAnchorElement).href
          if (!preloadedResources.current.has(href)) {
            const prefetchLink = document.createElement('link')
            prefetchLink.rel = 'prefetch'
            prefetchLink.href = href
            document.head.appendChild(prefetchLink)
            preloadedResources.current.add(href)
          }
        })
      }
    }

    window.addEventListener('scroll', handleScrollForPreload, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScrollForPreload)
    }
  }, [enableResourcePreloading])

  // 初始化性能优化
  useEffect(() => {
    const cleanupFunctions: Array<() => void> = []

    // 预加载关键资源
    preloadCriticalResources()

    // 优化图片加载
    optimizeImageLoading()

    // 内存优化
    const memoryCleanup = optimizeMemory()
    if (memoryCleanup) cleanupFunctions.push(memoryCleanup)

    // 滚动优化
    const scrollCleanup = optimizeScrolling()
    if (scrollCleanup) cleanupFunctions.push(scrollCleanup)

    // 预加载下一页
    const preloadCleanup = preloadNextPage()
    if (preloadCleanup) cleanupFunctions.push(preloadCleanup)

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup())
      intersectionObserver.current?.disconnect()
    }
  }, [
    preloadCriticalResources,
    optimizeImageLoading,
    optimizeMemory,
    optimizeScrolling,
    preloadNextPage
  ])

  // 添加性能优化的CSS类
  const optimizedClassName = [
    className,
    'performance-optimized',
    isMobile && 'mobile-optimized',
    enableVirtualScrolling && 'virtual-scroll-enabled',
    enableImageLazyLoading && 'lazy-loading-enabled'
  ].filter(Boolean).join(' ')

  return (
    <div
      ref={containerRef}
      className={optimizedClassName}
      style={{
        // 启用硬件加速
        transform: 'translateZ(0)',
        // 优化重绘性能
        willChange: 'auto',
        // 启用GPU合成
        backfaceVisibility: 'hidden',
        // 优化字体渲染
        textRendering: 'optimizeSpeed',
        // 图片渲染优化
        imageRendering: isMobile ? 'optimizeSpeed' : 'auto'
      }}
    >
      {children}
    </div>
  )
}

// 性能监控组件
interface PerformanceMonitorProps {
  showMetrics?: boolean
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  className?: string
}

export function PerformanceMonitor({
  showMetrics = process.env.NODE_ENV === 'development',
  position = 'top-right',
  className = ''
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = React.useState<{
    fps: number
    memory: number
    loadTime: number
  }>({ fps: 0, memory: 0, loadTime: 0 })

  useEffect(() => {
    if (!showMetrics) return

    let frameCount = 0
    let lastTime = performance.now()
    let animationId: number

    const measureFPS = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        
        setMetrics(prev => ({
          ...prev,
          fps,
          memory: (performance as any).memory?.usedJSHeapSize || 0,
          loadTime: performance.timing?.loadEventEnd - performance.timing?.navigationStart || 0
        }))

        frameCount = 0
        lastTime = currentTime
      }

      animationId = requestAnimationFrame(measureFPS)
    }

    measureFPS()

    return () => {
      cancelAnimationFrame(animationId)
    }
  }, [showMetrics])

  if (!showMetrics) return null

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  }

  return (
    <div
      className={`fixed ${positionClasses[position]} bg-black bg-opacity-75 text-white text-xs p-2 rounded font-mono z-50 ${className}`}
      style={{ pointerEvents: 'none' }}
    >
      <div>FPS: {metrics.fps}</div>
      <div>Memory: {Math.round(metrics.memory / 1024 / 1024)}MB</div>
      <div>Load: {Math.round(metrics.loadTime)}ms</div>
    </div>
  )
}