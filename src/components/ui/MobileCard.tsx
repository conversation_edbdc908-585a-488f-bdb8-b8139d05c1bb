'use client'

import React from 'react'
import { Card, Space, Typography, Tag, Button, Divider } from 'antd'
import { MoreOutlined } from '@ant-design/icons'
import type { AdaptiveTableColumn } from './AdaptiveTable'

const { Text, Title } = Typography

export interface MobileCardProps<T = any> {
  /**
   * 数据记录
   */
  data: T
  
  /**
   * 表格列配置
   */
  columns: AdaptiveTableColumn<T>[]
  
  /**
   * 卡片配置
   */
  config?: {
    titleField?: string
    subtitleField?: string
    descriptionField?: string
    customRender?: (record: T, index: number) => React.ReactNode
  }
  
  /**
   * 记录索引
   */
  index: number
  
  /**
   * 点击事件
   */
  onClick?: (record: T, index: number) => void
  
  /**
   * 操作按钮
   */
  actions?: React.ReactNode[]
  
  /**
   * 额外的CSS类名
   */
  className?: string
}

/**
 * 移动端卡片组件
 * 用于在移动端以卡片形式展示表格数据
 */
export function MobileCard<T extends Record<string, any>>({
  data,
  columns,
  config,
  index,
  onClick,
  actions,
  className = ''
}: MobileCardProps<T>) {
  
  // 获取卡片标题
  const getCardTitle = () => {
    if (config?.titleField && data[config.titleField]) {
      return data[config.titleField]
    }
    
    // 默认使用第一个字符串字段作为标题
    const titleColumn = columns.find(col => 
      col.dataIndex && typeof data[col.dataIndex as string] === 'string'
    )
    
    if (titleColumn && titleColumn.dataIndex) {
      return data[titleColumn.dataIndex as string]
    }
    
    return `记录 ${index + 1}`
  }

  // 获取卡片副标题
  const getCardSubtitle = () => {
    if (config?.subtitleField && data[config.subtitleField]) {
      return data[config.subtitleField]
    }
    return null
  }

  // 获取卡片描述
  const getCardDescription = () => {
    if (config?.descriptionField && data[config.descriptionField]) {
      return data[config.descriptionField]
    }
    return null
  }

  // 渲染字段值
  const renderFieldValue = (column: AdaptiveTableColumn<T>, value: any) => {
    // 优先使用卡片专用渲染函数
    if (column.cardRender) {
      return column.cardRender(value, data, index)
    }
    
    // 使用通用渲染函数
    if (column.render) {
      return column.render(value, data, index)
    }
    
    // 默认渲染
    if (value === null || value === undefined) {
      return <Text type="secondary">-</Text>
    }
    
    if (typeof value === 'boolean') {
      return (
        <Tag color={value ? 'success' : 'default'}>
          {value ? '是' : '否'}
        </Tag>
      )
    }
    
    if (Array.isArray(value)) {
      return (
        <Space wrap>
          {value.map((item, idx) => (
            <Tag key={idx}>{String(item)}</Tag>
          ))}
        </Space>
      )
    }
    
    return String(value)
  }

  // 获取要显示的字段
  const getDisplayFields = () => {
    return columns.filter(column => {
      // 如果明确设置了 showInCard，则遵循该设置
      if (column.showInCard !== undefined) {
        return column.showInCard
      }
      
      // 默认显示优先级1-3的字段
      return !column.mobilePriority || column.mobilePriority <= 3
    })
  }

  // 自定义渲染
  if (config?.customRender) {
    return (
      <Card 
        className={`mobile-card ${className}`}
        onClick={() => onClick?.(data, index)}
      >
        {config.customRender(data, index)}
      </Card>
    )
  }

  const title = getCardTitle()
  const subtitle = getCardSubtitle()
  const description = getCardDescription()
  const displayFields = getDisplayFields()

  return (
    <Card
      className={`mobile-card ${onClick ? 'mobile-card-clickable' : ''} ${className}`}
      onClick={() => onClick?.(data, index)}
      actions={actions}
      size="small"
    >
      {/* 卡片头部 */}
      <div className="mobile-card-header">
        <div className="flex-1">
          {/* 主标题 */}
          <div className="mobile-card-title">
            <Text strong className="text-base">
              {title}
            </Text>
          </div>
          
          {/* 副标题 */}
          {subtitle && (
            <div className="mobile-card-subtitle mt-1">
              <Text type="secondary" className="text-sm">
                {subtitle}
              </Text>
            </div>
          )}
        </div>
        
        {/* 操作按钮 */}
        {actions && actions.length > 0 && (
          <div className="mobile-card-actions">
            <Button 
              type="text" 
              icon={<MoreOutlined />} 
              size="small"
              onClick={(e) => {
                e.stopPropagation()
                // 这里可以触发操作菜单
              }}
            />
          </div>
        )}
      </div>

      {/* 描述信息 */}
      {description && (
        <>
          <Divider className="my-3" />
          <div className="mobile-card-description">
            <Text className="text-sm">
              {description}
            </Text>
          </div>
        </>
      )}

      {/* 字段列表 */}
      {displayFields.length > 0 && (
        <>
          <Divider className="my-3" />
          <div className="mobile-card-fields">
            <Space direction="vertical" size="small" className="w-full">
              {displayFields.map((column, fieldIndex) => {
                const fieldKey = column.dataIndex as string
                const fieldValue = data[fieldKey]
                const fieldLabel = column.cardLabel || column.title
                
                // 跳过标题字段，避免重复显示
                if (fieldKey === config?.titleField || 
                    fieldKey === config?.subtitleField || 
                    fieldKey === config?.descriptionField) {
                  return null
                }
                
                return (
                  <div key={fieldKey || fieldIndex} className="mobile-card-field">
                    <div className="flex justify-between items-start">
                      <div className="mobile-card-field-label flex-shrink-0 mr-3">
                        <Text type="secondary" className="text-xs">
                          {fieldLabel}:
                        </Text>
                      </div>
                      <div className="mobile-card-field-value flex-1 text-right">
                        <div className="text-sm">
                          {renderFieldValue(column, fieldValue)}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </Space>
          </div>
        </>
      )}
    </Card>
  )
}

// 导出相关类型
export type { MobileCardProps }