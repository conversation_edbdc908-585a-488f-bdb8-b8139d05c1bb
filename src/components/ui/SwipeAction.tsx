'use client'

import React, { useState, useRef, useCallback } from 'react'
import { useTouch } from '@/hooks/useTouch'

export interface ActionConfig {
  key: string
  label: string
  icon?: React.ReactNode
  color?: 'primary' | 'danger' | 'warning' | 'success'
  onClick?: () => void
}

export interface SwipeActionProps {
  children: React.ReactNode
  leftActions?: ActionConfig[]
  rightActions?: ActionConfig[]
  onAction?: (action: string) => void
  className?: string
  disabled?: boolean
  swipeThreshold?: number
}

export function SwipeAction({
  children,
  leftActions = [],
  rightActions = [],
  onAction,
  className = '',
  disabled = false,
  swipeThreshold = 80
}: SwipeActionProps) {
  const [translateX, setTranslateX] = useState(0)
  const [isRevealed, setIsRevealed] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const startXRef = useRef(0)
  const isDraggingRef = useRef(false)

  const getActionColor = (color?: string) => {
    switch (color) {
      case 'danger':
        return 'bg-red-500 text-white'
      case 'warning':
        return 'bg-yellow-500 text-white'
      case 'success':
        return 'bg-green-500 text-white'
      case 'primary':
      default:
        return 'bg-blue-500 text-white'
    }
  }

  const handleActionClick = useCallback((action: ActionConfig) => {
    action.onClick?.()
    onAction?.(action.key)
    // Reset position after action
    setTranslateX(0)
    setIsRevealed(false)
  }, [onAction])

  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (disabled) return
    
    const touch = event.touches[0]
    startXRef.current = touch.clientX
    isDraggingRef.current = true
  }, [disabled])

  const handleTouchMove = useCallback((event: React.TouchEvent) => {
    if (disabled || !isDraggingRef.current) return

    const touch = event.touches[0]
    const deltaX = touch.clientX - startXRef.current
    
    // Limit swipe distance
    const maxLeftSwipe = leftActions.length > 0 ? leftActions.length * 80 : 0
    const maxRightSwipe = rightActions.length > 0 ? rightActions.length * 80 : 0
    
    let newTranslateX = deltaX
    
    if (deltaX > 0) {
      // Swiping right (revealing left actions)
      newTranslateX = Math.min(deltaX, maxLeftSwipe)
    } else {
      // Swiping left (revealing right actions)
      newTranslateX = Math.max(deltaX, -maxRightSwipe)
    }
    
    setTranslateX(newTranslateX)
  }, [disabled, leftActions.length, rightActions.length])

  const handleTouchEnd = useCallback(() => {
    if (disabled) return
    
    isDraggingRef.current = false
    
    // Determine if actions should be revealed
    const shouldRevealLeft = translateX > swipeThreshold && leftActions.length > 0
    const shouldRevealRight = translateX < -swipeThreshold && rightActions.length > 0
    
    if (shouldRevealLeft) {
      setTranslateX(leftActions.length * 80)
      setIsRevealed(true)
    } else if (shouldRevealRight) {
      setTranslateX(-rightActions.length * 80)
      setIsRevealed(true)
    } else {
      setTranslateX(0)
      setIsRevealed(false)
    }
  }, [disabled, translateX, swipeThreshold, leftActions.length, rightActions.length])

  const { touchHandlers } = useTouch({
    onSwipe: (direction, distance) => {
      if (disabled) return
      
      if (direction === 'left' && rightActions.length > 0) {
        setTranslateX(-rightActions.length * 80)
        setIsRevealed(true)
      } else if (direction === 'right' && leftActions.length > 0) {
        setTranslateX(leftActions.length * 80)
        setIsRevealed(true)
      }
    }
  })

  // Close actions when clicking outside
  const handleContainerClick = useCallback((event: React.MouseEvent) => {
    if (isRevealed && event.target === event.currentTarget) {
      setTranslateX(0)
      setIsRevealed(false)
    }
  }, [isRevealed])

  return (
    <div 
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      onClick={handleContainerClick}
    >
      {/* Left Actions */}
      {leftActions.length > 0 && (
        <div 
          className="absolute left-0 top-0 h-full flex"
          style={{ 
            transform: `translateX(${Math.min(0, translateX - leftActions.length * 80)}px)`,
            transition: isDraggingRef.current ? 'none' : 'transform 0.3s ease'
          }}
        >
          {leftActions.map((action, index) => (
            <button
              key={action.key}
              className={`w-20 h-full flex flex-col items-center justify-center text-sm font-medium ${getActionColor(action.color)} hover:opacity-90 active:opacity-75`}
              onClick={() => handleActionClick(action)}
            >
              {action.icon && (
                <div className="mb-1">
                  {action.icon}
                </div>
              )}
              <span className="text-xs">{action.label}</span>
            </button>
          ))}
        </div>
      )}

      {/* Right Actions */}
      {rightActions.length > 0 && (
        <div 
          className="absolute right-0 top-0 h-full flex"
          style={{ 
            transform: `translateX(${Math.max(0, translateX + rightActions.length * 80)}px)`,
            transition: isDraggingRef.current ? 'none' : 'transform 0.3s ease'
          }}
        >
          {rightActions.map((action, index) => (
            <button
              key={action.key}
              className={`w-20 h-full flex flex-col items-center justify-center text-sm font-medium ${getActionColor(action.color)} hover:opacity-90 active:opacity-75`}
              onClick={() => handleActionClick(action)}
            >
              {action.icon && (
                <div className="mb-1">
                  {action.icon}
                </div>
              )}
              <span className="text-xs">{action.label}</span>
            </button>
          ))}
        </div>
      )}

      {/* Main Content */}
      <div
        className="relative z-10 bg-white"
        style={{
          transform: `translateX(${translateX}px)`,
          transition: isDraggingRef.current ? 'none' : 'transform 0.3s ease'
        }}
        {...touchHandlers}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {children}
      </div>
    </div>
  )
}