'use client'

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { message } from 'antd'

// Service Worker 状态接口
export interface ServiceWorkerState {
  isSupported: boolean
  isRegistered: boolean
  isInstalling: boolean
  isWaiting: boolean
  isActive: boolean
  hasUpdate: boolean
  registration: ServiceWorkerRegistration | null
  error: string | null
}

// Service Worker 操作接口
export interface ServiceWorkerActions {
  register: () => Promise<void>
  unregister: () => Promise<void>
  update: () => Promise<void>
  skipWaiting: () => void
  clearError: () => void
  sendMessage: (message: any) => void
}

// Context 接口
interface ServiceWorkerContextType {
  state: ServiceWorkerState
  actions: ServiceWorkerActions
}

// 创建 Context
const ServiceWorkerContext = createContext<ServiceWorkerContextType | null>(null)

// Provider Props
interface ServiceWorkerProviderProps {
  children: React.ReactNode
  swPath?: string
  options?: RegistrationOptions
  onInstalled?: () => void
  onUpdated?: () => void
  onError?: (error: Error) => void
}

/**
 * Service Worker Provider 组件
 * 管理 Service Worker 的注册、更新和状态
 */
export function ServiceWorkerProvider({
  children,
  swPath = '/service-worker.js',
  options = { scope: '/' },
  onInstalled,
  onUpdated,
  onError
}: ServiceWorkerProviderProps) {
  const [state, setState] = useState<ServiceWorkerState>({
    isSupported: 'serviceWorker' in navigator,
    isRegistered: false,
    isInstalling: false,
    isWaiting: false,
    isActive: false,
    hasUpdate: false,
    registration: null,
    error: null
  })

  // 更新状态
  const updateState = useCallback((updates: Partial<ServiceWorkerState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  // 注册 Service Worker
  const register = useCallback(async () => {
    if (!state.isSupported) {
      const error = 'Service Worker 不被当前浏览器支持'
      updateState({ error })
      onError?.(new Error(error))
      return
    }

    try {
      updateState({ isInstalling: true, error: null })

      const registration = await navigator.serviceWorker.register(swPath, options)
      
      updateState({
        isRegistered: true,
        isInstalling: false,
        registration
      })

      // 监听 Service Worker 状态变化
      if (registration.installing) {
        updateState({ isInstalling: true })
        registration.installing.addEventListener('statechange', handleStateChange)
      }

      if (registration.waiting) {
        updateState({ isWaiting: true, hasUpdate: true })
      }

      if (registration.active) {
        updateState({ isActive: true })
        onInstalled?.()
      }

      // 监听更新
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          updateState({ isInstalling: true })
          newWorker.addEventListener('statechange', handleStateChange)
        }
      })

      console.log('[ServiceWorker] Registered successfully')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Service Worker 注册失败'
      updateState({ 
        isInstalling: false, 
        error: errorMessage 
      })
      onError?.(error instanceof Error ? error : new Error(errorMessage))
      console.error('[ServiceWorker] Registration failed:', error)
    }
  }, [state.isSupported, swPath, options, onInstalled, onError, updateState])

  // 处理 Service Worker 状态变化
  const handleStateChange = useCallback((event: Event) => {
    const worker = event.target as ServiceWorker
    
    switch (worker.state) {
      case 'installing':
        updateState({ isInstalling: true })
        break
        
      case 'installed':
        updateState({ 
          isInstalling: false,
          isWaiting: true,
          hasUpdate: true
        })
        
        if (navigator.serviceWorker.controller) {
          // 有新版本可用
          message.info({
            content: '发现新版本，点击刷新页面以获取最新功能',
            duration: 0,
            key: 'sw-update'
          })
          onUpdated?.()
        } else {
          // 首次安装
          onInstalled?.()
        }
        break
        
      case 'activating':
        updateState({ isWaiting: false })
        break
        
      case 'activated':
        updateState({ 
          isActive: true,
          hasUpdate: false
        })
        
        // 关闭更新提示
        message.destroy('sw-update')
        
        if (navigator.serviceWorker.controller) {
          message.success('应用已更新到最新版本')
        }
        break
        
      case 'redundant':
        updateState({ 
          isActive: false,
          error: 'Service Worker 已失效'
        })
        break
    }
  }, [updateState, onInstalled, onUpdated])

  // 注销 Service Worker
  const unregister = useCallback(async () => {
    if (!state.registration) return

    try {
      const result = await state.registration.unregister()
      if (result) {
        updateState({
          isRegistered: false,
          isActive: false,
          registration: null
        })
        console.log('[ServiceWorker] Unregistered successfully')
      }
    } catch (error) {
      const errorMessage = 'Service Worker 注销失败'
      updateState({ error: errorMessage })
      console.error('[ServiceWorker] Unregistration failed:', error)
    }
  }, [state.registration, updateState])

  // 更新 Service Worker
  const update = useCallback(async () => {
    if (!state.registration) return

    try {
      await state.registration.update()
      console.log('[ServiceWorker] Update check completed')
    } catch (error) {
      console.error('[ServiceWorker] Update check failed:', error)
    }
  }, [state.registration])

  // 跳过等待，立即激活新版本
  const skipWaiting = useCallback(() => {
    if (state.registration?.waiting) {
      state.registration.waiting.postMessage({ type: 'SKIP_WAITING' })
    }
  }, [state.registration])

  // 清除错误
  const clearError = useCallback(() => {
    updateState({ error: null })
  }, [updateState])

  // 发送消息给 Service Worker
  const sendMessage = useCallback((message: any) => {
    if (state.registration?.active) {
      state.registration.active.postMessage(message)
    }
  }, [state.registration])

  // 监听来自 Service Worker 的消息
  useEffect(() => {
    if (!state.isSupported) return

    const handleMessage = (event: MessageEvent) => {
      console.log('[ServiceWorker] Message received:', event.data)
      
      // 处理特定消息类型
      switch (event.data?.type) {
        case 'SW_UPDATED':
          updateState({ hasUpdate: true })
          break
          
        case 'SW_OFFLINE':
          message.warning('网络连接已断开，已切换到离线模式')
          break
          
        case 'SW_ONLINE':
          message.success('网络连接已恢复')
          break
          
        case 'SW_CACHE_UPDATED':
          console.log('[ServiceWorker] Cache updated')
          break
      }
    }

    navigator.serviceWorker.addEventListener('message', handleMessage)
    
    return () => {
      navigator.serviceWorker.removeEventListener('message', handleMessage)
    }
  }, [state.isSupported, updateState])

  // 监听 Service Worker 控制器变化
  useEffect(() => {
    if (!state.isSupported) return

    const handleControllerChange = () => {
      console.log('[ServiceWorker] Controller changed')
      window.location.reload()
    }

    navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange)
    
    return () => {
      navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange)
    }
  }, [state.isSupported])

  // 自动注册 Service Worker
  useEffect(() => {
    if (state.isSupported && !state.isRegistered) {
      register()
    }
  }, [state.isSupported, state.isRegistered, register])

  // 定期检查更新
  useEffect(() => {
    if (!state.registration) return

    const checkForUpdates = () => {
      update()
    }

    // 每30分钟检查一次更新
    const interval = setInterval(checkForUpdates, 30 * 60 * 1000)
    
    // 页面获得焦点时检查更新
    const handleFocus = () => {
      checkForUpdates()
    }
    
    window.addEventListener('focus', handleFocus)
    
    return () => {
      clearInterval(interval)
      window.removeEventListener('focus', handleFocus)
    }
  }, [state.registration, update])

  const actions: ServiceWorkerActions = {
    register,
    unregister,
    update,
    skipWaiting,
    clearError,
    sendMessage
  }

  const contextValue: ServiceWorkerContextType = {
    state,
    actions
  }

  return (
    <ServiceWorkerContext.Provider value={contextValue}>
      {children}
    </ServiceWorkerContext.Provider>
  )
}

/**
 * 使用 Service Worker 的 Hook
 */
export function useServiceWorker(): ServiceWorkerContextType {
  const context = useContext(ServiceWorkerContext)
  
  if (!context) {
    throw new Error('useServiceWorker must be used within a ServiceWorkerProvider')
  }
  
  return context
}

/**
 * Service Worker 状态指示器组件
 */
export function ServiceWorkerStatus() {
  const { state, actions } = useServiceWorker()

  if (!state.isSupported) {
    return null
  }

  return (
    <div className="service-worker-status">
      {state.error && (
        <div className="sw-error">
          <span>Service Worker 错误: {state.error}</span>
          <button onClick={actions.clearError}>清除</button>
        </div>
      )}
      
      {state.hasUpdate && (
        <div className="sw-update">
          <span>发现新版本</span>
          <button onClick={actions.skipWaiting}>立即更新</button>
        </div>
      )}
      
      {state.isInstalling && (
        <div className="sw-installing">
          <span>正在安装更新...</span>
        </div>
      )}
    </div>
  )
}

// 导出相关类型
export type { ServiceWorkerState, ServiceWorkerActions }