'use client'

import React from 'react'
import { Card, Switch, Space, Typography, Divider, Button, message } from 'antd'
import { 
  BellOutlined, 
  MailOutlined, 
  SoundOutlined, 
  DesktopOutlined,
  DeleteOutlined 
} from '@ant-design/icons'

const { Title, Text } = Typography

interface NotificationSettingsCardProps {
  onSettingsChange?: (settings: any) => void
}

export default function NotificationSettingsCard({ 
  onSettingsChange 
}: NotificationSettingsCardProps) {
  const [settings, setSettings] = React.useState({
    email: true,
    push: true,
    sound: false,
    desktop: true,
  })

  const handleSettingChange = (key: string, value: boolean) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    onSettingsChange?.(newSettings)
    message.success('设置已保存')
  }

  const handleClearAllNotifications = () => {
    // 这里应该调用实际的清空API
    message.success('所有通知已清空')
  }

  return (
    <Card>
      <Title level={4}>
        <BellOutlined className="mr-2" />
        通知设置
      </Title>
      
      <Space direction="vertical" className="w-full" size="large">
        {/* 通知类型设置 */}
        <div>
          <Text strong className="block mb-3">通知方式</Text>
          <Space direction="vertical" className="w-full">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <MailOutlined className="text-blue-500" />
                <Text>邮件通知</Text>
              </div>
              <Switch
                checked={settings.email}
                onChange={(checked) => handleSettingChange('email', checked)}
              />
            </div>
            
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <BellOutlined className="text-green-500" />
                <Text>推送通知</Text>
              </div>
              <Switch
                checked={settings.push}
                onChange={(checked) => handleSettingChange('push', checked)}
              />
            </div>
            
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <SoundOutlined className="text-orange-500" />
                <Text>声音提醒</Text>
              </div>
              <Switch
                checked={settings.sound}
                onChange={(checked) => handleSettingChange('sound', checked)}
              />
            </div>
            
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <DesktopOutlined className="text-purple-500" />
                <Text>桌面通知</Text>
              </div>
              <Switch
                checked={settings.desktop}
                onChange={(checked) => handleSettingChange('desktop', checked)}
              />
            </div>
          </Space>
        </div>

        <Divider />

        {/* 通知管理 */}
        <div>
          <Text strong className="block mb-3">通知管理</Text>
          <Space direction="vertical" className="w-full">
            <div className="flex justify-between items-center">
              <div>
                <Text>清空所有通知</Text>
                <br />
                <Text type="secondary" className="text-xs">
                  删除所有已读和未读的通知记录
                </Text>
              </div>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={handleClearAllNotifications}
              >
                清空
              </Button>
            </div>
          </Space>
        </div>

        <Divider />

        {/* 通知提醒 */}
        <div>
          <Text strong className="block mb-3">提醒说明</Text>
          <Space direction="vertical" size="small">
            <Text type="secondary" className="text-xs">
              • 系统错误和安全相关通知将始终发送
            </Text>
            <Text type="secondary" className="text-xs">
              • 通知记录会自动清理30天前的已读消息
            </Text>
            <Text type="secondary" className="text-xs">
              • 关闭推送通知不会影响系统内通知显示
            </Text>
          </Space>
        </div>
      </Space>
    </Card>
  )
}