'use client'

import React from 'react'
import { Empty, Button } from 'antd'
import { BellOutlined, ReloadOutlined } from '@ant-design/icons'

interface NotificationEmptyProps {
  onRefresh?: () => void
  loading?: boolean
  showRefreshButton?: boolean
}

export default function NotificationEmpty({
  onRefresh,
  loading = false,
  showRefreshButton = true,
}: NotificationEmptyProps) {
  return (
    <div className="py-8 px-4 text-center">
      <Empty
        image={
          <div className="flex justify-center mb-4">
            <BellOutlined className="text-4xl text-gray-300" />
          </div>
        }
        imageStyle={{
          height: 'auto',
        }}
        description={
          <div className="text-gray-500">
            <div className="text-sm mb-2">暂无通知</div>
            <div className="text-xs text-gray-400">
              系统会在有重要事件时通知您
            </div>
          </div>
        }
      >
        {showRefreshButton && (
          <Button
            type="primary"
            ghost
            size="small"
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            loading={loading}
          >
            刷新
          </Button>
        )}
      </Empty>
    </div>
  )
}