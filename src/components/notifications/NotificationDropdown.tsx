'use client'

import React, { useState, useCallback } from 'react'
import { 
  Dropdown, 
  <PERSON><PERSON>, 
  Button, 
  Divider, 
  Space, 
  Typography, 
  Spin, 
  Pagination,
  message
} from 'antd'
import { 
  BellOutlined, 
  CheckOutlined, 
  DeleteOutlined, 
  SettingOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { useNotifications } from '@/hooks/useNotifications'
import NotificationItem from './NotificationItem'
import NotificationEmpty from './NotificationEmpty'

const { Text } = Typography

interface NotificationDropdownProps {
  className?: string
}

export default function NotificationDropdown({ className }: NotificationDropdownProps) {
  const [open, setOpen] = useState(false)
  const [selectedIds, setSelectedIds] = useState<string[]>([])

  const {
    notifications,
    total,
    unreadCount,
    loading,
    refresh,
    markAsRead,
    markAsUnread,
    deleteNotification,
    markAllAsRead,
    deleteMultiple,
    currentPage,
    setPage,
  } = useNotifications({
    page: 1,
    limit: 10,
    autoRefresh: open, // 只有下拉打开时才自动刷新
    refreshInterval: 30000,
  })

  const handleMarkAsRead = useCallback(async (id: string) => {
    await markAsRead(id)
  }, [markAsRead])

  const handleMarkAsUnread = useCallback(async (id: string) => {
    await markAsUnread(id)
  }, [markAsUnread])

  const handleDelete = useCallback(async (id: string) => {
    await deleteNotification(id)
  }, [deleteNotification])

  const handleActionClick = useCallback((url: string) => {
    // 关闭下拉框
    setOpen(false)
    
    // 根据URL类型处理跳转
    if (url.startsWith('/')) {
      // 内部路由
      window.location.href = url
    } else {
      // 外部链接
      window.open(url, '_blank')
    }
  }, [])

  const handleMarkAllAsRead = useCallback(async () => {
    if (unreadCount === 0) {
      message.info('没有未读通知')
      return
    }
    
    await markAllAsRead()
  }, [markAllAsRead, unreadCount])

  const handleDeleteAll = useCallback(async () => {
    if (notifications.length === 0) {
      message.info('没有通知可删除')
      return
    }

    const allIds = notifications.map(n => n.id)
    await deleteMultiple(allIds)
  }, [deleteMultiple, notifications])

  const dropdownContent = (
    <div className="w-80 bg-white shadow-lg rounded-lg border">
      {/* 头部 */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Text strong className="text-base">
              通知
            </Text>
            {unreadCount > 0 && (
              <Badge count={unreadCount} size="small" />
            )}
          </div>
          <Button
            type="text"
            size="small"
            icon={<ReloadOutlined />}
            onClick={refresh}
            loading={loading}
          />
        </div>

        {/* 操作按钮 */}
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={handleMarkAllAsRead}
            disabled={unreadCount === 0}
            className="text-xs px-2"
          >
            全部已读
          </Button>
          <Button
            type="link"
            size="small"
            onClick={handleDeleteAll}
            disabled={notifications.length === 0}
            className="text-xs px-2 text-red-500 hover:text-red-600"
          >
            清空全部
          </Button>
        </Space>
      </div>

      {/* 通知列表 */}
      <div className="max-h-96 overflow-y-auto">
        {loading && notifications.length === 0 ? (
          <div className="py-8 text-center">
            <Spin size="small" />
            <div className="mt-2 text-sm text-gray-500">加载中...</div>
          </div>
        ) : notifications.length > 0 ? (
          <>
            {notifications.map((notification) => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onMarkAsRead={handleMarkAsRead}
                onMarkAsUnread={handleMarkAsUnread}
                onDelete={handleDelete}
                onActionClick={handleActionClick}
              />
            ))}
            
            {/* 分页 */}
            {total > 10 && (
              <div className="p-3 border-t text-center">
                <Pagination
                  size="small"
                  current={currentPage}
                  total={total}
                  pageSize={10}
                  onChange={setPage}
                  showSizeChanger={false}
                  showQuickJumper={false}
                  simple
                />
              </div>
            )}
          </>
        ) : (
          <NotificationEmpty 
            onRefresh={refresh}
            loading={loading}
          />
        )}
      </div>

      {/* 底部设置链接 */}
      {notifications.length > 0 && (
        <>
          <Divider className="my-0" />
          <div className="p-3 text-center">
            <Button
              type="link"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => {
                setOpen(false)
                // 这里可以跳转到通知设置页面
                window.location.href = '/dashboard/settings?tab=notifications'
              }}
              className="text-xs"
            >
              通知设置
            </Button>
          </div>
        </>
      )}
    </div>
  )

  return (
    <Dropdown
      trigger={['click']}
      open={open}
      onOpenChange={setOpen}
      dropdownRender={() => dropdownContent}
      placement="bottomRight"
      arrow={false}
    >
      <Badge count={unreadCount} size="small" offset={[-2, 2]}>
        <div 
          className={`
            flex items-center justify-center w-8 h-8 rounded-full 
            hover:bg-gray-100 cursor-pointer transition-colors touch-target
            ${className}
          `}
        >
          <BellOutlined className="text-gray-600 text-lg" />
        </div>
      </Badge>
    </Dropdown>
  )
}