'use client'

import React from 'react'
import { Button, Typography, Space, Tag, Tooltip } from 'antd'
import { 
  CheckOutlined, 
  DeleteOutlined, 
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  LinkOutlined
} from '@ant-design/icons'
import type { Notification } from '@/types'

const { Text, Paragraph } = Typography

interface NotificationItemProps {
  notification: Notification
  onMarkAsRead?: (id: string) => void
  onMarkAsUnread?: (id: string) => void
  onDelete?: (id: string) => void
  onActionClick?: (url: string) => void
}

// 通知类型图标映射
const typeIcons = {
  INFO: <InfoCircleOutlined className="text-blue-500" />,
  SUCCESS: <CheckCircleOutlined className="text-green-500" />,
  WARNING: <ExclamationCircleOutlined className="text-yellow-500" />,
  ERROR: <CloseCircleOutlined className="text-red-500" />,
}

// 通知类型标签颜色映射
const typeColors = {
  INFO: 'blue',
  SUCCESS: 'green',
  WARNING: 'orange',
  ERROR: 'red',
} as const

export default function NotificationItem({
  notification,
  onMarkAsRead,
  onMarkAsUnread,
  onDelete,
  onActionClick,
}: NotificationItemProps) {
  const { id, title, message, type, read, actionUrl, createdAt } = notification

  const handleReadToggle = () => {
    if (read) {
      onMarkAsUnread?.(id)
    } else {
      onMarkAsRead?.(id)
    }
  }

  const handleDelete = () => {
    onDelete?.(id)
  }

  const handleActionClick = () => {
    if (actionUrl && onActionClick) {
      onActionClick(actionUrl)
      // 点击操作链接时自动标记为已读
      if (!read && onMarkAsRead) {
        onMarkAsRead(id)
      }
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return '刚刚'
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}分钟前`
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}小时前`
    } else if (diffInSeconds < 604800) {
      return `${Math.floor(diffInSeconds / 86400)}天前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  }

  return (
    <div
      className={`
        p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors
        ${!read ? 'bg-blue-50/30' : ''}
      `}
    >
      <div className="flex items-start gap-3">
        {/* 通知类型图标 */}
        <div className="flex-shrink-0 mt-1">
          {typeIcons[type]}
        </div>

        {/* 通知内容 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2 mb-2">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <Text
                  strong={!read}
                  className={`text-sm ${!read ? 'text-gray-900' : 'text-gray-600'}`}
                >
                  {title}
                </Text>
                <Tag color={typeColors[type]}>
                  {type}
                </Tag>
                {!read && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                )}
              </div>
              <Paragraph
                className={`text-xs mb-2 ${!read ? 'text-gray-700' : 'text-gray-500'}`}
                ellipsis={{ rows: 2, expandable: false }}
              >
                {message}
              </Paragraph>
            </div>
          </div>

          {/* 操作按钮和时间 */}
          <div className="flex items-center justify-between">
            <Text type="secondary" className="text-xs">
              {formatTime(createdAt)}
            </Text>

            <Space size="small">
              {/* 操作链接按钮 */}
              {actionUrl && (
                <Tooltip title="查看详情">
                  <Button
                    type="link"
                    size="small"
                    icon={<LinkOutlined />}
                    onClick={handleActionClick}
                    className="text-blue-500 hover:text-blue-600"
                  />
                </Tooltip>
              )}

              {/* 标记已读/未读按钮 */}
              <Tooltip title={read ? '标记未读' : '标记已读'}>
                <Button
                  type="link"
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={handleReadToggle}
                  className={`
                    ${read 
                      ? 'text-gray-400 hover:text-blue-500' 
                      : 'text-blue-500 hover:text-blue-600'
                    }
                  `}
                />
              </Tooltip>

              {/* 删除按钮 */}
              <Tooltip title="删除">
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={handleDelete}
                  className="text-gray-400 hover:text-red-500"
                />
              </Tooltip>
            </Space>
          </div>
        </div>
      </div>
    </div>
  )
}