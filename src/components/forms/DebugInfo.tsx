'use client'

import { Card, Typography, Tag } from 'antd'

const { Text, Title } = Typography

interface DebugInfoProps {
  title: string
  data: any
}

export function DebugInfo({ title, data }: DebugInfoProps) {
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <Card 
      size="small" 
      style={{ 
        background: '#f0f0f0', 
        border: '1px solid #d9d9d9',
        marginBottom: '16px'
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
        <Tag color="orange">DEBUG</Tag>
        <Title level={5} style={{ margin: 0 }}>
          {title}
        </Title>
      </div>
      <pre style={{ 
        background: '#ffffff', 
        padding: '8px', 
        borderRadius: '4px',
        fontSize: '12px',
        margin: 0,
        overflow: 'auto'
      }}>
        {JSON.stringify(data, null, 2)}
      </pre>
    </Card>
  )
}