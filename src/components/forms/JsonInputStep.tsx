'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  Typography,
  Button,
  Alert,
  Input,
  Space,
  message,
  Collapse,
} from 'antd'
import {
  FileTextOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ArrowRightOutlined,
  CopyOutlined,
} from '@ant-design/icons'
import { DebugInfo } from './DebugInfo'

const { Title, Text } = Typography
const { TextArea } = Input
const { Panel } = Collapse

interface JsonInputStepProps {
  onSubmit: (jsonData: any) => void
  initialValue?: any
  dataFormat?: 'jinshuju_standard' | 'jinshuju_automation' | 'custom'
}

// 示例JSON数据
const exampleJsonTemplates = {
  jinshuju_standard: {
    form: 'ZFs2eo',
    form_name: '预约免费肺功能检查、免费办理慢性病医保',
    entry: {
      serial_number: 123,
      field_1: '张三',
      field_2: '选项1',
      field_6: 123,
      field_7: '选项1',
      field_3: '13812345678',
      field_4: ['选项1', '选项2', '选项3'],
      x_field_1: '这是一行文字',
      color_mark: '深绿色',
      creator_name: '小王',
      created_at: '2025-06-29T05:16:12.175Z',
      updated_at: '2025-06-29T05:16:12.175Z',
      info_filling_duration: 123,
      info_platform: 'Macintosh',
      info_os: 'OS X 10.13.6',
      info_browser: 'Chrome 68.0.3440.106',
      info_region: {
        province: '陕西省',
        city: '西安市',
        district: '雁塔区',
        street: '高新路',
      },
      info_remote_ip: '127.0.0.1',
    },
  },
  jinshuju_automation: {
    form: 'usk6CY',
    form_name: '120餐券领取',
    entry: {
      token: 'TzbIm8FV',
      field_3: '',
      field_4: '2025-07-10',
      field_5: '高安市人民医院',
      field_6: '李卫平',
      field_7: '13707003778',
      field_8: '邹春香',
      field_10: '',
      field_11: '爱国路院区食堂（住院部二部后面）：徐中 18827949388',
      field_12: '120司机',
      gen_code: '031576671523',
      referral_link: 'http://jinshuju.net/f/usk6CY?referred_from=TzbIm8FV',
      referral_users_count: null,
      x_field_weixin_openid: 'orNMswF4m_KgjYAyPLr4E5CbsW2Q',
      x_field_weixin_unionid: 'oRZSYuIDHs9G1s6kG5ca_TUErN8k',
      x_field_weixin_nickname: '李卫平',
      referred_from_associated_serial_number: null,
    },
  },
  custom: {
    form: 'custom_form',
    form_name: '自定义格式示例',
    entry: {
      id: 'unique_id_123',
      field_1: '示例数据',
      field_2: '自定义字段',
      custom_field: '特殊字段',
      metadata: {
        source: 'custom_system',
        timestamp: '2025-07-10T10:00:00Z',
      },
    },
  },
}

export function JsonInputStep({ onSubmit, initialValue, dataFormat = 'jinshuju_standard' }: JsonInputStepProps) {
  const [jsonText, setJsonText] = useState(
    initialValue ? JSON.stringify(initialValue, null, 2) : ''
  )
  const [parsedJson, setParsedJson] = useState<any>(initialValue || null)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  // 仅在开发模式下输出调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('JsonInputStep 渲染, dataFormat:', dataFormat)
  }

  // 当dataFormat变化时，重新验证当前的JSON
  useEffect(() => {
    if (parsedJson) {
      const validationError = validateJsonStructure(parsedJson)
      if (validationError) {
        setError(validationError)
      } else {
        setError('')
      }
    }
  }, [dataFormat, parsedJson])

  const handleJsonChange = (value: string) => {
    setJsonText(value)
    setError('')

    if (!value.trim()) {
      setParsedJson(null)
      return
    }

    try {
      const parsed = JSON.parse(value)
      setParsedJson(parsed)
    } catch (err) {
      setParsedJson(null)
      setError('JSON格式不正确，请检查语法')
    }
  }

  const validateJsonStructure = (json: any): string | null => {
    if (process.env.NODE_ENV === 'development') {
      console.log('验证JSON结构, dataFormat:', dataFormat, 'json:', json)
    }
    
    if (!json || typeof json !== 'object') {
      return 'JSON必须是一个对象'
    }

    if (!json.form || typeof json.form !== 'string') {
      return '缺少必要字段: form (表单ID)'
    }

    if (!json.entry || typeof json.entry !== 'object') {
      return '缺少必要字段: entry (表单数据)'
    }

    // 根据数据格式进行不同的验证
    if (process.env.NODE_ENV === 'development') {
      console.log('使用数据格式验证:', dataFormat)
    }
    switch (dataFormat) {
      case 'jinshuju_standard':
        if (!json.form_name || typeof json.form_name !== 'string') {
          return '标准格式缺少必要字段: form_name (表单名称)'
        }
        if (!json.entry.hasOwnProperty('serial_number')) {
          return '标准格式必须包含 entry.serial_number 字段'
        }
        if (typeof json.entry.serial_number !== 'number') {
          return 'serial_number 必须是数字类型，当前类型: ' + typeof json.entry.serial_number
        }
        if (json.entry.serial_number <= 0) {
          return 'serial_number 必须是正整数'
        }
        break
      
      case 'jinshuju_automation':
        if (!json.form_name || typeof json.form_name !== 'string') {
          return '自动化格式缺少必要字段: form_name (表单名称)'
        }
        if (!json.entry.token || typeof json.entry.token !== 'string') {
          return '自动化格式必须包含 entry.token 字段作为唯一标识'
        }
        if (json.entry.token.length < 4) {
          return 'token 字段长度不能少于4个字符'
        }
        // 验证token格式（通常是字母数字组合）
        if (!/^[a-zA-Z0-9]+$/.test(json.entry.token)) {
          return 'token 字段只能包含字母和数字'
        }
        break
      
      case 'custom':
        // 自定义格式的验证比较宽松，只要有基本结构即可
        if (Object.keys(json.entry).length === 0) {
          return 'entry对象不能为空'
        }
        break
      
      default:
        return '不支持的数据格式'
    }

    return null
  }

  const handleSubmit = () => {
    if (!parsedJson) {
      message.error('请先输入有效的JSON数据')
      return
    }

    const validationError = validateJsonStructure(parsedJson)
    if (validationError) {
      setError(validationError)
      return
    }

    setLoading(true)
    setTimeout(() => {
      onSubmit(parsedJson)
      setLoading(false)
    }, 500)
  }

  const handleUseExample = () => {
    const exampleJson = exampleJsonTemplates[dataFormat]
    const exampleText = JSON.stringify(exampleJson, null, 2)
    setJsonText(exampleText)
    handleJsonChange(exampleText)
  }

  const copyExample = () => {
    const exampleJson = exampleJsonTemplates[dataFormat]
    navigator.clipboard.writeText(JSON.stringify(exampleJson, null, 2))
    message.success(`${getFormatName(dataFormat)}示例JSON已复制到剪贴板`)
  }

  const getFormatName = (format: string) => {
    switch (format) {
      case 'jinshuju_standard': return '金数据标准格式'
      case 'jinshuju_automation': return '金数据自动化格式'
      case 'custom': return '自定义格式'
      default: return '示例'
    }
  }

  // 根据数据格式获取系统字段列表
  const getSystemFields = (format: string): string[] => {
    const baseSystemFields = ['created_at', 'updated_at', 'creator_name', 'color_mark']
    const infoFields = ['info_']  // 以info_开头的字段
    
    switch (format) {
      case 'jinshuju_standard':
        return [...baseSystemFields, 'serial_number', ...infoFields]
      case 'jinshuju_automation':
        return [...baseSystemFields, 'token', 'x_field_weixin_openid', 'x_field_weixin_unionid', 'x_field_weixin_nickname', 'gen_code', 'referral_link', 'referral_users_count', 'referred_from_associated_serial_number', ...infoFields]
      case 'custom':
        return [...baseSystemFields, ...infoFields]
      default:
        return [...baseSystemFields, 'serial_number', ...infoFields]
    }
  }

  const renderJsonPreview = () => {
    if (!parsedJson) return null

    const { entry } = parsedJson
    const systemFields = getSystemFields(dataFormat)
    
    // 过滤出非系统字段
    const fields = Object.keys(entry).filter(key => {
      // 跳过系统字段
      const isSystemField = systemFields.some(sysField => {
        if (sysField === 'info_') {
          return key.startsWith('info_')
        }
        return key === sysField
      })
      return !isSystemField
    })

    // 获取所有字段并分类
    const allFields = Object.keys(entry)
    const systemFieldsList = allFields.filter(key => {
      const isSystemField = systemFields.some(sysField => {
        if (sysField === 'info_') {
          return key.startsWith('info_')
        }
        return key === sysField
      })
      return isSystemField
    })

    return (
      <Card size="small" title="解析结果预览" className="mt-4">
        <div className="space-y-3">
          <div>
            <Text strong>表单ID: </Text>
            <Text code>{parsedJson.form}</Text>
          </div>
          <div>
            <Text strong>表单名称: </Text>
            <Text>{parsedJson.form_name}</Text>
          </div>
          <div>
            <Text strong>总字段数量: </Text>
            <Text type="success">{allFields.length} 个</Text>
            <Text type="secondary" className="ml-2">
              (用户字段: {fields.length} 个，系统字段: {systemFieldsList.length} 个)
            </Text>
          </div>
          
          <div>
            <Text strong>用户数据字段: </Text>
            <div className="mt-1">
              {fields.length > 0 ? fields.map(field => (
                <span
                  key={field}
                  className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2 mb-1"
                >
                  {field}: {typeof entry[field]}
                </span>
              )) : <Text type="secondary">无用户数据字段</Text>}
            </div>
          </div>

          <div>
            <Text strong>系统字段: </Text>
            <div className="mt-1">
              {systemFieldsList.length > 0 ? systemFieldsList.map(field => (
                <span
                  key={field}
                  className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded mr-2 mb-1"
                >
                  {field}: {typeof entry[field]}
                </span>
              )) : <Text type="secondary">无系统字段</Text>}
            </div>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <DebugInfo 
        title="JsonInputStep Props" 
        data={{ 
          dataFormat, 
          hasInitialValue: !!initialValue,
          currentError: error,
          hasParsedJson: !!parsedJson
        }} 
      />
      
      <Card>
        <Title level={4} className="mb-4">
          <FileTextOutlined className="mr-2" />
          输入金数据JSON样例
        </Title>

        <Alert
          message="说明"
          description={`请粘贴一个完整的${getFormatName(dataFormat)}Webhook推送JSON数据样例。系统将根据这个样例自动识别字段结构并生成字段映射配置。不同数据格式的验证规则有所不同，请确保数据格式与所选类型匹配。`}
          type="info"
          showIcon
          className="mb-4"
        />

        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <Text strong>JSON数据:</Text>
              <Space>
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={copyExample}
                >
                  复制示例
                </Button>
                <Button size="small" type="link" onClick={handleUseExample}>
                  使用示例数据
                </Button>
              </Space>
            </div>

            <TextArea
              value={jsonText}
              onChange={e => handleJsonChange(e.target.value)}
              placeholder={`请粘贴${getFormatName(dataFormat)}Webhook JSON数据...`}
              rows={15}
              className="font-mono text-sm"
            />
          </div>

          {error && (
            <Alert
              message="JSON解析错误"
              description={error}
              type="error"
              showIcon
              icon={<ExclamationCircleOutlined />}
            />
          )}

          {parsedJson && !error && (
            <Alert
              message="JSON解析成功"
              description="JSON格式正确，已成功解析数据结构"
              type="success"
              showIcon
              icon={<CheckCircleOutlined />}
            />
          )}

          {renderJsonPreview()}

          <div className="flex justify-end">
            <Button
              type="primary"
              size="large"
              onClick={handleSubmit}
              disabled={!parsedJson || !!error}
              loading={loading}
              icon={<ArrowRightOutlined />}
            >
              解析完成，下一步
            </Button>
          </div>
        </div>
      </Card>

      {/* 示例JSON展示 */}
      <Collapse ghost>
        <Panel header="查看JSON数据格式说明" key="1">
          <Card size="small">
            <Text strong className="block mb-2">
              标准金数据Webhook JSON结构:
            </Text>
            <pre className="bg-gray-50 p-3 rounded text-xs overflow-x-auto">
              {`{
  "form": "表单ID",
  "form_name": "表单名称", 
  "entry": {
    "serial_number": 序列号,
    "field_1": "字段1值",
    "field_2": "字段2值",
    "field_3": "字段3值",
    "x_field_1": "扩展字段值",
    "created_at": "创建时间",
    "updated_at": "更新时间",
    "info_region": {
      "province": "省份",
      "city": "城市"
    }
  }
}`}
            </pre>
            <div className="mt-3 space-y-1 text-sm text-gray-600">
              <div>
                • <Text code>form</Text>: 金数据表单的唯一标识
              </div>
              <div>
                • <Text code>form_name</Text>: 表单显示名称
              </div>
              <div>
                • <Text code>entry</Text>: 包含所有表单字段数据
              </div>
              <div>
                • <Text code>field_*</Text>: 普通表单字段
              </div>
              <div>
                • <Text code>x_field_*</Text>: 扩展字段
              </div>
              <div>
                • <Text code>info_*</Text>: 系统自动生成的元数据
              </div>
            </div>
          </Card>
        </Panel>
      </Collapse>
    </div>
  )
}
