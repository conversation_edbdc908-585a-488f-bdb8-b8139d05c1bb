'use client'

import {
  Layout,
  Breadcrumb,
  Space,
  Avatar,
  Dropdown,
  Typography,
  Badge,
} from 'antd'
import {
  UserOutlined,
  LogoutOutlined,
  KeyOutlined,
  BellOutlined,
  SettingOutlined,
  MenuOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import NotificationDropdown from '@/components/notifications/NotificationDropdown'
import { useResponsive } from '@/hooks/useResponsive'

const { Header: AntHeader } = Layout
const { Text } = Typography

interface HeaderProps {
  user?: {
    id: number
    username: string
    nickname?: string
    avatarUrl?: string
  }
  onLogout?: () => void
  onMenuToggle?: () => void
}

// 路径映射配置
const pathNameMap: Record<string, string> = {
  '/dashboard': '仪表板',
  '/forms': '表单管理',
  '/forms/config': '表单配置',
  '/data': '数据管理',
  '/data/view': '数据查看',
  '/data/export': '数据导出',
  '/data/batch': '批量操作',
  '/settings': '系统设置',
  '/settings/profile': '个人设置',
  '/settings/password': '修改密码',
  '/settings/users': '用户管理',
  '/settings/logs': '系统日志',
  '/help': '帮助文档',
}

export function Header({ user, onLogout, onMenuToggle }: HeaderProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { isMobile, isTablet } = useResponsive()

  // 检查是否可以返回上一页
  const canGoBack = () => {
    return pathname !== '/dashboard' && typeof window !== 'undefined' && window.history.length > 1
  }

  // 处理返回按钮点击
  const handleGoBack = () => {
    if (canGoBack()) {
      router.back()
    } else {
      router.push('/dashboard')
    }
  }

  // 生成面包屑
  const generateBreadcrumb = (path: string) => {
    const pathSegments = path.split('/').filter(Boolean)
    const breadcrumbItems = [
      {
        title: (
          <Link
            href="/dashboard"
            className="text-gray-600 hover:text-primary-500 transition-colors"
          >
            首页
          </Link>
        ),
        key: 'dashboard',
      },
    ]

    let currentPath = ''
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      if (pathNameMap[currentPath]) {
        const isLast = index === pathSegments.length - 1
        breadcrumbItems.push({
          title: isLast ? (
            <Text className="text-gray-900 font-medium">
              {pathNameMap[currentPath]}
            </Text>
          ) : (
            <Link
              href={currentPath}
              className="text-gray-600 hover:text-primary-500 transition-colors"
            >
              {pathNameMap[currentPath]}
            </Link>
          ),
          key: currentPath,
        })
      }
    })

    return breadcrumbItems
  }

  // 生成移动端简化标题
  const getMobilePageTitle = (path: string) => {
    return pathNameMap[path] || '肺功能数据管理平台'
  }

  // 获取面包屑层级深度
  const getBreadcrumbDepth = (path: string) => {
    return generateBreadcrumb(path).length
  }

  // 用户菜单项
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: (
        <Link href="/settings/profile" className="flex items-center">
          个人设置
        </Link>
      ),
    },
    {
      key: 'password',
      icon: <KeyOutlined />,
      label: (
        <Link href="/settings/password" className="flex items-center">
          修改密码
        </Link>
      ),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: (
        <Link href="/settings" className="flex items-center">
          系统设置
        </Link>
      ),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: onLogout,
      className: 'text-red-500 hover:text-red-600',
    },
  ]

  return (
    <AntHeader className="flex items-center justify-between px-4 sm:px-6 bg-white shadow-ant border-b border-gray-200">
      {/* 左侧：移动端导航按钮 + 面包屑 */}
      <div className="flex items-center flex-1 min-w-0">
        {/* 移动端导航按钮 */}
        <div className="flex items-center lg:hidden mr-3">
          {/* 返回按钮 - 仅在非首页且移动端显示 */}
          {isMobile && canGoBack() && (
            <button
              onClick={handleGoBack}
              className="mobile-nav-button mr-2 touch-feedback"
              aria-label="返回上一页"
            >
              <ArrowLeftOutlined className="text-gray-600 text-lg" />
            </button>
          )}
          
          {/* 汉堡菜单按钮 */}
          <button
            onClick={onMenuToggle}
            className="mobile-nav-button touch-feedback"
            aria-label={onMenuToggle ? "打开导航菜单" : "关闭导航菜单"}
          >
            <MenuOutlined className="text-gray-600 text-lg" />
          </button>
        </div>

        {/* 面包屑导航 */}
        <div className="flex-1 min-w-0">
          {/* 桌面端完整面包屑 */}
          <div className="hidden lg:block">
            <Breadcrumb 
              items={generateBreadcrumb(pathname)}
              className="desktop-breadcrumb"
            />
          </div>
          
          {/* 平板端简化面包屑 */}
          <div className="hidden md:block lg:hidden">
            <Breadcrumb 
              items={generateBreadcrumb(pathname).slice(-2)} // 只显示最后两级
              className="tablet-breadcrumb mobile-breadcrumb"
            />
          </div>
          
          {/* 移动端页面标题 */}
          <div className="block md:hidden">
            <div className="flex items-center">
              <Text className="mobile-page-title">
                {getMobilePageTitle(pathname)}
              </Text>
              {/* 显示层级指示器 */}
              {pathname !== '/dashboard' && getBreadcrumbDepth(pathname) > 2 && (
                <span className="mobile-breadcrumb-indicator" />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 右侧用户信息和操作 */}
      <div className="flex items-center">
        <Space size={isMobile ? 'small' : 'middle'}>
          {/* 通知铃铛 - 在移动端也显示 */}
          <div className={isMobile ? 'block' : 'hidden sm:block'}>
            <NotificationDropdown className={isMobile ? 'mobile-notification' : ''} />
          </div>

          {/* 用户信息下拉菜单 */}
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
            arrow={{ pointAtCenter: true }}
            overlayClassName="mobile-user-dropdown"
          >
            <div className={`flex items-center space-x-2 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors touch-target ${
              isMobile ? 'min-w-[44px] justify-center' : ''
            }`}>
              <Avatar
                size={isMobile ? 'default' : 'small'}
                src={user?.avatarUrl}
                icon={!user?.avatarUrl && <UserOutlined />}
                className="border border-gray-200"
              />
              {/* 桌面端和平板端显示用户信息 */}
              <div className="hidden sm:flex flex-col items-start">
                <Text className="text-sm font-medium text-gray-900 leading-none">
                  {user?.nickname || user?.username || '用户'}
                </Text>
                <Text className="text-xs text-gray-500 leading-none mt-0.5">
                  {user?.username}
                </Text>
              </div>
            </div>
          </Dropdown>
        </Space>
      </div>
    </AntHeader>
  )
}
