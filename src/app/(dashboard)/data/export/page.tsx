'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Select,
  DatePicker,
  Button,
  Space,
  message,
  Table,
  Checkbox,
} from 'antd'
import {
  DownloadOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
} from '@ant-design/icons'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'

const { RangePicker } = DatePicker
const { Option } = Select

interface FormConfig {
  id: string
  formId: string
  formName: string
  isActive: boolean
  fieldCount: number
  webhookUrl: string
  createdAt: string
  updatedAt: string
}

interface ExportRecord {
  id: string
  formName: string
  exportType: string
  dateRange: string
  recordCount: number
  fileSize: string
  createdAt: string
  downloadUrl: string
  status: 'processing' | 'completed' | 'failed'
}

export default function DataExportPage() {
  // 确保状态始终初始化为空数组
  const [forms, setFormsState] = useState<FormConfig[]>(() => [])
  const [exportHistory, setExportHistory] = useState<ExportRecord[]>(() => [])
  const [loading, setLoading] = useState(false)
  const [historyLoading, setHistoryLoading] = useState(true)
  const [formsLoading, setFormsLoading] = useState(true)
  const [form] = Form.useForm()

  // 增强的安全 setForms 函数，确保只设置数组
  const setForms = React.useCallback((newForms: FormConfig[] | any) => {
    if (newForms === null || newForms === undefined) {
      setFormsState([])
      return
    }

    if (Array.isArray(newForms)) {
      setFormsState(newForms)
    } else {
      console.error(
        'setForms received non-array value:',
        newForms,
        'setting empty array instead'
      )
      setFormsState([])
    }
  }, [])

  // 增强的安全检查，确保 forms 始终是数组
  const safeForms = React.useMemo(() => {
    return Array.isArray(forms) ? forms : []
  }, [forms])

  useEffect(() => {
    fetchForms()
    fetchExportHistory()
  }, [])

  const fetchForms = async () => {
    try {
      setFormsLoading(true)
      // 在开始请求前确保状态为空数组
      setForms([])

      const response = await fetch('/api/forms', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // 添加超时设置
        signal: AbortSignal.timeout(10000), // 10秒超时
      })

      if (response.ok) {
        const result = await response.json()

        // 更严格的数据验证
        if (
          result &&
          typeof result === 'object' &&
          result.success === true &&
          result.data &&
          typeof result.data === 'object' &&
          Array.isArray(result.data.forms)
        ) {
          setForms(result.data.forms)
        } else {
          console.error('API返回数据格式错误:', result)
          console.error(
            '预期的格式: { success: true, data: { forms: FormConfig[] } }'
          )
          setForms([])
          message.error('获取表单列表失败：数据格式错误')
        }
      } else if (response.status === 401) {
        setForms([])
        message.error('请先登录后再访问此页面')
      } else {
        let errorText = ''
        try {
          const errorData = await response.json()
          errorText =
            errorData.error || errorData.message || response.statusText
        } catch {
          errorText = response.statusText
        }
        console.error('API请求失败:', response.status, errorText)
        setForms([])
        message.error(`获取表单列表失败: ${errorText}`)
      }
    } catch (error) {
      console.error('获取表单列表失败:', error)
      setForms([])

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          message.error('请求超时，请检查网络连接')
        } else if (error.message.includes('Failed to fetch')) {
          message.error('网络连接失败，请检查网络连接')
        } else {
          message.error(`网络错误: ${error.message}`)
        }
      } else {
        message.error('未知错误，请稍后重试')
      }
    } finally {
      setFormsLoading(false)
    }
  }

  const fetchExportHistory = async () => {
    try {
      setHistoryLoading(true)
      // 模拟数据
      setExportHistory([
        {
          id: '1',
          formName: '肺功能检查表',
          exportType: 'excel',
          dateRange: '2024-01-01 ~ 2024-01-31',
          recordCount: 150,
          fileSize: '2.3MB',
          createdAt: '2024-01-31 14:30:00',
          downloadUrl: '/api/download/export-1.xlsx',
          status: 'completed',
        },
      ])
    } catch (error) {
      console.error('获取导出历史失败:', error)
      message.error('获取导出历史失败')
    } finally {
      setHistoryLoading(false)
    }
  }

  const handleExport = async (values: any) => {
    try {
      setLoading(true)
      const { formId, dateRange, exportType, includeFields } = values

      const params = new URLSearchParams({
        format: exportType,
        includeFields: includeFields?.join(',') || '',
      })

      if (dateRange && dateRange.length === 2) {
        params.append('startDate', dateRange[0].format('YYYY-MM-DD'))
        params.append('endDate', dateRange[1].format('YYYY-MM-DD'))
      }

      const response = await fetch(`/api/data/${formId}/export?${params}`)

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `export-${formId}-${dayjs().format('YYYY-MM-DD-HHmm')}.${exportType}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)

        message.success('导出成功')
        fetchExportHistory()
      } else {
        // 尝试解析错误信息
        let errorMessage = '导出失败'
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorMessage
        } catch {
          errorMessage = `导出失败 (HTTP ${response.status})`
        }
        message.error(errorMessage)
      }
    } catch (error) {
      console.error('导出失败:', error)
      if (error instanceof Error) {
        if (error.message.includes('Failed to fetch')) {
          message.error('网络连接失败，请检查网络连接')
        } else {
          message.error(`导出失败: ${error.message}`)
        }
      } else {
        message.error('导出失败')
      }
    } finally {
      setLoading(false)
    }
  }

  const exportColumns: ColumnsType<ExportRecord> = [
    {
      title: '表单名称',
      dataIndex: 'formName',
      key: 'formName',
    },
    {
      title: '导出类型',
      dataIndex: 'exportType',
      key: 'exportType',
      render: type => (
        <Space>
          {type === 'excel' ? <FileExcelOutlined /> : <FilePdfOutlined />}
          {type.toUpperCase()}
        </Space>
      ),
    },
    {
      title: '时间范围',
      dataIndex: 'dateRange',
      key: 'dateRange',
    },
    {
      title: '记录数',
      dataIndex: 'recordCount',
      key: 'recordCount',
      render: count => count.toLocaleString(),
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
    },
    {
      title: '导出时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: status => {
        const statusMap = {
          processing: { color: 'blue', text: '处理中' },
          completed: { color: 'green', text: '已完成' },
          failed: { color: 'red', text: '失败' },
        }
        const config = statusMap[status as keyof typeof statusMap]
        return <span style={{ color: config.color }}>{config.text}</span>
      },
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) =>
        record.status === 'completed' && (
          <Button
            type="link"
            icon={<DownloadOutlined />}
            onClick={() => window.open(record.downloadUrl)}
          >
            下载
          </Button>
        ),
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">数据导出</h1>
        <p className="text-gray-600 mt-1">导出表单数据到 Excel 或 CSV 格式</p>
      </div>

      <Card title="导出数据" className="mb-6">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleExport}
          initialValues={{
            exportType: 'excel',
            dateRange: [dayjs().startOf('month'), dayjs().endOf('month')],
          }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Form.Item
              name="formId"
              label="选择表单"
              rules={[{ required: true, message: '请选择要导出的表单' }]}
            >
              <Select
                placeholder={
                  formsLoading
                    ? '加载中...'
                    : safeForms.length === 0
                      ? '暂无可用表单'
                      : '请选择表单'
                }
                loading={formsLoading}
                disabled={formsLoading || safeForms.length === 0}
              >
                {/* 添加防御性渲染，确保 safeForms 是数组且每个项目都有必要字段 */}
                {Array.isArray(safeForms) && safeForms.length > 0
                  ? safeForms
                      .filter(
                        form =>
                          form &&
                          typeof form === 'object' &&
                          form.formId &&
                          form.formName
                      )
                      .map((form, index) => (
                        <Option
                          key={form.id || form.formId || index}
                          value={form.formId}
                        >
                          {form.formName || `表单 ${index + 1}`}
                        </Option>
                      ))
                  : !formsLoading && (
                      <Option value="" disabled>
                        {safeForms.length === 0
                          ? '暂无可用表单'
                          : '数据加载失败'}
                      </Option>
                    )}
              </Select>
            </Form.Item>

            <Form.Item
              name="exportType"
              label="导出格式"
              rules={[{ required: true, message: '请选择导出格式' }]}
            >
              <Select>
                <Option value="excel">Excel (.xlsx)</Option>
                <Option value="csv">CSV (.csv)</Option>
              </Select>
            </Form.Item>

            <Form.Item name="dateRange" label="时间范围">
              <RangePicker
                format="YYYY-MM-DD"
                placeholder={['开始日期', '结束日期']}
                className="w-full"
              />
            </Form.Item>

            <Form.Item name="includeFields" label="包含字段">
              <Checkbox.Group>
                <div className="space-y-2">
                  <Checkbox value="all">所有字段</Checkbox>
                  <Checkbox value="basic">基础信息</Checkbox>
                  <Checkbox value="timestamp">时间戳</Checkbox>
                </div>
              </Checkbox.Group>
            </Form.Item>
          </div>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<DownloadOutlined />}
            >
              开始导出
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card title="导出历史" className="mb-6">
        <Table
          columns={exportColumns}
          dataSource={exportHistory}
          loading={historyLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: total => `共 ${total} 条导出记录`,
          }}
        />
      </Card>
    </div>
  )
}
