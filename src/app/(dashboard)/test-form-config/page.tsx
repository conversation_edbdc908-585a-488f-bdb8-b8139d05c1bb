'use client'

import { useState } from 'react'
import { Card, Typography, Button, Select, Alert } from 'antd'
import { JsonInputStep } from '@/components/forms/JsonInputStep'

const { Title } = Typography

export default function TestFormConfigPage() {
  const [dataFormat, setDataFormat] = useState<'jinshuju_standard' | 'jinshuju_automation' | 'custom'>('jinshuju_standard')
  const [result, setResult] = useState<any>(null)

  const handleJsonSubmit = (jsonData: any) => {
    setResult(jsonData)
    console.log('JSON提交结果:', jsonData)
  }

  return (
    <div className="space-y-6">
      <Card>
        <Title level={2}>表单配置测试页面</Title>
        <p>这个页面用于测试不同数据格式的JSON输入组件</p>
      </Card>

      <Card>
        <Title level={4}>选择数据格式</Title>
        <Select 
          value={dataFormat} 
          onChange={setDataFormat}
          style={{ width: 300 }}
        >
          <Select.Option value="jinshuju_standard">金数据标准格式</Select.Option>
          <Select.Option value="jinshuju_automation">金数据自动化格式</Select.Option>
          <Select.Option value="custom">自定义格式</Select.Option>
        </Select>
        <div style={{ marginTop: 10 }}>
          当前选择: <strong>{dataFormat}</strong>
        </div>
      </Card>

      <JsonInputStep
        onSubmit={handleJsonSubmit}
        dataFormat={dataFormat}
      />

      {result && (
        <Card>
          <Title level={4}>提交结果</Title>
          <Alert
            message="JSON验证成功"
            description="数据已通过验证，可以进入下一步"
            type="success"
            showIcon
          />
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            marginTop: '10px'
          }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </Card>
      )}
    </div>
  )
}