'use client'

import { useState, useEffect } from 'react'
import { Card, Table, Button, Space, Tag, message, Modal, Tooltip, Switch } from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  PoweroffOutlined,
} from '@ant-design/icons'
import { useRouter } from 'next/navigation'
import type { ColumnsType } from 'antd/es/table'
import { copyToClipboard } from '@/lib/utils'

interface FormConfig {
  id: string
  formId: string
  formName: string
  isActive: boolean
  fieldCount: number
  webhookUrl: string
  createdAt: string
  updatedAt: string
}

export default function FormsListPage() {
  const [loading, setLoading] = useState(true)
  const [forms, setForms] = useState<FormConfig[]>([])
  const [toggleLoading, setToggleLoading] = useState<Set<string>>(new Set())
  const router = useRouter()

  useEffect(() => {
    fetchForms()
  }, [])

  const fetchForms = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/forms')
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data && Array.isArray(result.data.forms)) {
          setForms(result.data.forms)
        } else {
          console.error('API返回数据格式错误:', result)
          setForms([]) // 确保设置为空数组而不是undefined
          message.error('获取表单列表失败：数据格式错误')
        }
      } else {
        console.error('API请求失败:', response.status, response.statusText)
        setForms([]) // 确保设置为空数组而不是undefined
        message.error('获取表单列表失败')
      }
    } catch (error) {
      console.error('获取表单列表失败:', error)
      setForms([]) // 确保设置为空数组而不是undefined
      message.error('获取表单列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 删除表单配置
  const deleteForm = async (formId: string, deleteType: 'soft' | 'data' | 'complete', force: boolean = false) => {
    try {
      let url = `/api/forms/${formId}`
      const method = 'DELETE'
      let successMessage = '表单配置删除成功'

      // 根据删除类型设置不同的参数
      switch (deleteType) {
        case 'soft':
          // 软删除：仅标记为已删除
          successMessage = '表单配置删除成功（数据已保留）'
          break
        case 'data':
          // 先删除数据，再软删除配置
          const dataResponse = await fetch(`/api/forms/${formId}/data`, {
            method: 'DELETE',
          })
          if (!dataResponse.ok) {
            const dataResult = await dataResponse.json()
            message.error(`删除数据失败: ${dataResult.error || '未知错误'}`)
            return
          }
          successMessage = '表单配置和数据删除成功'
          break
        case 'complete':
          // 完全删除：包括表结构
          url += `?fullDelete=true${force ? '&force=true' : ''}`
          successMessage = '表单和所有相关数据已完全删除'
          break
      }

      // 删除表单配置
      const response = await fetch(url, { method })
      const result = await response.json()

      if (result.success) {
        message.success(successMessage)
        if (result.warnings && result.warnings.length > 0) {
          message.warning(`注意: ${result.warnings.join(', ')}`)
        }
        fetchForms()
      } else if (result.requiresForce) {
        // 需要强制删除确认
        Modal.confirm({
          title: '确认强制删除',
          content: (
            <div className="space-y-3">
              <p className="text-red-600">⚠️ 警告：该表单包含 {result.details?.dataCount} 条数据记录！</p>
              <div className="bg-red-50 p-3 rounded border-l-4 border-red-400">
                <p className="font-medium text-red-800">强制删除将会：</p>
                <ul className="text-sm text-red-600 mt-1 list-disc list-inside">
                  <li>永久删除表单配置</li>
                  <li>永久删除所有 {result.details?.dataCount} 条数据记录</li>
                  <li>删除数据库表结构</li>
                  <li>清理相关验证失败记录</li>
                </ul>
                <p className="text-sm text-red-600 mt-2 font-medium">此操作不可恢复！</p>
              </div>
            </div>
          ),
          okText: '确认强制删除',
          okType: 'danger',
          cancelText: '取消',
          width: 520,
          onOk: () => deleteForm(formId, 'complete', true),
        })
      } else {
        message.error(result.error || '删除失败')
      }
    } catch (error) {
      console.error('删除表单配置失败:', error)
      message.error('删除失败')
    }
  }

  // 显示删除确认对话框
  const showDeleteConfirm = (formId: string, formName: string) => {
    Modal.confirm({
      title: '确认删除表单配置',
      content: (
        <div className="space-y-3">
          <p>您即将删除表单配置：<strong>{formName}</strong></p>
          <p className="text-orange-600">⚠️ 删除表单配置后，该表单将无法接收新数据。</p>
          <p>请选择删除方式：</p>
          <div className="space-y-2 mt-3">
            <div className="p-3 bg-blue-50 rounded border-l-4 border-blue-400">
              <p className="font-medium text-blue-800">仅删除配置（推荐）</p>
              <p className="text-sm text-blue-600">表单数据和表结构将保留，可以在数据查看页面继续查看历史数据</p>
            </div>
            <div className="p-3 bg-orange-50 rounded border-l-4 border-orange-400">
              <p className="font-medium text-orange-800">删除配置和数据</p>
              <p className="text-sm text-orange-600">删除表单配置和数据记录，但保留数据库表结构</p>
            </div>
            <div className="p-3 bg-red-50 rounded border-l-4 border-red-400">
              <p className="font-medium text-red-800">完全删除（包含表结构）</p>
              <p className="text-sm text-red-600">⚠️ 删除所有相关数据、表结构和验证失败记录，此操作不可恢复</p>
            </div>
          </div>
        </div>
      ),
      width: 580,
      okText: '仅删除配置',
      cancelText: '取消',
      onOk: () => deleteForm(formId, 'soft'),
      footer: (_, { OkBtn, CancelBtn }) => (
        <div className="flex justify-between">
          <div className="space-x-2">
            <Button
              danger
              onClick={() => {
                Modal.destroyAll()
                Modal.confirm({
                  title: '确认完全删除',
                  content: (
                    <div className="space-y-3">
                      <p className="text-red-600 font-medium">您即将完全删除表单：{formName}</p>
                      <div className="bg-red-50 p-3 rounded border-l-4 border-red-400">
                        <p className="font-medium text-red-800">此操作将：</p>
                        <ul className="text-sm text-red-600 mt-1 list-disc list-inside">
                          <li>永久删除表单配置</li>
                          <li>永久删除所有数据记录</li>
                          <li>删除数据库表结构</li>
                          <li>清理相关验证失败记录</li>
                        </ul>
                        <p className="text-sm text-red-600 mt-2 font-medium">此操作不可恢复！</p>
                      </div>
                    </div>
                  ),
                  okText: '确认完全删除',
                  okType: 'danger',
                  cancelText: '取消',
                  width: 520,
                  onOk: () => deleteForm(formId, 'complete'),
                })
              }}
            >
              完全删除
            </Button>
            <Button
              onClick={() => {
                Modal.destroyAll()
                Modal.confirm({
                  title: '确认删除数据',
                  content: '确定要删除表单配置和所有数据吗？数据库表结构将保留。',
                  okText: '确认删除',
                  okType: 'danger',
                  cancelText: '取消',
                  onOk: () => deleteForm(formId, 'data'),
                })
              }}
            >
              删除数据
            </Button>
          </div>
          <div>
            <CancelBtn />
            <OkBtn />
          </div>
        </div>
      ),
    })
  }

  const handleCopyWebhookUrl = async (url: string) => {
    try {
      const success = await copyToClipboard(url)
      if (success) {
        message.success('Webhook URL已复制到剪贴板')
      } else {
        message.error('复制失败，请手动复制')
      }
    } catch (error) {
      console.error('复制失败:', error)
      message.error('复制失败，请手动复制')
    }
  }

  const handleToggleStatus = (formId: string, currentStatus: boolean, formName: string) => {
    // 防止重复操作
    if (toggleLoading.has(formId)) {
      return
    }

    const action = currentStatus ? '禁用' : '启用'
    const statusText = currentStatus ? '禁用后将不再接收Webhook数据' : '启用后将开始接收Webhook数据'

    Modal.confirm({
      title: `确认${action}表单`,
      content: (
        <div>
          <p>表单名称：{formName}</p>
          <p>{statusText}</p>
          <p>确定要{action}此表单吗？</p>
        </div>
      ),
      okText: `确定${action}`,
      okType: currentStatus ? 'danger' : 'primary',
      cancelText: '取消',
      onOk: async () => {
        // 设置loading状态
        setToggleLoading(prev => new Set(prev).add(formId))

        // 乐观更新：立即更新本地状态
        const newStatus = !currentStatus
        setForms(prevForms =>
          prevForms.map(form =>
            form.formId === formId
              ? { ...form, isActive: newStatus }
              : form
          )
        )

        try {
          const response = await fetch(`/api/forms/${formId}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              isActive: newStatus,
            }),
          })

          const result = await response.json()

          if (response.ok && result.success) {
            message.success(result.message)
            // API成功，状态已经是正确的，不需要额外操作
          } else {
            // API失败，回滚本地状态
            setForms(prevForms =>
              prevForms.map(form =>
                form.formId === formId
                  ? { ...form, isActive: currentStatus }
                  : form
              )
            )
            message.error(result.error || `${action}失败`)
          }
        } catch (error) {
          console.error(`${action}表单失败:`, error)
          // 网络错误，回滚本地状态
          setForms(prevForms =>
            prevForms.map(form =>
              form.formId === formId
                ? { ...form, isActive: currentStatus }
                : form
            )
          )
          message.error(`${action}失败，请重试`)
        } finally {
          // 清除loading状态
          setToggleLoading(prev => {
            const newSet = new Set(prev)
            newSet.delete(formId)
            return newSet
          })
        }
      },
    })
  }

  const columns: ColumnsType<FormConfig> = [
    {
      title: '表单名称',
      dataIndex: 'formName',
      key: 'formName',
      render: (text, record) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-gray-500 text-sm">ID: {record.formId}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive, record) => {
        const isToggling = toggleLoading.has(record.formId)
        return (
          <div className="flex items-center space-x-2">
            <Tag color={isActive ? 'green' : 'red'}>
              {isActive ? '启用' : '禁用'}
            </Tag>
            <Tooltip title={
              isToggling
                ? '正在切换状态...'
                : (isActive ? '点击禁用表单' : '点击启用表单')
            }>
              <Switch
                size="small"
                checked={isActive}
                loading={isToggling}
                disabled={isToggling}
                onChange={() => handleToggleStatus(record.formId, isActive, record.formName)}
                checkedChildren="开"
                unCheckedChildren="关"
              />
            </Tooltip>
          </div>
        )
      },
    },
    {
      title: '字段数量',
      dataIndex: 'fieldCount',
      key: 'fieldCount',
      render: count => (
        <span className="font-mono">{count?.toLocaleString() ?? '0'}</span>
      ),
    },
    {
      title: 'Webhook URL',
      dataIndex: 'webhookUrl',
      key: 'webhookUrl',
      width: 300,
      render: url => (
        <div className="flex items-center space-x-2">
          <Tooltip title={url || '未设置'} placement="top">
            <code className="text-xs bg-gray-100 px-2 py-1 rounded flex-1 block truncate">
              {url || '未设置'}
            </code>
          </Tooltip>
          {url && (
            <Button
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopyWebhookUrl(url)}
              title="复制Webhook URL"
            />
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: date => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => router.push(`/data/view?formId=${record.formId}`)}
          >
            查看数据
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => router.push(`/forms/config?id=${record.formId}`)}
          >
            编辑
          </Button>
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => showDeleteConfirm(record.formId, record.formName)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">表单列表</h1>
          <p className="text-sm sm:text-base text-gray-600 mt-1">
            管理所有已配置的表单和其数据接收状态
          </p>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => router.push('/forms/config')}
          className="touch-target mobile-full-width sm:w-auto"
          size="middle"
        >
          <span className="hidden sm:inline">新建表单配置</span>
          <span className="sm:hidden">新建表单</span>
        </Button>
      </div>

      <Card className="mobile-card">
        <Table
          columns={columns}
          dataSource={Array.isArray(forms) ? forms : []}
          loading={loading}
          rowKey="id"
          scroll={{
            x: 'max-content',
            scrollToFirstRowOnChange: true
          }}
          size="small"
          className="mobile-table"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: total => `共 ${total} 个表单配置`,
            responsive: true,
            showLessItems: true,
          }}
        />
      </Card>
    </div>
  )
}
