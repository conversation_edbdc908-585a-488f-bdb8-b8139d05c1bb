'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import {
  Card,
  Typography,
  Steps,
  Button,
  Space,
  Form,
  Input,
  Select,
  message,
  Alert,
  Divider,
  Spin,
} from 'antd'
import {
  FormOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  <PERSON>LeftOutlined,
  ArrowRightOutlined,
  EditOutlined,
} from '@ant-design/icons'
import { JsonInputStep } from '@/components/forms/JsonInputStep'
import { FieldMappingStep } from '@/components/forms/FieldMappingStep'
import { ConfigReviewStep } from '@/components/forms/ConfigReviewStep'
import { DebugInfo } from '@/components/forms/DebugInfo'

const { Title, Text } = Typography

interface FormConfigData {
  formId: string
  formName: string
  sampleJson: any
  fieldMapping: Record<string, any>
  dataFormat: 'jinshuju_standard' | 'jinshuju_automation' | 'custom'
  dataPathConfig?: any
}

export default function FormConfigPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const editFormId = searchParams.get('id')
  const isEditMode = !!editFormId

  const [currentStep, setCurrentStep] = useState(0)
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(isEditMode)
  const [formData, setFormData] = useState<FormConfigData>({
    formId: '',
    formName: '',
    sampleJson: null,
    fieldMapping: {},
    dataFormat: 'jinshuju_standard',
    dataPathConfig: undefined,
  })

  // Fetch existing form data when in edit mode
  useEffect(() => {
    if (isEditMode && editFormId) {
      fetchFormData(editFormId)
    }
  }, [isEditMode, editFormId])

  const fetchFormData = async (formId: string) => {
    try {
      setInitialLoading(true)
      const response = await fetch(`/api/forms/${formId}`)
      const result = await response.json()

      if (result.success) {
        const formConfig = result.data
        setFormData({
          formId: formConfig.formId,
          formName: formConfig.formName,
          sampleJson: formConfig.sampleJson,
          fieldMapping: formConfig.fieldMapping,
          dataFormat: formConfig.dataFormat || 'jinshuju_standard',
          dataPathConfig: formConfig.dataPathConfig,
        })
      } else {
        message.error('获取表单配置失败')
        router.push('/forms')
      }
    } catch (error) {
      console.error('获取表单配置失败:', error)
      message.error('获取表单配置失败')
      router.push('/forms')
    } finally {
      setInitialLoading(false)
    }
  }

  const steps = [
    {
      title: '基本信息',
      description: isEditMode ? '修改表单基本信息' : '输入表单基本信息',
      icon: <FormOutlined />,
    },
    {
      title: 'JSON样例',
      description: isEditMode ? '修改金数据JSON样例' : '输入金数据JSON样例',
      icon: <FileTextOutlined />,
    },
    {
      title: '字段映射',
      description: isEditMode ? '修改字段映射关系' : '配置字段映射关系',
      icon: <DatabaseOutlined />,
    },
    {
      title: '确认配置',
      description: isEditMode ? '确认修改内容' : '检查并确认配置',
      icon: <CheckCircleOutlined />,
    },
  ]

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleBasicInfoSubmit = (values: {
    formId: string
    formName: string
    dataFormat: 'jinshuju_standard' | 'jinshuju_automation' | 'custom'
  }) => {
    setFormData(prev => ({
      ...prev,
      formId: values.formId,
      formName: values.formName,
      dataFormat: values.dataFormat,
    }))
    handleNext()
  }

  const handleJsonSubmit = (jsonData: any) => {
    setFormData(prev => ({
      ...prev,
      sampleJson: jsonData,
    }))
    handleNext()
  }

  const handleMappingSubmit = (mapping: Record<string, any>) => {
    setFormData(prev => ({
      ...prev,
      fieldMapping: mapping,
    }))
    handleNext()
  }

  const handleFinalSubmit = async () => {
    setLoading(true)
    try {
      const url = isEditMode ? `/api/forms/${editFormId}` : '/api/forms'
      const method = isEditMode ? 'PUT' : 'POST'

      // For edit mode, only send the fields that can be updated
      const submitData = isEditMode
        ? {
            formName: formData.formName,
            fieldMapping: formData.fieldMapping,
            sampleJson: formData.sampleJson,
          }
        : formData

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || (isEditMode ? '更新失败' : '创建失败'))
      }

      if (result.success) {
        message.success(
          isEditMode ? '表单配置更新成功！' : '表单配置创建成功！'
        )

        if (isEditMode) {
          // Redirect to forms list after successful edit
          router.push('/forms')
        } else {
          // Reset form for new creation
          setFormData({
            formId: '',
            formName: '',
            sampleJson: null,
            fieldMapping: {},
            dataFormat: 'jinshuju_standard',
            dataPathConfig: undefined,
          })
          setCurrentStep(0)
        }
      } else {
        throw new Error(result.error || (isEditMode ? '更新失败' : '创建失败'))
      }
    } catch (error) {
      console.error(
        isEditMode ? '更新表单配置失败:' : '创建表单配置失败:',
        error
      )
      message.error(
        error instanceof Error
          ? error.message
          : isEditMode
            ? '更新失败，请重试'
            : '创建失败，请重试'
      )
    } finally {
      setLoading(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card>
            <Title level={4} className="mb-4">
              表单基本信息
            </Title>
            <Form
              layout="vertical"
              onFinish={handleBasicInfoSubmit}
              initialValues={{
                formId: formData.formId,
                formName: formData.formName,
                dataFormat: formData.dataFormat,
              }}
            >
              <Form.Item
                label="表单ID"
                name="formId"
                rules={
                  isEditMode
                    ? []
                    : [
                        { required: true, message: '请输入表单ID' },
                        {
                          pattern: /^[a-zA-Z0-9_-]+$/,
                          message: '表单ID只能包含字母、数字、下划线和横线',
                        },
                      ]
                }
                help={
                  isEditMode
                    ? '表单ID不可修改'
                    : '这是金数据表单的唯一标识符，通常在Webhook URL中可以找到'
                }
              >
                <Input
                  placeholder="例如: ZFs2eo"
                  disabled={isEditMode}
                  readOnly={isEditMode}
                />
              </Form.Item>

              <Form.Item
                label="表单名称"
                name="formName"
                rules={[
                  { required: true, message: '请输入表单名称' },
                  { max: 200, message: '表单名称不能超过200个字符' },
                ]}
              >
                <Input placeholder="例如: 预约免费肺功能检查、免费办理慢性病医保" />
              </Form.Item>

              <Form.Item
                label="数据格式"
                name="dataFormat"
                rules={[{ required: true, message: '请选择数据格式' }]}
                help="选择对应的金数据webhook格式类型"
              >
                <Select 
                  placeholder="请选择数据格式类型"
                  optionLabelProp="label"
                >
                  <Select.Option 
                    value="jinshuju_standard" 
                    label="金数据标准格式"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">金数据标准格式</span>
                      <span className="text-xs text-gray-500 mt-1">
                        含有serial_number的标准webhook格式
                      </span>
                    </div>
                  </Select.Option>
                  <Select.Option 
                    value="jinshuju_automation" 
                    label="金数据自动化格式"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">金数据自动化格式</span>
                      <span className="text-xs text-gray-500 mt-1">
                        使用token标识，包含微信相关字段
                      </span>
                    </div>
                  </Select.Option>
                  <Select.Option 
                    value="custom" 
                    label="自定义格式"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">自定义格式</span>
                      <span className="text-xs text-gray-500 mt-1">
                        需要配置自定义数据路径映射
                      </span>
                    </div>
                  </Select.Option>
                </Select>
              </Form.Item>

              <Alert
                message="提示"
                description={
                  isEditMode
                    ? '您正在编辑现有表单配置。表单ID不可修改，但可以更新表单名称、数据格式和其他配置。'
                    : '表单ID是金数据表单的唯一标识，用于接收对应表单的Webhook数据。数据格式用于正确解析不同类型的JSON结构。根据您使用的金数据功能选择对应格式：标准格式适用于普通表单，自动化格式适用于微信推广等自动化功能。'
                }
                type="info"
                showIcon
                className="mb-4"
              />

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  className="touch-target mobile-full-width"
                >
                  下一步
                  <ArrowRightOutlined />
                </Button>
              </Form.Item>
            </Form>
          </Card>
        )

      case 1:
        return (
          <JsonInputStep
            onSubmit={handleJsonSubmit}
            initialValue={formData.sampleJson}
            dataFormat={formData.dataFormat}
          />
        )

      case 2:
        return (
          <FieldMappingStep
            sampleJson={formData.sampleJson}
            onSubmit={handleMappingSubmit}
            initialMapping={formData.fieldMapping}
            dataFormat={formData.dataFormat}
          />
        )

      case 3:
        return (
          <ConfigReviewStep
            formData={formData}
            onSubmit={handleFinalSubmit}
            loading={loading}
            isEditMode={isEditMode}
          />
        )

      default:
        return null
    }
  }

  if (initialLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
          <span className="ml-3">加载表单配置中...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <DebugInfo 
        title="Form Config State" 
        data={{ 
          currentStep, 
          formData: {
            formId: formData.formId,
            formName: formData.formName,
            dataFormat: formData.dataFormat,
            hasDataPathConfig: !!formData.dataPathConfig
          },
          isEditMode
        }} 
      />
      
      {/* 页面标题 */}
      <div>
        <div className="flex items-center mb-2">
          {isEditMode && <EditOutlined className="mr-2 text-blue-500" />}
          <Title level={2} className="mb-0">
            {isEditMode ? '编辑表单配置' : '新建表单配置'}
          </Title>
        </div>
        <Text type="secondary">
          {isEditMode
            ? `正在编辑表单: ${formData.formName || formData.formId}`
            : '配置金数据表单的字段映射，系统将根据配置自动处理Webhook数据'}
        </Text>
      </div>

      {/* 步骤指示器 */}
      <Card>
        <Steps
          current={currentStep}
          items={steps}
          size="small"
          className="mb-0"
        />
      </Card>

      {/* 步骤内容 */}
      {renderStepContent()}

      {/* 底部操作按钮 */}
      {currentStep > 0 && currentStep < steps.length - 1 && (
        <Card>
          <div className="flex justify-between">
            <Button onClick={handlePrev} disabled={loading}>
              <ArrowLeftOutlined />
              上一步
            </Button>
            <Button type="primary" onClick={handleNext} disabled={loading}>
              下一步
              <ArrowRightOutlined />
            </Button>
          </div>
        </Card>
      )}

      {currentStep === steps.length - 1 && (
        <Card>
          <div className="flex justify-between">
            <Button onClick={handlePrev} disabled={loading}>
              <ArrowLeftOutlined />
              上一步
            </Button>
            <Space>
              <Button onClick={() => setCurrentStep(0)} disabled={loading}>
                重新配置
              </Button>
              <Button
                type="primary"
                onClick={handleFinalSubmit}
                loading={loading}
                size="large"
              >
                创建配置
              </Button>
            </Space>
          </div>
        </Card>
      )}
    </div>
  )
}
