'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  Typography,
  Table,
  Input,
  Select,
  DatePicker,
  Space,
  Tag,
  Button,
  Modal,
  Descriptions,
  Alert,
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  FilterOutlined,
  ClearOutlined,
  Exclamation<PERSON><PERSON>cleOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface SystemLog {
  id: string
  userId: number | null
  action: string
  resource: string
  resourceId: string | null
  details: any
  ipAddress: string
  userAgent: string
  createdAt: string
  user?: {
    username: string
    doctor_name: string
  }
}

interface LogListResponse {
  success: boolean
  data: {
    logs: SystemLog[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }
}

export default function LogsPage() {
  const [logs, setLogs] = useState<SystemLog[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null)
  const [detailVisible, setDetailVisible] = useState(false)
  const [filters, setFilters] = useState({
    action: '',
    resource: '',
    userId: '',
    ipAddress: '',
    dateRange: null as any,
  })
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  })

  // 操作类型映射
  const actionMap: Record<string, { label: string; color: string; icon: React.ReactNode }> = {
    'LOGIN': { label: '用户登录', color: 'green', icon: <CheckCircleOutlined /> },
    'LOGOUT': { label: '用户登出', color: 'blue', icon: <InfoCircleOutlined /> },
    'CREATE_USER': { label: '创建用户', color: 'cyan', icon: <CheckCircleOutlined /> },
    'UPDATE_USER': { label: '更新用户', color: 'orange', icon: <ExclamationCircleOutlined /> },
    'DELETE_USER': { label: '删除用户', color: 'red', icon: <CloseCircleOutlined /> },
    'CHANGE_PASSWORD': { label: '修改密码', color: 'purple', icon: <ExclamationCircleOutlined /> },
    'RESET_USER_PASSWORD': { label: '重置密码', color: 'volcano', icon: <ExclamationCircleOutlined /> },
    'UPDATE_PROFILE': { label: '更新资料', color: 'blue', icon: <InfoCircleOutlined /> },
    'CREATE_FORM_CONFIG': { label: '创建表单配置', color: 'green', icon: <CheckCircleOutlined /> },
    'UPDATE_FORM_CONFIG': { label: '更新表单配置', color: 'orange', icon: <ExclamationCircleOutlined /> },
    'DELETE_FORM_CONFIG': { label: '删除表单配置', color: 'red', icon: <CloseCircleOutlined /> },
    'WEBHOOK_RECEIVED': { label: 'Webhook接收', color: 'lime', icon: <CheckCircleOutlined /> },
    'WEBHOOK_ERROR': { label: 'Webhook错误', color: 'red', icon: <CloseCircleOutlined /> },
    'BATCH_DELETE_DATA': { label: '批量删除数据', color: 'red', icon: <CloseCircleOutlined /> },
    'BATCH_UPDATE_DATA': { label: '批量更新数据', color: 'orange', icon: <ExclamationCircleOutlined /> },
    'EXPORT_DATA': { label: '导出数据', color: 'cyan', icon: <InfoCircleOutlined /> },
  }

  // 资源类型映射
  const resourceMap: Record<string, string> = {
    'User': '用户',
    'FormConfig': '表单配置',
    'WebhookData': 'Webhook数据',
    'FormData': '表单数据',
  }

  // 获取日志列表
  const fetchLogs = async (page = 1, searchFilters = filters) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.pageSize.toString(),
      })

      // 添加搜索参数
      if (searchFilters.action) params.append('action', searchFilters.action)
      if (searchFilters.resource) params.append('resource', searchFilters.resource)
      if (searchFilters.userId) params.append('userId', searchFilters.userId)
      if (searchFilters.ipAddress) params.append('ipAddress', searchFilters.ipAddress)
      if (searchFilters.dateRange && searchFilters.dateRange.length === 2) {
        params.append('startDate', dayjs(searchFilters.dateRange[0]).format('YYYY-MM-DD'))
        params.append('endDate', dayjs(searchFilters.dateRange[1]).format('YYYY-MM-DD'))
      }

      const response = await fetch(`/api/logs?${params}`)
      const result: LogListResponse = await response.json()

      if (result.success) {
        setLogs(result.data.logs)
        setPagination({
          current: result.data.pagination.page,
          pageSize: result.data.pagination.limit,
          total: result.data.pagination.total,
        })
      } else {
        console.error('获取日志列表失败')
      }
    } catch (error) {
      console.error('获取日志列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 搜索处理
  const handleSearch = () => {
    fetchLogs(1, filters)
  }

  // 清空搜索
  const handleClearSearch = () => {
    setFilters({
      action: '',
      resource: '',
      userId: '',
      ipAddress: '',
      dateRange: null,
    })
    fetchLogs(1, {
      action: '',
      resource: '',
      userId: '',
      ipAddress: '',
      dateRange: null,
    })
  }

  // 查看详情
  const showDetail = (log: SystemLog) => {
    setSelectedLog(log)
    setDetailVisible(true)
  }

  useEffect(() => {
    fetchLogs()
  }, [])

  const columns: ColumnsType<SystemLog> = [
    {
      title: '时间',
      dataIndex: 'createdAt',
      width: 160,
      render: (date) => (
        <div>
          <div>{dayjs(date).format('MM-DD HH:mm:ss')}</div>
          <Text type="secondary" className="text-xs">
            {dayjs(date).format('YYYY')}
          </Text>
        </div>
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 140,
      render: (action) => {
        const actionInfo = actionMap[action] || { 
          label: action, 
          color: 'default', 
          icon: <InfoCircleOutlined /> 
        }
        return (
          <Tag 
            color={actionInfo.color} 
            icon={actionInfo.icon}
            className="flex items-center"
          >
            {actionInfo.label}
          </Tag>
        )
      },
    },
    {
      title: '资源',
      dataIndex: 'resource',
      width: 100,
      render: (resource) => (
        <Tag color="blue">
          {resourceMap[resource] || resource}
        </Tag>
      ),
    },
    {
      title: '用户',
      key: 'user',
      width: 120,
      render: (_, record) => {
        if (!record.user) {
          return <Text type="secondary">系统</Text>
        }
        return (
          <div>
            <div>{record.user.doctor_name || record.user.username}</div>
            <Text type="secondary" className="text-xs">
              @{record.user.username}
            </Text>
          </div>
        )
      },
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      width: 120,
      render: (ip) => <Text code>{ip}</Text>,
    },
    {
      title: '详情',
      key: 'details',
      ellipsis: true,
      render: (_, record) => {
        const details = record.details
        if (!details || typeof details !== 'object') {
          return '-'
        }
        
        // 提取关键信息显示
        const keyInfo = []
        if (details.formId) keyInfo.push(`表单: ${details.formId}`)
        if (details.username) keyInfo.push(`用户: ${details.username}`)
        if (details.recordCount) keyInfo.push(`数量: ${details.recordCount}`)
        if (details.tableName) keyInfo.push(`表: ${details.tableName}`)
        
        return keyInfo.length > 0 ? keyInfo.join(', ') : '-'
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Button
          type="text"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => showDetail(record)}
        />
      ),
    },
  ]

  return (
    <div className="space-y-4">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <Title level={2} className="mb-1">
            系统日志
          </Title>
          <Text type="secondary">
            查看系统操作记录和审计日志
          </Text>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => fetchLogs(pagination.current, filters)}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 搜索筛选 */}
      <Card size="small">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            <Select
              placeholder="操作类型"
              value={filters.action}
              onChange={(value) => setFilters({ ...filters, action: value })}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(actionMap).map(([key, value]) => (
                <Option key={key} value={key}>
                  {value.label}
                </Option>
              ))}
            </Select>

            <Select
              placeholder="资源类型"
              value={filters.resource}
              onChange={(value) => setFilters({ ...filters, resource: value })}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(resourceMap).map(([key, value]) => (
                <Option key={key} value={key}>
                  {value}
                </Option>
              ))}
            </Select>

            <Input
              placeholder="用户ID"
              value={filters.userId}
              onChange={(e) => setFilters({ ...filters, userId: e.target.value })}
              allowClear
            />

            <Input
              placeholder="IP地址"
              value={filters.ipAddress}
              onChange={(e) => setFilters({ ...filters, ipAddress: e.target.value })}
              allowClear
            />

            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
              style={{ width: '100%' }}
            />
          </div>

          <div>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                搜索
              </Button>
              <Button
                icon={<ClearOutlined />}
                onClick={handleClearSearch}
              >
                重置
              </Button>
            </Space>
          </div>
        </div>
      </Card>

      {/* 日志列表 */}
      <Card>
        <Alert
          message="日志说明"
          description="系统自动记录所有关键操作，包括用户管理、表单配置、数据操作等。日志数据用于安全审计和问题排查。"
          type="info"
          showIcon
          className="mb-4"
        />

        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              if (pageSize !== pagination.pageSize) {
                setPagination({ ...pagination, pageSize })
              }
              fetchLogs(page, filters)
            },
          }}
          scroll={{ x: 1000 }}
          size="small"
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="日志详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {selectedLog && (
          <div className="space-y-4">
            <Descriptions bordered size="small" column={2}>
              <Descriptions.Item label="操作时间" span={2}>
                {dayjs(selectedLog.createdAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="操作类型" span={1}>
                <Tag 
                  color={actionMap[selectedLog.action]?.color || 'default'}
                  icon={actionMap[selectedLog.action]?.icon}
                >
                  {actionMap[selectedLog.action]?.label || selectedLog.action}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="资源类型" span={1}>
                <Tag color="blue">
                  {resourceMap[selectedLog.resource] || selectedLog.resource}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="资源ID" span={1}>
                {selectedLog.resourceId || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="操作用户" span={1}>
                {selectedLog.user ? 
                  `${selectedLog.user.doctor_name || selectedLog.user.username} (@${selectedLog.user.username})` : 
                  '系统'
                }
              </Descriptions.Item>
              <Descriptions.Item label="IP地址" span={1}>
                <Text code>{selectedLog.ipAddress}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="用户代理" span={1}>
                <Text ellipsis style={{ maxWidth: 200 }}>
                  {selectedLog.userAgent}
                </Text>
              </Descriptions.Item>
            </Descriptions>

            {selectedLog.details && (
              <div>
                <Title level={5}>详细信息</Title>
                <pre className="bg-gray-50 p-4 rounded text-sm overflow-auto max-h-64">
                  {JSON.stringify(selectedLog.details, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}