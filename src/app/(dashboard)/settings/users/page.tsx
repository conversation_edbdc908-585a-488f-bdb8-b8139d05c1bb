'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Tag,
  Avatar,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Select,
  Alert,
  Tooltip,
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  ReloadOutlined,
  EyeOutlined,
  KeyOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { Search } = Input
const { Option } = Select

interface User {
  id: string
  username: string
  nickname: string
  email: string | null
  avatar: string | null
  isActive: boolean
  role: string
  createdAt: string
  lastLoginAt: string | null
  loginCount: number
}

interface UserListResponse {
  success: boolean
  data: {
    users: User[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [modalVisible, setModalVisible] = useState(false)
  const [modalType, setModalType] = useState<'create' | 'edit' | 'view'>(
    'create'
  )
  const [form] = Form.useForm()
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  // 获取用户列表
  const fetchUsers = async (page = 1, search = '', pageSize?: number) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: (pageSize || pagination.pageSize).toString(),
        ...(search && { search }),
      })

      const response = await fetch(`/api/users?${params}`)
      const result: UserListResponse = await response.json()

      if (result.success) {
        setUsers(result.data.users)
        setPagination({
          current: result.data.pagination.page,
          pageSize: result.data.pagination.limit,
          total: result.data.pagination.total,
        })
      } else {
        message.error('获取用户列表失败')
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
      message.error('获取用户列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value)
    fetchUsers(1, value)
  }

  // 打开模态框
  const openModal = (type: 'create' | 'edit' | 'view', user?: User) => {
    setModalType(type)
    setSelectedUser(user || null)
    setModalVisible(true)

    if (type === 'edit' && user) {
      form.setFieldsValue({
        username: user.username,
        nickname: user.nickname,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
      })
    } else if (type === 'create') {
      form.resetFields()
    }
  }

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false)
    setSelectedUser(null)
    form.resetFields()
  }

  // 提交用户数据
  const handleSubmit = async (values: any) => {
    try {
      const url =
        modalType === 'create' ? '/api/users' : `/api/users/${selectedUser?.id}`
      const method = modalType === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      })

      const result = await response.json()

      if (result.success) {
        message.success(
          modalType === 'create' ? '用户创建成功' : '用户更新成功'
        )
        closeModal()
        fetchUsers(pagination.current, searchText)
      } else {
        message.error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('用户操作失败:', error)
      message.error('操作失败')
    }
  }

  // 切换用户状态
  const toggleUserStatus = async (userId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      })

      const result = await response.json()

      if (result.success) {
        message.success(`用户已${isActive ? '启用' : '禁用'}`)
        fetchUsers(pagination.current, searchText)
      } else {
        message.error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('切换用户状态失败:', error)
      message.error('操作失败')
    }
  }

  // 删除用户
  const deleteUser = async (userId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        message.success('用户删除成功')
        fetchUsers(pagination.current, searchText)
      } else {
        message.error(result.error || '删除失败')
      }
    } catch (error) {
      console.error('删除用户失败:', error)
      message.error('删除失败')
    }
  }

  // 重置密码
  const resetPassword = async (userId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}/reset-password`, {
        method: 'POST',
      })

      const result = await response.json()

      if (result.success) {
        Modal.info({
          title: '密码重置成功',
          content: (
            <div>
              <p>
                新密码: <code>{result.data.newPassword}</code>
              </p>
              <p className="text-red-500 text-sm mt-2">
                请将新密码告知用户，并提醒用户及时修改密码
              </p>
            </div>
          ),
        })
      } else {
        message.error(result.error || '重置密码失败')
      }
    } catch (error) {
      console.error('重置密码失败:', error)
      message.error('重置密码失败')
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  const columns: ColumnsType<User> = [
    {
      title: '用户',
      key: 'user',
      width: 200,
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <Avatar size={40} src={record.avatar} icon={<UserOutlined />} />
          <div>
            <div className="font-medium">{record.nickname}</div>
            <div className="text-gray-500 text-sm">@{record.username}</div>
          </div>
        </div>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      render: email => email || <Text type="secondary">未设置</Text>,
    },
    {
      title: '角色',
      dataIndex: 'role',
      width: 100,
      render: role => (
        <Tag color={role === 'admin' ? 'red' : 'blue'}>
          {role === 'admin' ? '管理员' : '普通用户'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 80,
      render: isActive => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '正常' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '登录次数',
      dataIndex: 'loginCount',
      width: 100,
      render: count => <Text>{count || 0}</Text>,
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      width: 160,
      render: date =>
        date ? (
          new Date(date).toLocaleString('zh-CN')
        ) : (
          <Text type="secondary">从未登录</Text>
        ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => openModal('view', record)}
            />
          </Tooltip>
          <Tooltip title="编辑用户">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => openModal('edit', record)}
            />
          </Tooltip>
          <Tooltip title="重置密码">
            <Popconfirm
              title="确认重置密码"
              description="系统将生成新的随机密码，确定要重置吗？"
              onConfirm={() => resetPassword(record.id)}
              okText="确认"
              cancelText="取消"
            >
              <Button type="text" size="small" icon={<KeyOutlined />} />
            </Popconfirm>
          </Tooltip>
          <Popconfirm
            title="确认删除"
            description="删除后将无法恢复，确定要删除这个用户吗？"
            onConfirm={() => deleteUser(record.id)}
            okText="确认删除"
            cancelText="取消"
            okType="danger"
          >
            <Tooltip title="删除用户">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div className="space-y-4">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <Title level={2} className="mb-1">
            用户管理
          </Title>
          <Text type="secondary">管理系统用户账户和权限</Text>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => fetchUsers(pagination.current, searchText)}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openModal('create')}
          >
            添加用户
          </Button>
        </Space>
      </div>

      {/* 搜索和筛选 */}
      <Card size="small">
        <div className="flex justify-between items-center">
          <Search
            placeholder="搜索用户名、昵称或邮箱"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
            loading={loading}
            enterButton={<SearchOutlined />}
          />
          <div>
            <Text type="secondary">共 {pagination.total} 个用户</Text>
          </div>
        </div>
      </Card>

      {/* 用户列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={Array.isArray(users) ? users : []}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              const newPageSize = pageSize || pagination.pageSize
              setPagination({
                ...pagination,
                current: page,
                pageSize: newPageSize
              })
              fetchUsers(page, searchText, newPageSize)
            },
          }}
          scroll={{
            x: 'max-content',
            scrollToFirstRowOnChange: true
          }}
          size="small"
          className="mobile-table"
        />
      </Card>

      {/* 用户编辑/创建模态框 */}
      <Modal
        title={
          modalType === 'create'
            ? '添加用户'
            : modalType === 'edit'
              ? '编辑用户'
              : '用户详情'
        }
        open={modalVisible}
        onCancel={closeModal}
        footer={
          modalType === 'view'
            ? [
                <Button key="close" onClick={closeModal}>
                  关闭
                </Button>,
              ]
            : [
                <Button key="cancel" onClick={closeModal}>
                  取消
                </Button>,
                <Button
                  key="submit"
                  type="primary"
                  onClick={() => form.submit()}
                >
                  {modalType === 'create' ? '创建' : '更新'}
                </Button>,
              ]
        }
        width={600}
      >
        {modalType === 'view' && selectedUser ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Avatar
                size={64}
                src={selectedUser.avatar}
                icon={<UserOutlined />}
              />
              <div>
                <Title level={4} className="mb-1">
                  {selectedUser.nickname}
                </Title>
                <Text type="secondary">@{selectedUser.username}</Text>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Text type="secondary">邮箱:</Text>
                <div>{selectedUser.email || '未设置'}</div>
              </div>
              <div>
                <Text type="secondary">角色:</Text>
                <div>
                  <Tag color={selectedUser.role === 'admin' ? 'red' : 'blue'}>
                    {selectedUser.role === 'admin' ? '管理员' : '普通用户'}
                  </Tag>
                </div>
              </div>
              <div>
                <Text type="secondary">状态:</Text>
                <div>
                  <Tag color={selectedUser.isActive ? 'green' : 'red'}>
                    {selectedUser.isActive ? '正常' : '禁用'}
                  </Tag>
                </div>
              </div>
              <div>
                <Text type="secondary">登录次数:</Text>
                <div>{selectedUser.loginCount || 0}</div>
              </div>
              <div>
                <Text type="secondary">注册时间:</Text>
                <div>
                  {new Date(selectedUser.createdAt).toLocaleString('zh-CN')}
                </div>
              </div>
              <div>
                <Text type="secondary">最后登录:</Text>
                <div>
                  {selectedUser.lastLoginAt
                    ? new Date(selectedUser.lastLoginAt).toLocaleString('zh-CN')
                    : '从未登录'}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <Form form={form} layout="vertical" onFinish={handleSubmit}>
            {modalType === 'create' && (
              <Alert
                message="密码设置"
                description="系统将自动生成初始密码，用户首次登录后需要修改密码"
                type="info"
                showIcon
                className="mb-4"
              />
            )}

            <Form.Item
              label="用户名"
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                {
                  pattern: /^[a-zA-Z0-9_-]+$/,
                  message: '用户名只能包含字母、数字、下划线和横线',
                },
                { min: 3, message: '用户名至少3个字符' },
                { max: 20, message: '用户名不能超过20个字符' },
              ]}
            >
              <Input
                placeholder="请输入用户名"
                disabled={modalType === 'edit'}
              />
            </Form.Item>

            <Form.Item
              label="昵称"
              name="nickname"
              rules={[
                { required: true, message: '请输入昵称' },
                { max: 50, message: '昵称不能超过50个字符' },
              ]}
            >
              <Input placeholder="请输入昵称" />
            </Form.Item>

            <Form.Item
              label="邮箱"
              name="email"
              rules={[
                { type: 'email', message: '请输入有效的邮箱地址' },
                { max: 100, message: '邮箱地址不能超过100个字符' },
              ]}
            >
              <Input placeholder="请输入邮箱地址（可选）" />
            </Form.Item>

            <Form.Item
              label="角色"
              name="role"
              rules={[{ required: true, message: '请选择用户角色' }]}
              initialValue="user"
            >
              <Select placeholder="请选择用户角色">
                <Option value="user">普通用户</Option>
                <Option value="admin">管理员</Option>
              </Select>
            </Form.Item>

            {modalType === 'edit' && (
              <Form.Item
                label="状态"
                name="isActive"
                valuePropName="checked"
                initialValue={true}
              >
                <Select>
                  <Option value={true}>启用</Option>
                  <Option value={false}>禁用</Option>
                </Select>
              </Form.Item>
            )}
          </Form>
        )}
      </Modal>
    </div>
  )
}
