import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 处理 BigInt 序列化的通用函数
function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (typeof obj === 'bigint') {
    return obj.toString()
  }

  if (obj instanceof Date) {
    return obj.toISOString()
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeBigInt)
  }

  if (typeof obj === 'object') {
    const serialized: any = {}
    for (const [key, value] of Object.entries(obj)) {
      if (key === 'raw_data' && typeof value === 'string') {
        try {
          serialized[key] = JSON.parse(value)
        } catch {
          serialized[key] = value
        }
      } else {
        serialized[key] = serializeBigInt(value)
      }
    }
    return serialized
  }

  return obj
}
import { checkTableExists, createDynamicTable } from '@/lib/dynamicTable'
import { z } from 'zod'

// GET /api/data/[formId] - 获取表单数据
export async function GET(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.formId
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    // 获取表单配置 - 允许访问禁用但未删除的表单
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isDeleted: false },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在或已删除' },
        { status: 404 }
      )
    }

    const tableName = formConfig.tableName || `form_data_${formId}`
    const fieldMapping = formConfig.fieldMapping as Record<string, any>
    const skip = (page - 1) * limit

    // 检查数据表是否存在，如果不存在则创建
    const tableExists = await checkTableExists(tableName)
    if (!tableExists) {
      console.log(`表 ${tableName} 不存在，正在创建...`)
      const createResult = await createDynamicTable(formId, fieldMapping)
      if (!createResult.success) {
        return NextResponse.json(
          {
            success: false,
            error: `数据表不存在且创建失败: ${createResult.error}`
          },
          { status: 500 }
        )
      }
      console.log(`表 ${tableName} 创建成功`)
    }

    // 构建搜索条件
    const whereConditions: string[] = []
    const queryParams: any[] = []
    const paramIndex = 1

    // 处理搜索参数
    for (const [key, value] of Array.from(searchParams.entries())) {
      if (key === 'page' || key === 'limit') continue

      if (key.endsWith('_start')) {
        const fieldName = key.replace('_start', '')
        whereConditions.push(`\`${fieldName}\` >= ?`)
        queryParams.push(value)
      } else if (key.endsWith('_end')) {
        const fieldName = key.replace('_end', '')
        whereConditions.push(`\`${fieldName}\` <= ?`)
        queryParams.push(value + ' 23:59:59')
      } else if (key === 'serial_number') {
        whereConditions.push('serial_number = ?')
        queryParams.push(parseInt(value))
      } else {
        // 字符串字段模糊搜索
        whereConditions.push(`\`${key}\` LIKE ?`)
        queryParams.push(`%${value}%`)
      }
    }

    const whereClause =
      whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM \`${tableName}\` ${whereClause}`
    const countResult = await prisma.$queryRawUnsafe<Array<{ total: bigint }>>(
      countQuery,
      ...queryParams
    )
    const total = Number(countResult[0]?.total || 0)

    // 获取数据
    const dataQuery = `
      SELECT * FROM \`${tableName}\` 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `

    const records = await prisma.$queryRawUnsafe<Array<any>>(
      dataQuery,
      ...queryParams,
      limit,
      skip
    )

    // 转换数据格式，处理 BigInt 类型
    const formattedRecords = records.map(record => serializeBigInt(record))

    // 调试日志：打印fieldMapping数据
    console.log('🔍 [API DEBUG] formId:', formId)
    console.log('🔍 [API DEBUG] fieldMapping类型:', typeof formConfig.fieldMapping)
    console.log('🔍 [API DEBUG] fieldMapping内容:', JSON.stringify(formConfig.fieldMapping, null, 2))

    // 检查每个字段的enabled状态
    if (formConfig.fieldMapping && typeof formConfig.fieldMapping === 'object') {
      Object.entries(formConfig.fieldMapping as Record<string, any>).forEach(([key, config]) => {
        console.log(`🔍 [API DEBUG] 字段 ${key}: enabled=${config.enabled}, name=${config.name}`)
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        records: formattedRecords,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        formConfig: {
          formId: formConfig.formId,
          formName: formConfig.formName,
          fieldMapping: formConfig.fieldMapping,
        },
      },
    })
  } catch (error) {
    console.error('获取表单数据失败:', error)
    return NextResponse.json(
      { success: false, error: '获取表单数据失败' },
      { status: 500 }
    )
  }
}

// PUT /api/data/[formId] - 更新单条数据
export async function PUT(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.formId
    const { searchParams } = new URL(request.url)
    const recordId = searchParams.get('id')

    if (!recordId) {
      return NextResponse.json(
        { success: false, error: '缺少记录ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { updateData } = body

    if (!updateData || Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: '请提供要更新的数据' },
        { status: 400 }
      )
    }

    // 获取表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isDeleted: false },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在或已删除' },
        { status: 404 }
      )
    }

    const tableName = formConfig.tableName || `form_data_${formId}`
    const fieldMapping = formConfig.fieldMapping as Record<string, any>

    // 验证字段是否存在于配置中
    const invalidFields = Object.keys(updateData).filter(
      field => !fieldMapping[field] && !['serial_number'].includes(field)
    )

    if (invalidFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `无效的字段: ${invalidFields.join(', ')}`,
        },
        { status: 400 }
      )
    }

    // 处理数据类型转换，将数组和对象转换为JSON字符串
    const processedUpdateData: any = {}
    Object.entries(updateData).forEach(([key, value]) => {
      if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
        processedUpdateData[key] = JSON.stringify(value)
      } else {
        processedUpdateData[key] = value
      }
    })

    // 构建更新SQL
    const setClause = Object.keys(processedUpdateData)
      .map(key => `\`${key}\` = ?`)
      .join(', ')
    const updateQuery = `
      UPDATE \`${tableName}\` 
      SET ${setClause}, updated_at = NOW() 
      WHERE id = ?
    `

    const queryParams = [...Object.values(processedUpdateData), parseInt(recordId)]

    // 执行更新
    const result = await prisma.$executeRawUnsafe(updateQuery, ...queryParams)

    if (result === 0) {
      return NextResponse.json(
        { success: false, error: '记录不存在或更新失败' },
        { status: 404 }
      )
    }

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id,
        action: 'UPDATE_DATA',
        resource: 'FormData',
        resourceId: recordId,
        details: {
          formId,
          tableName,
          recordId,
          updateData,
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: '数据更新成功',
      data: { recordId, updatedFields: Object.keys(updateData) },
    })
  } catch (error) {
    console.error('更新数据失败:', error)
    return NextResponse.json(
      { success: false, error: '更新数据失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/data/[formId] - 删除单条数据
export async function DELETE(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.formId
    const { searchParams } = new URL(request.url)
    const recordId = searchParams.get('id')

    if (!recordId) {
      return NextResponse.json(
        { success: false, error: '缺少记录ID' },
        { status: 400 }
      )
    }

    // 获取表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isDeleted: false },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在或已删除' },
        { status: 404 }
      )
    }

    const tableName = formConfig.tableName || `form_data_${formId}`

    // 先获取要删除的记录信息（用于日志）
    const getRecordQuery = `SELECT * FROM \`${tableName}\` WHERE id = ?`
    const records = await prisma.$queryRawUnsafe<Array<any>>(
      getRecordQuery,
      parseInt(recordId)
    )

    if (records.length === 0) {
      return NextResponse.json(
        { success: false, error: '记录不存在' },
        { status: 404 }
      )
    }

    // 执行删除
    const deleteQuery = `DELETE FROM \`${tableName}\` WHERE id = ?`
    const result = await prisma.$executeRawUnsafe(
      deleteQuery,
      parseInt(recordId)
    )

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id,
        action: 'DELETE_DATA',
        resource: 'FormData',
        resourceId: recordId,
        details: {
          formId,
          tableName,
          recordId,
          deletedRecord: serializeBigInt(records[0]),
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: '数据删除成功',
      data: { recordId },
    })
  } catch (error) {
    console.error('删除数据失败:', error)
    return NextResponse.json(
      { success: false, error: '删除数据失败' },
      { status: 500 }
    )
  }
}
