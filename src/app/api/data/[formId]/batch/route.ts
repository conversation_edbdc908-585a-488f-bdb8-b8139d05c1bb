import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// 批量删除验证schema
const batchDeleteSchema = z.object({
  ids: z.array(z.string()).min(1, '至少选择一条数据'),
})

// DELETE /api/data/[formId]/batch - 批量删除数据
export async function DELETE(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.formId
    const body = await request.json()

    // 验证请求数据
    const validation = batchDeleteSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const { ids } = validation.data

    // 获取表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isDeleted: false },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在或已删除' },
        { status: 404 }
      )
    }

    const tableName = formConfig.tableName

    // 构建批量删除SQL
    const placeholders = ids.map(() => '?').join(',')
    const deleteQuery = `DELETE FROM \`${tableName}\` WHERE id IN (${placeholders})`

    // 执行删除
    const result = await prisma.$executeRawUnsafe(
      deleteQuery,
      ...ids.map(id => parseInt(id))
    )

    // 检查删除结果
    if (result === 0) {
      return NextResponse.json(
        { success: false, error: '没有找到要删除的记录' },
        { status: 404 }
      )
    }

    // 记录系统日志
    try {
      await prisma.systemLog.create({
        data: {
          userId: session.user.id,
          action: 'BATCH_DELETE_DATA',
          resource: 'FormData',
          resourceId: formId,
          details: {
            formId,
            tableName,
            deletedIds: ids,
            deletedCount: result,
          },
          ipAddress:
            request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
      })
    } catch (logError) {
      console.error('记录系统日志失败:', logError)
      // 日志失败不应该影响删除操作的成功
    }

    return NextResponse.json({
      success: true,
      message: `成功删除 ${result} 条数据`,
      data: {
        deletedCount: result,
        requestedCount: ids.length,
      },
    })
  } catch (error) {
    console.error('批量删除数据失败:', error)
    return NextResponse.json(
      { success: false, error: '批量删除数据失败' },
      { status: 500 }
    )
  }
}

// PUT /api/data/[formId]/batch - 批量更新数据
export async function PUT(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.formId
    const body = await request.json()

    const { ids, updateData } = body

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, error: '请选择要更新的数据' },
        { status: 400 }
      )
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: '请提供要更新的数据' },
        { status: 400 }
      )
    }

    // 获取表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isDeleted: false },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在或已删除' },
        { status: 404 }
      )
    }

    const tableName = formConfig.tableName

    // 构建更新SQL
    const setClause = Object.keys(updateData)
      .map(key => `\`${key}\` = ?`)
      .join(', ')
    const placeholders = ids.map(() => '?').join(',')
    const updateQuery = `
      UPDATE \`${tableName}\` 
      SET ${setClause}, updated_at = NOW() 
      WHERE id IN (${placeholders})
    `

    const queryParams = [
      ...Object.values(updateData),
      ...ids.map((id: string) => parseInt(id)),
    ]

    // 执行更新
    const result = await prisma.$executeRawUnsafe(updateQuery, ...queryParams)

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id,
        action: 'BATCH_UPDATE_DATA',
        resource: 'FormData',
        resourceId: formId,
        details: {
          formId,
          tableName,
          updatedIds: ids,
          updateData,
          updatedCount: result,
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: `成功更新 ${result} 条数据`,
      data: {
        updatedCount: result,
        requestedCount: ids.length,
      },
    })
  } catch (error) {
    console.error('批量更新数据失败:', error)
    return NextResponse.json(
      { success: false, error: '批量更新数据失败' },
      { status: 500 }
    )
  }
}
