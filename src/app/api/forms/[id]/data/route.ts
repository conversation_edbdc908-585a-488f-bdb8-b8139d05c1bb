import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// DELETE /api/forms/[id]/data - 删除表单的所有数据
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.id

    // 检查表单配置是否存在（包括已删除的）
    const existingForm = await prisma.formConfig.findUnique({
      where: { formId },
    })

    if (!existingForm) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    const tableName = existingForm.tableName || `form_data_${formId}`

    // 尝试删除数据，如果表不存在则忽略错误
    let recordCount = 0
    try {
      // 先尝试获取记录数量
      const countQuery = `SELECT COUNT(*) as count FROM \`${tableName}\``
      const countResult = await prisma.$queryRawUnsafe<Array<{ count: number }>>(
        countQuery
      )
      recordCount = countResult[0]?.count || 0

      // 如果有记录，则删除
      if (recordCount > 0) {
        const deleteQuery = `DELETE FROM \`${tableName}\``
        await prisma.$executeRawUnsafe(deleteQuery)
      }
    } catch (error: any) {
      // 如果表不存在（错误代码1146），这是正常情况
      if (error.code === 'P2010' && error.meta?.code === '1146') {
        console.log(`数据表 ${tableName} 不存在，无需删除数据`)
        recordCount = 0
      } else {
        // 其他错误需要抛出
        throw error
      }
    }

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id!,
        action: 'DELETE_FORM_DATA',
        resource: 'FormData',
        resourceId: formId,
        details: {
          formId,
          formName: existingForm.formName,
          tableName,
          deletedRecords: recordCount,
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: `成功删除 ${recordCount} 条数据记录`,
      data: {
        deletedRecords: recordCount,
      },
    })
  } catch (error) {
    console.error('删除表单数据失败:', error)
    return NextResponse.json(
      { success: false, error: '删除表单数据失败' },
      { status: 500 }
    )
  }
}
