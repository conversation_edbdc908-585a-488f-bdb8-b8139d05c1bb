import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { updateTableSchema, FieldMapping } from '@/lib/dynamicTable'
import { invalidateCache } from '@/lib/cache'
import { completeFormCleanup, checkFormDeletionSafety } from '@/lib/formCleanup'

// 更新表单配置验证schema
const updateFormConfigSchema = z.object({
  formName: z
    .string()
    .min(1, '表单名称不能为空')
    .max(200, '表单名称不能超过200个字符')
    .optional(),
  fieldMapping: z
    .record(
      z.object({
        name: z.string(),
        type: z.enum([
          'string',
          'number',
          'boolean',
          'date',
          'array',
          'object',
          'text',
        ]),
        required: z.boolean(),
        enabled: z.boolean().optional(), // 新增：字段是否启用
        description: z.string().optional(),
      })
    )
    .optional(),
  isActive: z.boolean().optional(),
  fieldCount: z.number().optional(),
  // 新增字段支持多种数据格式
  dataFormat: z.enum(['jinshuju_standard', 'jinshuju_automation', 'custom']).optional(),
  dataPathConfig: z.object({
    paths: z.object({
      formId: z.string(),
      formName: z.string(),
      entryData: z.string(),
      serialNumber: z.string().optional(),
      createdAt: z.string().optional(),
      updatedAt: z.string().optional(),
      creatorName: z.string().optional(),
      ipAddress: z.string().optional(),
    }),
    fieldPrefix: z.string().optional(),
    customFieldMapping: z.record(z.string()).optional(),
  }).optional(),
})

// 切换表单状态验证schema
const toggleFormStatusSchema = z.object({
  isActive: z.boolean(),
})

// GET /api/forms/[id] - 获取单个表单配置详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.id

    const formConfig = await prisma.formConfig.findUnique({
      where: { formId },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            doctor_name: true,
          },
        },
      },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: formConfig,
    })
  } catch (error) {
    console.error('获取表单配置详情失败:', error)
    return NextResponse.json(
      { success: false, error: '获取表单配置详情失败' },
      { status: 500 }
    )
  }
}

// PUT /api/forms/[id] - 更新表单配置
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.id
    const body = await request.json()

    // 验证请求数据
    const validation = updateFormConfigSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const updateData = validation.data

    // 检查表单配置是否存在
    const existingForm = await prisma.formConfig.findUnique({
      where: { formId },
    })

    if (!existingForm) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    // 如果更新了字段映射，重新计算启用的字段数量
    if (updateData.fieldMapping) {
      updateData.fieldCount = Object.values(updateData.fieldMapping).filter(
        field => field.enabled === true
      ).length
    }

    // 如果字段映射发生变化，需要更新数据库表结构
    let schemaUpdateWarnings: string[] = []
    if (updateData.fieldMapping) {
      console.log('检测到字段映射更新，开始更新数据库表结构...')

      const oldFieldMapping = existingForm.fieldMapping as any as Record<
        string,
        FieldMapping
      >
      const newFieldMapping = updateData.fieldMapping

      const schemaUpdateResult = await updateTableSchema(
        formId,
        oldFieldMapping || {},
        newFieldMapping
      )

      if (!schemaUpdateResult.success) {
        return NextResponse.json(
          {
            success: false,
            error: `更新数据库表结构失败: ${schemaUpdateResult.error}`,
            details: { schemaError: schemaUpdateResult.error },
          },
          { status: 500 }
        )
      }

      if (schemaUpdateResult.warnings) {
        schemaUpdateWarnings = schemaUpdateResult.warnings
        console.log('数据库表结构更新完成，包含警告信息:', schemaUpdateWarnings)
      } else {
        console.log('数据库表结构更新完成')
      }
    }

    // 更新表单配置
    const updatedForm = await prisma.formConfig.update({
      where: { formId },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id!,
        action: 'UPDATE_FORM_CONFIG',
        resource: 'FormConfig',
        resourceId: updatedForm.id.toString(),
        details: {
          formId,
          updatedFields: Object.keys(updateData),
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    const response: any = {
      success: true,
      message: '表单配置更新成功',
      data: updatedForm,
    }

    // 如果有数据库表结构更新的警告信息，包含在响应中
    if (schemaUpdateWarnings.length > 0) {
      response.warnings = schemaUpdateWarnings
      response.message += '（包含数据库结构更新）'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('更新表单配置失败:', error)
    return NextResponse.json(
      { success: false, error: '更新表单配置失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/forms/[id] - 删除表单配置
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.id
    const { searchParams } = new URL(request.url)
    const fullDelete = searchParams.get('fullDelete') === 'true'
    const force = searchParams.get('force') === 'true'

    // 检查表单配置是否存在
    const existingForm = await prisma.formConfig.findUnique({
      where: { formId },
    })

    if (!existingForm) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    if (fullDelete) {
      // 完全删除：删除所有相关数据
      if (!force) {
        // 检查删除安全性
        const safetyCheck = await checkFormDeletionSafety(formId)
        if (!safetyCheck.safe && safetyCheck.dataCount && safetyCheck.dataCount > 0) {
          return NextResponse.json({
            success: false,
            error: '表单包含数据，需要强制删除',
            requiresForce: true,
            details: {
              dataCount: safetyCheck.dataCount,
              warnings: safetyCheck.warnings,
            },
          }, { status: 409 })
        }
      }

      // 执行完全清理
      const cleanupResult = await completeFormCleanup(
        formId,
        session.user.id!.toString(),
        ipAddress,
        userAgent
      )

      if (!cleanupResult.success) {
        return NextResponse.json({
          success: false,
          error: `完全删除失败: ${cleanupResult.error}`,
          warnings: cleanupResult.warnings,
        }, { status: 500 })
      }

      // 清除相关缓存
      invalidateCache.form()
      invalidateCache.dashboard()

      const response: any = {
        success: true,
        message: '表单和所有相关数据已完全删除',
        details: cleanupResult.details,
      }

      if (cleanupResult.warnings && cleanupResult.warnings.length > 0) {
        response.warnings = cleanupResult.warnings
      }

      return NextResponse.json(response)

    } else {
      // 软删除：仅标记为已删除状态
      const deletedForm = await prisma.formConfig.update({
        where: { formId },
        data: {
          isDeleted: true,
          isActive: false, // 同时禁用表单
          updatedAt: new Date(),
        },
      })

      // 记录系统日志
      await prisma.systemLog.create({
        data: {
          userId: session.user.id!,
          action: 'DELETE_FORM_CONFIG',
          resource: 'FormConfig',
          resourceId: deletedForm.id.toString(),
          details: {
            formId,
            formName: existingForm.formName,
            deleteType: 'soft',
          },
          ipAddress,
          userAgent,
        },
      })

      // 清除相关缓存
      invalidateCache.form()
      invalidateCache.dashboard()

      return NextResponse.json({
        success: true,
        message: '表单配置删除成功（数据已保留）',
      })
    }
  } catch (error) {
    console.error('删除表单配置失败:', error)
    return NextResponse.json(
      { success: false, error: '删除表单配置失败' },
      { status: 500 }
    )
  }
}

// PATCH /api/forms/[id] - 切换表单状态
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.id
    const body = await request.json()

    // 验证请求数据
    const validation = toggleFormStatusSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const { isActive } = validation.data

    // 检查表单是否存在
    const existingForm = await prisma.formConfig.findUnique({
      where: { formId },
    })

    if (!existingForm) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    // 更新表单状态
    const updatedForm = await prisma.formConfig.update({
      where: { formId },
      data: { isActive },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id,
        action: isActive ? 'ENABLE_FORM' : 'DISABLE_FORM',
        resource: 'FormConfig',
        resourceId: updatedForm.id.toString(),
        details: {
          formId,
          formName: updatedForm.formName,
          previousStatus: existingForm.isActive,
          newStatus: isActive,
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    // 清除相关缓存
    invalidateCache.form()
    invalidateCache.dashboard()

    return NextResponse.json({
      success: true,
      message: `表单已${isActive ? '启用' : '禁用'}`,
      data: {
        formId: updatedForm.formId,
        formName: updatedForm.formName,
        isActive: updatedForm.isActive,
      },
    })
  } catch (error) {
    console.error('切换表单状态失败:', error)
    return NextResponse.json(
      { success: false, error: '切换表单状态失败' },
      { status: 500 }
    )
  }
}
