import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createDynamicTable } from '@/lib/dynamicTable'
import { withCache, cacheKeys, invalidateCache } from '@/lib/cache'
import { z } from 'zod'

// 表单配置验证schema
const createFormConfigSchema = z.object({
  formId: z
    .string()
    .min(1, '表单ID不能为空')
    .regex(/^[a-zA-Z0-9_-]+$/, '表单ID只能包含字母、数字、下划线和横线'),
  formName: z
    .string()
    .min(1, '表单名称不能为空')
    .max(200, '表单名称不能超过200个字符'),
  sampleJson: z.object({
    form: z.string(),
    form_name: z.string(),
    entry: z.object({}).passthrough(),
  }),
  fieldMapping: z.record(
    z.object({
      name: z.string(),
      type: z.enum([
        'string',
        'number',
        'boolean',
        'date',
        'array',
        'object',
        'text',
      ]),
      required: z.boolean(),
      enabled: z.boolean().optional(), // 新增：字段是否启用
      description: z.string().optional(),
    })
  ),
})

const updateFormConfigSchema = createFormConfigSchema.partial()

// GET /api/forms - 获取表单配置列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''

    const skip = (page - 1) * limit

    // 构建查询条件
    const where = search
      ? {
          isActive: true, // 只返回活跃的表单配置
          OR: [
            { formId: { contains: search, mode: 'insensitive' as const } },
            { formName: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : { isActive: true } // 只返回活跃的表单配置

    // 使用缓存获取列表数据
    const cacheKey = cacheKeys.formList(page, limit, search)
    const { forms, total } = await withCache(
      cacheKey,
      async () => {
        const [formsData, totalCount] = await Promise.all([
          prisma.formConfig.findMany({
            where,
            skip,
            take: limit,
            orderBy: { createdAt: 'desc' },
            select: {
              id: true,
              formId: true,
              formName: true,
              isActive: true,
              fieldCount: true,
              webhookUrl: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
          prisma.formConfig.count({ where }),
        ])

        // 确保返回的数据格式正确
        const validForms = Array.isArray(formsData) ? formsData : []
        const validTotal = typeof totalCount === 'number' ? totalCount : 0

        return { forms: validForms, total: validTotal }
      },
      60 // 缓存1分钟
    )

    // 额外验证返回的数据
    if (!Array.isArray(forms)) {
      console.error('Forms data is not an array:', forms)
      return NextResponse.json(
        { success: false, error: '数据格式错误' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        forms,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('获取表单配置列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取表单配置列表失败' },
      { status: 500 }
    )
  }
}

// POST /api/forms - 创建表单配置
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()

    // 验证请求数据
    const validation = createFormConfigSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const { formId, formName, sampleJson, fieldMapping } = validation.data

    // 检查表单ID是否已存在
    const existingForm = await prisma.formConfig.findUnique({
      where: { formId },
    })

    if (existingForm) {
      return NextResponse.json(
        {
          success: false,
          error: '表单ID已存在，请使用其他ID',
        },
        { status: 409 }
      )
    }

    // 计算启用的字段数量
    const fieldCount = Object.values(fieldMapping).filter(
      field => field.enabled === true
    ).length

    // 生成webhook URL
    const webhookUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/webhook/${formId}`

    // 生成数据表名
    const tableName = `form_data_${formId}`

    // 创建动态数据表
    const tableResult = await createDynamicTable(formId, fieldMapping)
    if (!tableResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: `创建数据表失败: ${tableResult.error}`,
        },
        { status: 500 }
      )
    }

    // 创建表单配置
    const formConfig = await prisma.formConfig.create({
      data: {
        formId,
        formName,
        sampleJson,
        fieldMapping,
        fieldCount,
        webhookUrl,
        tableName,
        isActive: true,
        createdById: null, // 暂时设为null，避免外键约束问题
      },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id,
        action: 'CREATE_FORM_CONFIG',
        resource: 'FormConfig',
        resourceId: formConfig.id.toString(),
        details: {
          formId,
          formName,
          fieldCount,
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    // 清除相关缓存
    invalidateCache.form()
    invalidateCache.dashboard()

    return NextResponse.json(
      {
        success: true,
        message: '表单配置创建成功',
        data: {
          id: formConfig.id,
          formId: formConfig.formId,
          formName: formConfig.formName,
          webhookUrl: formConfig.webhookUrl,
          tableName: formConfig.tableName,
          fieldCount: formConfig.fieldCount,
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('创建表单配置失败:', error)
    return NextResponse.json(
      { success: false, error: '创建表单配置失败' },
      { status: 500 }
    )
  }
}
