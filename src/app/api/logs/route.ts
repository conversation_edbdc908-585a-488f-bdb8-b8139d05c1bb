import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export const dynamic = 'force-dynamic'

// GET /api/logs - 获取系统日志列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查是否为管理员
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (currentUser?.role !== 'admin') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const action = searchParams.get('action')
    const resource = searchParams.get('resource')
    const userId = searchParams.get('userId')
    const ipAddress = searchParams.get('ipAddress')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}

    if (action) {
      where.action = action
    }

    if (resource) {
      where.resource = resource
    }

    if (userId) {
      where.userId = parseInt(userId)
    }

    if (ipAddress) {
      where.ipAddress = {
        contains: ipAddress,
        mode: 'insensitive'
      }
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate)
      }
      if (endDate) {
        const endDateTime = new Date(endDate)
        endDateTime.setHours(23, 59, 59, 999) // 设置为当天的最后一刻
        where.createdAt.lte = endDateTime
      }
    }

    // 获取总数
    const total = await prisma.systemLog.count({ where })

    // 获取日志列表
    const logs = await prisma.systemLog.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            username: true,
            doctor_name: true,
          }
        }
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        logs: logs.map(log => ({
          ...log,
          id: log.id.toString(),
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('获取系统日志失败:', error)
    return NextResponse.json(
      { success: false, error: '获取系统日志失败' },
      { status: 500 }
    )
  }
}