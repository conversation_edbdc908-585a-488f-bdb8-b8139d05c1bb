import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { NotificationService } from '@/lib/notification-service'
import type { ApiResponse, BatchNotificationRequest } from '@/types'
import { z } from 'zod'

// 批量操作请求体验证
const BatchOperationSchema = z.object({
  action: z.enum(['mark_read', 'delete']),
  ids: z.array(z.string()).optional(), // 如果为空，则操作所有通知
})

/**
 * PUT /api/notifications/batch - 批量操作通知
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        message: '未授权访问',
        code: 'UNAUTHORIZED',
        timestamp: new Date().toISOString()
      }, { status: 401 })
    }

    const body = await request.json()
    const validationResult = BatchOperationSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        message: '请求数据格式错误',
        code: 'INVALID_DATA',
        timestamp: new Date().toISOString()
      }, { status: 400 })
    }

    const { action, ids } = validationResult.data
    let result: number = 0

    switch (action) {
      case 'mark_read':
        if (ids && ids.length > 0) {
          // 批量标记指定通知为已读
          const promises = ids.map(id => 
            NotificationService.markAsRead(id, session.user.id, true)
          )
          const results = await Promise.all(promises)
          result = results.filter(Boolean).length
        } else {
          // 标记所有未读通知为已读
          result = await NotificationService.markAllAsRead(session.user.id)
        }
        break

      case 'delete':
        if (ids && ids.length > 0) {
          // 批量删除指定通知
          result = await NotificationService.deleteMultiple(ids, session.user.id)
        } else {
          return NextResponse.json<ApiResponse<null>>({
            success: false,
            message: '批量删除操作必须指定通知ID',
            code: 'INVALID_PARAMS',
            timestamp: new Date().toISOString()
          }, { status: 400 })
        }
        break

      default:
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          message: '不支持的操作类型',
          code: 'INVALID_PARAMS',
          timestamp: new Date().toISOString()
        }, { status: 400 })
    }

    const actionNames = {
      mark_read: '标记已读',
      delete: '删除'
    }

    return NextResponse.json<ApiResponse<{ count: number }>>({
      success: true,
      message: `批量${actionNames[action]}成功，共处理${result}条通知`,
      data: { count: result },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('批量操作通知失败:', error)
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      message: '批量操作通知失败',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * DELETE /api/notifications/batch - 批量删除通知
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        message: '未授权访问',
        code: 'UNAUTHORIZED',
        timestamp: new Date().toISOString()
      }, { status: 401 })
    }

    const body = await request.json()
    const { ids }: BatchNotificationRequest = body

    if (!ids || ids.length === 0) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        message: '请选择要删除的通知',
        code: 'INVALID_PARAMS',
        timestamp: new Date().toISOString()
      }, { status: 400 })
    }

    const count = await NotificationService.deleteMultiple(ids, session.user.id)

    return NextResponse.json<ApiResponse<{ count: number }>>({
      success: true,
      message: `批量删除成功，共删除${count}条通知`,
      data: { count },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('批量删除通知失败:', error)
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      message: '批量删除通知失败',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}