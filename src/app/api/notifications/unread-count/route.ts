import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { NotificationService } from '@/lib/notification-service'
import type { ApiResponse } from '@/types'

export const dynamic = 'force-dynamic'

/**
 * GET /api/notifications/unread-count - 获取未读通知数量
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        message: '未授权访问',
        code: 'UNAUTHORIZED',
        timestamp: new Date().toISOString()
      }, { status: 401 })
    }

    const count = await NotificationService.getUnreadCount(session.user.id)

    return NextResponse.json<ApiResponse<{ count: number }>>({
      success: true,
      message: '获取未读通知数量成功',
      data: { count },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('获取未读通知数量失败:', error)
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      message: '获取未读通知数量失败',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}