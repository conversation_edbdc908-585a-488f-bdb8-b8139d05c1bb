import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// 查询参数验证schema
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('20'),
  formId: z.string().optional(),
  errorType: z.string().optional(),
  status: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET /api/validation-failures - 获取验证失败记录列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查用户角色
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    })

    if (currentUser?.role !== 'admin') {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const validation = querySchema.safeParse(Object.fromEntries(searchParams))

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '参数验证失败',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const { page, limit, formId, errorType, status, startDate, endDate } =
      validation.data

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const skip = (pageNum - 1) * limitNum

    // 构建查询条件
    const where: any = {}

    if (formId) {
      where.formId = formId
    }

    if (errorType) {
      where.errorType = errorType
    }

    if (status) {
      where.status = status
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate + 'T00:00:00.000Z')
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate + 'T23:59:59.999Z')
      }
    }

    // 获取记录和总数
    const [records, total] = await Promise.all([
      prisma.webhookValidationFailure.findMany({
        where,
        skip,
        take: limitNum,
        orderBy: { createdAt: 'desc' },
        include: {
          resolver: {
            select: {
              id: true,
              username: true,
              doctor_name: true,
            },
          },
        },
      }),
      prisma.webhookValidationFailure.count({ where }),
    ])

    // 获取所有相关的表单配置信息
    const formIds = Array.from(new Set(records.map(record => record.formId)))
    const formConfigs = await prisma.formConfig.findMany({
      where: {
        formId: { in: formIds },
      },
      select: {
        formId: true,
        formName: true,
      },
    })

    // 创建formId到formName的映射
    const formNameMap = new Map(
      formConfigs.map(config => [config.formId, config.formName])
    )

    return NextResponse.json({
      success: true,
      data: {
        records: records.map(record => ({
          ...record,
          id: record.id.toString(),
          formName: formNameMap.get(record.formId) || '未知表单',
        })),
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      },
    })
  } catch (error) {
    console.error('获取验证失败记录失败:', error)
    return NextResponse.json(
      { success: false, error: '获取验证失败记录失败' },
      { status: 500 }
    )
  }
}

// 更新记录状态的body验证schema
const updateStatusSchema = z.object({
  id: z.string(),
  status: z.enum(['pending', 'reviewing', 'resolved', 'ignored']),
  notes: z.string().optional(),
})

// PATCH /api/validation-failures - 更新验证失败记录状态
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查用户角色
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true, id: true },
    })

    if (currentUser?.role !== 'admin') {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 403 })
    }

    const body = await request.json()
    const validation = updateStatusSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '参数验证失败',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const { id, status, notes } = validation.data

    // 更新记录状态
    const updateData: any = {
      status,
      updatedAt: new Date(),
    }

    if (notes) {
      updateData.notes = notes
    }

    if (status === 'resolved') {
      updateData.resolvedBy = currentUser.id
      updateData.resolvedAt = new Date()
    }

    const updatedRecord = await prisma.webhookValidationFailure.update({
      where: { id: BigInt(id) },
      data: updateData,
      include: {
        resolver: {
          select: {
            id: true,
            username: true,
            doctor_name: true,
          },
        },
      },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: currentUser.id,
        action: 'UPDATE_VALIDATION_FAILURE',
        resource: 'WebhookValidationFailure',
        resourceId: id,
        details: {
          old_status: 'unknown', // 可以改进记录旧状态
          new_status: status,
          notes,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        ...updatedRecord,
        id: updatedRecord.id.toString(),
      },
    })
  } catch (error) {
    console.error('更新验证失败记录状态失败:', error)
    return NextResponse.json(
      { success: false, error: '更新验证失败记录状态失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/validation-failures - 批量删除验证失败记录
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查用户角色
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true, id: true },
    })

    if (currentUser?.role !== 'admin') {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 403 })
    }

    const body = await request.json()
    const { ids, clearAll, clearResolved } = body

    let deleteResult
    let actionType = 'BATCH_DELETE_VALIDATION_FAILURES'
    let details: any = {}

    if (clearAll) {
      // 清除所有记录
      deleteResult = await prisma.webhookValidationFailure.deleteMany({})
      actionType = 'CLEAR_ALL_VALIDATION_FAILURES'
      details = { cleared_all: true }
    } else if (clearResolved) {
      // 清除已解决的记录
      deleteResult = await prisma.webhookValidationFailure.deleteMany({
        where: {
          status: 'resolved',
        },
      })
      actionType = 'CLEAR_RESOLVED_VALIDATION_FAILURES'
      details = { cleared_resolved: true }
    } else if (Array.isArray(ids) && ids.length > 0) {
      // 批量删除指定记录
      deleteResult = await prisma.webhookValidationFailure.deleteMany({
        where: {
          id: {
            in: ids.map((id: string) => BigInt(id)),
          },
        },
      })
      details = {
        deleted_count: deleteResult.count,
        deleted_ids: ids,
      }
    } else {
      return NextResponse.json(
        {
          success: false,
          error: '请选择要删除的记录或指定清除操作',
        },
        { status: 400 }
      )
    }

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: currentUser.id,
        action: actionType,
        resource: 'WebhookValidationFailure',
        resourceId: null,
        details: {
          deleted_count: deleteResult.count,
          ...details,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        deletedCount: deleteResult.count,
      },
    })
  } catch (error) {
    console.error('批量删除验证失败记录失败:', error)
    return NextResponse.json(
      { success: false, error: '批量删除验证失败记录失败' },
      { status: 500 }
    )
  }
}
