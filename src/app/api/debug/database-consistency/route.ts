import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { checkTableExists, createDynamicTable } from '@/lib/dynamicTable'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    console.log('开始检查数据库一致性...')

    // 获取所有表单配置
    const formConfigs = await prisma.formConfig.findMany({
      where: {
        isDeleted: false
      },
      select: {
        id: true,
        formId: true,
        formName: true,
        tableName: true,
        fieldMapping: true,
        isActive: true
      }
    })

    const results = []
    let inconsistentCount = 0

    for (const config of formConfigs) {
      const expectedTableName = config.tableName || `form_data_${config.formId}`
      const tableExists = await checkTableExists(expectedTableName)

      const result: {
        formId: string
        formName: string
        expectedTableName: string
        tableExists: boolean
        isConsistent: boolean
        issue?: string
      } = {
        formId: config.formId,
        formName: config.formName,
        expectedTableName,
        tableExists,
        isConsistent: tableExists
      }

      if (!tableExists) {
        inconsistentCount++
        result.issue = '数据表不存在'
      }

      results.push(result)
    }

    return NextResponse.json({
      success: true,
      summary: {
        totalForms: formConfigs.length,
        inconsistentCount,
        consistentCount: formConfigs.length - inconsistentCount
      },
      details: results
    })

  } catch (error) {
    console.error('检查数据库一致性时出错:', error)
    return NextResponse.json({
      success: false,
      error: '检查数据库一致性失败',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const { formId, action } = body

    if (action === 'fix' && formId) {
      // 修复单个表单的数据表
      const formConfig = await prisma.formConfig.findUnique({
        where: { formId }
      })

      if (!formConfig) {
        return NextResponse.json({
          success: false,
          error: '表单配置不存在'
        }, { status: 404 })
      }

      const tableName = formConfig.tableName || `form_data_${formId}`
      const tableExists = await checkTableExists(tableName)

      if (tableExists) {
        return NextResponse.json({
          success: true,
          message: '数据表已存在，无需修复'
        })
      }

      // 创建数据表
      const fieldMapping = formConfig.fieldMapping ? 
        JSON.parse(formConfig.fieldMapping as string) : {}

      const createResult = await createDynamicTable(formId, fieldMapping)

      if (createResult.success) {
        // 更新表单配置中的表名
        if (formConfig.tableName !== tableName) {
          await prisma.formConfig.update({
            where: { id: formConfig.id },
            data: { tableName }
          })
        }

        return NextResponse.json({
          success: true,
          message: `成功创建数据表 ${tableName}`
        })
      } else {
        return NextResponse.json({
          success: false,
          error: `创建数据表失败: ${createResult.error}`
        }, { status: 500 })
      }
    }

    if (action === 'fix-all') {
      // 修复所有不一致的表
      const formConfigs = await prisma.formConfig.findMany({
        where: { isDeleted: false }
      })

      let fixedCount = 0
      let failedCount = 0
      const results = []

      for (const config of formConfigs) {
        const tableName = config.tableName || `form_data_${config.formId}`
        const tableExists = await checkTableExists(tableName)

        if (!tableExists) {
          const fieldMapping = config.fieldMapping ? 
            JSON.parse(config.fieldMapping as string) : {}

          const createResult = await createDynamicTable(config.formId, fieldMapping)

          if (createResult.success) {
            fixedCount++
            
            // 更新表名
            if (config.tableName !== tableName) {
              await prisma.formConfig.update({
                where: { id: config.id },
                data: { tableName }
              })
            }

            results.push({
              formId: config.formId,
              status: 'fixed',
              message: `成功创建表 ${tableName}`
            })
          } else {
            failedCount++
            results.push({
              formId: config.formId,
              status: 'failed',
              error: createResult.error
            })
          }
        }
      }

      return NextResponse.json({
        success: true,
        summary: {
          fixedCount,
          failedCount
        },
        details: results
      })
    }

    return NextResponse.json({
      success: false,
      error: '无效的操作'
    }, { status: 400 })

  } catch (error) {
    console.error('修复数据库一致性时出错:', error)
    return NextResponse.json({
      success: false,
      error: '修复数据库一致性失败',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}