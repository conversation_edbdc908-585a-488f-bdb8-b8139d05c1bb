import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export const dynamic = 'force-dynamic'

// 定义字段信息接口
interface FieldInfo {
  key: string
  name: any
  enabled: any
}

// GET /api/debug/form-config - 调试表单配置
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const formId = searchParams.get('formId')

    if (!formId) {
      return NextResponse.json({ error: 'formId参数缺失' }, { status: 400 })
    }

    // 获取表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId },
    })

    if (!formConfig) {
      return NextResponse.json({ error: '表单配置不存在' }, { status: 404 })
    }

    // 详细分析fieldMapping
    const fieldMapping = formConfig.fieldMapping as Record<string, any>
    const analysis = {
      formId: formConfig.formId,
      formName: formConfig.formName,
      fieldMappingType: typeof fieldMapping,
      fieldMappingKeys: Object.keys(fieldMapping),
      totalFields: Object.keys(fieldMapping).length,
      enabledFields: [] as FieldInfo[],
      disabledFields: [] as FieldInfo[],
      undefinedEnabledFields: [] as FieldInfo[],
    }

    // 分析每个字段的enabled状态
    Object.entries(fieldMapping).forEach(([key, config]) => {
      if (config.enabled === true) {
        analysis.enabledFields.push({ key, name: config.name, enabled: config.enabled })
      } else if (config.enabled === false) {
        analysis.disabledFields.push({ key, name: config.name, enabled: config.enabled })
      } else {
        analysis.undefinedEnabledFields.push({ key, name: config.name, enabled: config.enabled })
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        rawFieldMapping: fieldMapping,
        analysis,
      },
    })
  } catch (error) {
    console.error('调试表单配置失败:', error)
    return NextResponse.json(
      { success: false, error: '调试表单配置失败' },
      { status: 500 }
    )
  }
}
