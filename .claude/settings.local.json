{"permissions": {"allow": ["<PERSON><PERSON>(true)", "WebFetch(domain:github.com)", "Bash(node:*)", "Bash(npm:*)", "Bash(sudo chown:*)", "Bash(yarn add:*)", "Bash(npx:*)", "Bash(./node_modules/.bin/playwright-mcp:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(ls:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_click", "mcp__playwright__browser_type", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_close", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(pkill:*)", "mcp__playwright__browser_install", "Bash(git merge:*)", "Bash(scripts/deploy-traditional.sh:*)", "Bash(grep:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(gh pr view:*)", "Bash(echo $SHELL)", "<PERSON><PERSON>(env)", "Bash(sudo rm:*)", "Bash(NODE_OPTIONS=\"--max-old-space-size=4096\" npm run build --no-audit)", "<PERSON><PERSON>(mkdir:*)", "Bash(rg:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["playwright"]}