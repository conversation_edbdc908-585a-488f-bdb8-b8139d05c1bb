# 通知功能详细实施计划

## 项目概述
完善肺功能数据管理平台的通知功能，将右上角铃铛图标从静态展示升级为功能完整的通知系统。

## 现状分析
- ✅ 已有右上角铃铛图标（仅视觉效果，硬编码显示3个通知）
- ✅ 已有完整的TypeScript类型定义 (`src/types/index.ts:333-361`)
- ✅ 已有Ant Design主题配置 (`src/lib/theme.ts:159-169`)
- ✅ 已有SystemLog系统用于审计日志
- ❌ 缺少通知数据库模型
- ❌ 缺少通知API接口
- ❌ 缺少通知状态管理
- ❌ 缺少通知UI组件

## 第一阶段：数据库设计 (估时: 30分钟)

### 1.1 Notification数据库模型设计
**文件**: `prisma/schema.prisma`
```prisma
model Notification {
  id          String   @id @default(cuid())
  userId      String
  title       String   @db.VarChar(200)
  message     String   @db.Text
  type        NotificationType @default(INFO)
  read        Boolean  @default(false)
  actionUrl   String?  @db.VarChar(500)
  sourceType  String?  @db.VarChar(50)  // 'webhook_failure', 'form_submission', 'system'
  sourceId    String?  @db.VarChar(100) // 关联的源记录ID
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId, read])
  @@index([createdAt])
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
}
```

### 1.2 用户模型更新
在User模型中添加关联关系：
```prisma
model User {
  // ... 现有字段
  notifications Notification[]
}
```

### 1.3 数据库迁移
- 运行 `npm run db:generate` 生成Prisma客户端
- 运行 `npm run db:push` 推送schema变更

## 第二阶段：API接口开发 (估时: 90分钟)

### 2.1 通知CRUD API
**文件**: `src/app/api/notifications/route.ts`
```typescript
// GET /api/notifications - 获取用户通知列表
// 支持分页、筛选(read/unread)、类型过滤
// 返回格式：{ notifications: Notification[], total: number, unreadCount: number }

// POST /api/notifications - 创建新通知(系统内部使用)
```

**文件**: `src/app/api/notifications/[id]/route.ts`
```typescript
// PUT /api/notifications/[id] - 标记通知为已读/未读
// DELETE /api/notifications/[id] - 删除单个通知
```

**文件**: `src/app/api/notifications/batch/route.ts`
```typescript
// PUT /api/notifications/batch - 批量标记已读
// DELETE /api/notifications/batch - 批量删除
```

### 2.2 通知服务层
**文件**: `src/lib/notification-service.ts`
```typescript
// createNotification() - 创建通知的核心方法
// getNotifications() - 获取通知列表
// markAsRead() - 标记已读
// deleteNotification() - 删除通知
// getUnreadCount() - 获取未读数量
```

## 第三阶段：前端状态管理 (估时: 45分钟)

### 3.1 通知Hooks
**文件**: `src/hooks/useNotifications.ts`
```typescript
// 自定义Hook处理通知状态管理
// useNotifications() - 获取通知列表和未读数量
// useMarkAsRead() - 标记已读操作
// useDeleteNotification() - 删除通知操作
// 使用SWR或React Query模式进行数据管理
```

## 第四阶段：UI组件开发 (估时: 120分钟)

### 4.1 通知下拉组件
**文件**: `src/components/notifications/NotificationDropdown.tsx`
```typescript
// 主通知下拉容器
// 集成Ant Design Dropdown + Popover
// 包含：头部统计、通知列表、底部操作按钮
// 支持无限滚动或分页加载
```

### 4.2 通知项组件
**文件**: `src/components/notifications/NotificationItem.tsx`
```typescript
// 单个通知项展示
// 包含：图标、标题、消息、时间、已读状态
// 支持点击跳转、标记已读、删除操作
// 根据type显示不同颜色和图标
```

### 4.3 空状态组件
**文件**: `src/components/notifications/NotificationEmpty.tsx`
```typescript
// 无通知时的空状态展示
// 使用Ant Design Empty组件
```

### 4.4 更新Header组件
**文件**: `src/components/layout/Header.tsx` (第171-178行)
```typescript
// 替换现有硬编码的铃铛图标
// 集成真实通知数量显示
// 添加NotificationDropdown组件
// 保持现有样式和响应式设计
```

## 第五阶段：系统集成 (估时: 75分钟)

### 5.1 Webhook失败通知
**文件**: `src/app/api/webhook/[formId]/route.ts`
```typescript
// 在webhook验证失败时创建通知
// 集成到现有的WebhookValidationFailure逻辑中
// 通知管理员有新的数据验证失败
```

### 5.2 系统事件通知
**文件**: 各个关键操作点
```typescript
// 表单配置变更通知
// 用户登录异常通知  
// 系统错误通知
// 数据导出完成通知
```

### 5.3 通知类型定义更新
**文件**: `src/types/index.ts` (更新现有类型)
```typescript
// 更新现有Notification接口以匹配数据库模型
// 添加API响应类型定义
// 添加通知设置相关类型
```

## 第六阶段：优化和完善 (估时: 60分钟)

### 6.1 性能优化
- 实现通知数据缓存策略
- 添加防抖处理避免频繁API调用
- 优化长列表渲染性能

### 6.2 用户体验增强
- 添加通知声音提示(可选)
- 实现通知的自动过期清理
- 添加通知偏好设置页面

### 6.3 错误处理和边界情况
- API错误处理和重试机制
- 网络异常时的降级体验
- 权限控制和安全验证

## 技术规范和约定

### 编码规范
- 遵循项目现有的TypeScript strict模式
- 使用Ant Design组件和主题系统
- 保持与现有代码风格一致的命名约定
- 响应式设计支持移动端

### 数据处理
- 所有API使用现有的ApiResponse<T>格式
- 错误处理遵循项目统一标准
- 日志记录集成到SystemLog系统

### 安全考虑
- 通知数据按用户隔离
- API路由添加身份验证中间件
- 防止XSS攻击的输入验证

## 验收标准

1. ✅ 铃铛图标显示真实未读通知数量
2. ✅ 点击铃铛展开通知下拉面板
3. ✅ 通知列表支持标记已读/删除操作
4. ✅ 系统关键事件自动生成通知
5. ✅ 移动端响应式体验良好
6. ✅ 通知API性能稳定，响应时间<200ms
7. ✅ 通过所有现有测试，无TypeScript错误

## 实施进度追踪

- [x] 第一阶段：数据库设计 (30分钟) ✅ 已完成
- [x] 第二阶段：API接口开发 (90分钟) ✅ 已完成
- [x] 第三阶段：前端状态管理 (45分钟) ✅ 已完成
- [x] 第四阶段：UI组件开发 (120分钟) ✅ 已完成
- [x] 第五阶段：系统集成 (75分钟) ✅ 已完成
- [x] 第六阶段：优化完善 (60分钟) ✅ 已完成

**总估时**: 约6.5小时，分6个阶段逐步实施

## 🎉 实施完成总结

### 已完成功能
1. **数据库模型** - Notification表和相关关联关系
2. **完整API系统** - CRUD操作、批量操作、未读数量等
3. **前端Hooks** - useNotifications、useUnreadCount等状态管理
4. **UI组件** - NotificationDropdown、NotificationItem、NotificationEmpty等
5. **系统集成** - Webhook失败通知、表单配置变更通知等
6. **性能优化** - 缓存机制、防抖处理等
7. **辅助工具** - 种子数据、性能测试、设置组件等

### 可用命令
```bash
# 生成Prisma客户端和推送schema
npm run db:generate && npm run db:push

# 创建示例通知数据
npm run notifications:seed

# 运行性能测试
npm run notifications:test

# 启动开发服务器测试功能
npm run dev
```

### 验收完成状态
- ✅ 铃铛图标显示真实未读通知数量
- ✅ 点击铃铛展开通知下拉面板
- ✅ 通知列表支持标记已读/删除操作
- ✅ 系统关键事件自动生成通知
- ✅ 移动端响应式体验良好
- ✅ 通知API性能稳定，集成缓存机制
- ✅ 通过TypeScript类型检查，无编译错误

**🚀 通知功能已完全实现并可投入使用！**

---

*创建时间: 2025-07-13*  
*项目: 肺功能数据管理平台*  
*功能: 通知系统完善*