# Playwright MCP 使用指南

## 概述

Playwright MCP（Model Context Protocol）是一个强大的浏览器自动化工具，它允许 AI 模型通过结构化的接口与网页进行交互。

## 快速开始

### 1. 启动 MCP 服务器

```bash
# 无头模式启动（适合自动化任务）
npm run mcp:start

# 有界面模式启动（适合调试和开发）
npm run mcp:dev

# 使用自定义配置启动
npm run mcp:test
```

### 2. 基本配置

项目包含两个主要配置文件：

- **mcp-config.json**: MCP 服务器基本配置
- **playwright-mcp.config.js**: Playwright 详细配置

### 3. 支持的浏览器

- **Chromium** (默认)
- **Firefox**
- **WebKit** (Safari)
- **Microsoft Edge**

## 主要功能

### 浏览器自动化
- 页面导航和交互
- 表单填写和提交
- 元素点击和文本输入
- 页面截图和PDF生成

### 数据提取
- 网页内容抓取
- 结构化数据提取
- 表格数据导出
- API 接口测试

### 测试和验证
- UI 自动化测试
- 可访问性检查
- 性能监控
- 视觉回归测试

## 使用示例

### 基本页面操作
```javascript
// 这些操作可以通过 MCP 接口执行
// 1. 导航到页面
// 2. 等待页面加载
// 3. 查找元素
// 4. 执行操作（点击、输入等）
// 5. 获取页面内容或截图
```

### 测试自动化
- 可以集成到项目的测试流程中
- 支持持续集成/持续部署 (CI/CD)
- 生成测试报告和截图

## 配置选项

### 浏览器设置
- `browser.type`: 浏览器类型 (chromium/firefox/webkit)
- `browser.headless`: 是否无头模式
- `browser.viewport`: 视口大小设置

### 安全设置
- `security.sandbox`: 沙盒模式
- `security.allowedDomains`: 允许访问的域名
- `security.disableWebSecurity`: Web 安全设置

### 网络设置
- `network.ignoreHTTPSErrors`: 忽略 HTTPS 错误
- `network.userAgent`: 用户代理字符串

## 故障排除

### 常见问题

1. **浏览器未安装**
   ```bash
   npx playwright install
   ```

2. **权限问题**
   - 检查文件夹权限
   - 确保 Node.js 版本 >= 18

3. **网络连接问题**
   - 检查代理设置
   - 验证防火墙配置

### 调试技巧

1. **启用详细日志**
   ```bash
   DEBUG=pw:* npm run mcp:dev
   ```

2. **使用有界面模式**
   ```bash
   npm run mcp:dev
   ```

3. **检查配置文件**
   - 验证 `playwright-mcp.config.js` 设置
   - 确认端口没有冲突

## 最佳实践

1. **开发环境**: 使用有界面模式便于调试
2. **生产环境**: 使用无头模式提高性能
3. **安全性**: 限制允许访问的域名
4. **性能**: 合理设置超时时间
5. **维护**: 定期更新浏览器版本

## 相关资源

- [Playwright 官方文档](https://playwright.dev/)
- [MCP 协议规范](https://modelcontextprotocol.io/)
- [项目 README](./README.md)
- [开发文档](./docs/DEVELOPMENT.md)