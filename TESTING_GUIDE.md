# 表单配置JSON格式测试指南

## 问题描述
用户反馈在"新建表单配置"页面的JSON输入步骤中，使用金数据自动化格式的JSON数据时仍然出现错误："缺少必要字段: entry.serial_number (序列号)"

## 测试页面
为了更好地测试和诊断问题，我创建了以下测试资源：

### 1. 独立测试页面
访问：`http://localhost:3011/test-form-config`
- 这是一个专门的测试页面，可以独立测试JsonInputStep组件
- 可以选择不同的数据格式并立即看到效果

### 2. 静态HTML测试页面
打开：`test-frontend-flow.html`
- 纯静态页面，包含完整的验证逻辑
- 可以离线测试验证功能

### 3. 命令行验证工具
运行：`node debug-validation.js`
- 测试验证逻辑的正确性
- 验证不同格式的JSON数据

## 测试步骤

### 步骤1：验证逻辑测试
```bash
cd /Users/<USER>/PythonProjects/free_lung_function_project_admin
node debug-validation.js
```
预期结果：所有验证测试应该通过

### 步骤2：独立组件测试
1. 访问 `http://localhost:3011/test-form-config`
2. 选择"金数据自动化格式"
3. 点击"使用示例数据"按钮
4. 观察验证结果

### 步骤3：完整流程测试
1. 访问 `http://localhost:3011/forms/config`
2. 填写基本信息：
   - 表单ID: `test_automation`
   - 表单名称: `测试自动化格式`
   - **数据格式: 选择"金数据自动化格式"**
3. 点击"下一步"
4. 在JSON输入框中粘贴以下测试数据：

```json
{
  "form": "usk6CY",
  "form_name": "120餐券领取",
  "entry": {
    "token": "TzbIm8FV",
    "field_3": "",
    "field_4": "2025-07-10",
    "field_5": "高安市人民医院",
    "field_6": "李卫平",
    "field_7": "13707003778",
    "field_8": "邹春香",
    "field_11": "爱国路院区食堂（住院部二部后面）：徐中 18827949388",
    "field_12": "120司机",
    "gen_code": "031576671523",
    "referral_link": "http://jinshuju.net/f/usk6CY?referred_from=TzbIm8FV",
    "x_field_weixin_openid": "orNMswF4m_KgjYAyPLr4E5CbsW2Q",
    "x_field_weixin_unionid": "oRZSYuIDHs9G1s6kG5ca_TUErN8k",
    "x_field_weixin_nickname": "李卫平"
  }
}
```

5. 观察验证结果

## 调试信息
在开发模式下，页面会显示调试信息框，包含：
- 当前选择的数据格式
- 组件接收到的props
- 验证状态

## 常见问题排查

### 问题1：仍然提示缺少serial_number
**可能原因：**
- 用户在第一步没有选择正确的数据格式
- 数据格式没有正确传递给JsonInputStep组件

**排查方法：**
1. 检查调试信息中的`dataFormat`值
2. 确认第一步选择了"金数据自动化格式"
3. 查看浏览器控制台的日志输出

### 问题2：示例数据不匹配
**解决方案：**
- 每种格式都有对应的示例数据
- 使用"使用示例数据"按钮自动填充正确格式

### 问题3：验证逻辑不生效
**排查方法：**
1. 检查控制台是否有JavaScript错误
2. 确认`dataFormat`参数正确传递
3. 查看验证函数的日志输出

## 代码修复记录

### 1. 添加useEffect监听dataFormat变化
确保当用户更改数据格式时，已输入的JSON会重新验证

### 2. 增强验证逻辑
- 标准格式：要求`entry.serial_number`
- 自动化格式：要求`entry.token`
- 自定义格式：宽松验证

### 3. 添加调试组件
在开发模式下显示组件状态，便于问题诊断

## 预期结果
使用正确的数据格式配置后：
- 金数据自动化格式的JSON应该通过验证
- 不再出现"缺少serial_number"错误
- 可以正常进入字段映射步骤

## 联系支持
如果问题仍然存在，请提供：
1. 具体的操作步骤
2. 浏览器控制台的错误信息
3. 调试信息框中显示的数据
4. 使用的JSON数据内容