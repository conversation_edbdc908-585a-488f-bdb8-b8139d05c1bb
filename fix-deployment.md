# 修复部署样式问题的解决方案

## 问题分析
错误 `Refused to apply style from 'http://free.e3e4.club:3011/_next/static/css/ba23526582c1573d.css' because its MIME type ('text/html') is not a supported stylesheet MIME type` 表明：

1. CSS文件请求返回了HTML内容（404页面）而不是CSS
2. 静态文件没有正确构建或部署

## 立即解决方案

### 1. 在服务器上重新构建项目
```bash
# 登录服务器
ssh <EMAIL>

# 进入项目目录
cd ~/www/wwwroot/free_lung_function_project

# 清理缓存
rm -rf .next

# 设置 NODE_ENV
export NODE_ENV=production

# 重新构建
npm run build

# 检查构建结果
ls -la .next/static/css/
```

### 2. 检查Nginx配置
确保Nginx正确处理静态文件：

```nginx
# 在 /etc/nginx/sites-available/lung-function-admin 中添加或确认：
location /_next/static/ {
    alias /home/<USER>/www/wwwroot/free_lung_function_project/.next/static/;
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Content-Type text/css;  # 强制CSS MIME类型
}

# 或者使用proxy方式（推荐）：
location /_next/ {
    proxy_pass http://127.0.0.1:3011;
    proxy_set_header Host $host;
}
```

### 3. Next.js配置优化
在 `next.config.js` 中确保：

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 添加这些配置
  assetPrefix: '',
  basePath: '',
  trailingSlash: false,
  
  // 静态资源优化
  async headers() {
    return [
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
          {
            key: 'Content-Type',
            value: 'text/css; charset=utf-8',
          },
        ],
      },
    ]
  },
}
```

### 4. 重启服务
```bash
# 重启应用
pm2 restart lung-function-admin

# 重启Nginx
sudo systemctl reload nginx

# 验证
curl -I http://free.e3e4.club:3011/_next/static/css/[actual-css-filename].css
```

### 5. 临时解决方案（如果上述方法不行）
在项目中添加自定义document：

```javascript
// pages/_document.js (如果使用pages router) 或 app/layout.tsx
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <head>
        {/* 手动添加CSS链接以确保正确的MIME类型 */}
        <link 
          rel="stylesheet" 
          href="/_next/static/css/app/layout.css" 
          type="text/css"
        />
      </head>
      <body>{children}</body>
    </html>
  )
}
```

## 验证步骤
1. 检查 `.next/static/css/` 目录是否存在且包含CSS文件
2. 测试静态文件访问：`curl -I http://free.e3e4.club:3011/_next/static/css/[filename].css`
3. 确认响应头包含 `Content-Type: text/css`
4. 浏览器开发者工具网络标签检查CSS文件加载状态

## 预防措施
1. 确保构建过程完整执行
2. 定期检查磁盘空间（构建需要足够空间）
3. 监控PM2进程状态
4. 设置健康检查端点