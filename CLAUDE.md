# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start development server on port 3011
- `npm run build` - Build production application
- `npm run start` - Start production server
- `npm run lint` - Run ESLint checks
- `npm run lint:fix` - Fix ESLint issues automatically
- `npm run type-check` - Run TypeScript type checking
- `npm run format` - Format code with Prettier
- `npm test` - Run Jest tests
- `npm run test:watch` - Run tests in watch mode

### Database Operations
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio GUI
- `npm run db:seed` - Seed database with initial data

### Playwright MCP Commands
- `npm run mcp:start` - Start MCP server in headless mode
- `npm run mcp:dev` - Start MCP server with browser UI
- `npm run mcp:test` - Start MCP server with custom config

### Environment Setup
1. Copy `.env.example` to `.env.local`
2. Configure database URL and other required environment variables
3. Run `npm run db:push` to initialize database schema
4. Default admin credentials: `admin` / `admin123`

## Architecture Overview

### Core Technology Stack
- **Framework**: Next.js 14 with App Router
- **UI**: Ant Design 5.x + Tailwind CSS
- **Database**: MySQL 8.0 with Prisma ORM
- **Authentication**: NextAuth.js with JWT
- **State Management**: Zustand
- **Validation**: Zod
- **Testing**: Jest with React Testing Library

### Key Directory Structure
```
src/
├── app/                    # Next.js App Router pages
│   ├── (auth)/            # Authentication pages
│   ├── (dashboard)/       # Protected dashboard pages
│   └── api/               # API routes
├── components/            # React components
├── lib/                   # Utility libraries
├── types/                 # TypeScript type definitions
└── store/                 # Zustand store definitions
```

### Database Schema
The application uses Prisma with MySQL and includes these key models:
- `user` - User authentication and profiles
- `FormConfig` - Dynamic form configurations with field mappings
- `SystemLog` - Audit logs for user actions
- `WebhookValidationFailure` - Webhook error tracking
- `form_data_*` - Dynamically created tables for form submissions

### Authentication Flow
- Uses NextAuth.js with credentials provider
- Password hashing with bcryptjs
- JWT tokens with 30-day expiration
- Role-based access control
- Login actions logged to SystemLog

### Dynamic Form System
The application's core feature is handling webhook data from external forms:
1. **FormConfig**: Stores form metadata and field mappings
2. **Dynamic Tables**: Creates MySQL tables based on form structure
3. **Webhook Processing**: Validates and stores incoming form data
4. **Validation Failures**: Tracks and manages data validation errors

### Key Libraries and Utilities
- `src/lib/prisma.ts` - Database client configuration
- `src/lib/auth.ts` - NextAuth configuration
- `src/lib/utils.ts` - Common utility functions
- `src/types/index.ts` - Comprehensive type definitions

### API Structure
- RESTful API routes in `src/app/api/`
- Consistent response format with `ApiResponse<T>` type
- Error handling with proper HTTP status codes
- Authentication middleware for protected routes

### Data Flow
1. External forms send webhook data to `/api/webhook/[formId]`
2. Data validated against FormConfig field mappings
3. Valid data stored in dynamic `form_data_*` tables
4. Invalid data logged to `WebhookValidationFailure` table
5. Users can view, export, and manage data through dashboard

### Component Architecture
- Ant Design components with custom styling
- Responsive design with Tailwind CSS
- Reusable UI components in `src/components/ui/`
- Form components with react-hook-form integration
- Table components with pagination and filtering

### Playwright MCP Integration
The project includes Playwright MCP (Model Context Protocol) server for browser automation:

#### Installation and Setup
- **Version**: @playwright/mcp@0.0.29
- **Configuration**: `mcp-config.json` and `playwright-mcp.config.js`
- **Browsers**: Chromium, Firefox, WebKit (auto-installed)

#### Key Features
- Browser automation capabilities for testing
- Accessibility snapshots for AI interaction
- Web scraping and data extraction
- Visual testing and screenshot capture
- Multiple browser support with device emulation

#### Usage
```bash
# Start MCP server (headless mode)
npm run mcp:start

# Start MCP server with browser UI
npm run mcp:dev

# Start with custom configuration
npm run mcp:test
```

#### Configuration Options
- **Browser Types**: chromium, firefox, webkit, msedge
- **Modes**: headless (default) or headed mode
- **Capabilities**: tabs, pdf, history, wait, files, install
- **Network**: proxy support, HTTPS error handling
- **Security**: sandbox mode, allowed/blocked origins

### Development Notes
- Uses TypeScript with strict mode enabled
- Comprehensive type definitions in `src/types/index.ts`
- Husky pre-commit hooks for linting and formatting
- PM2 for production process management
- Supports both development and production deployments
- Playwright MCP enables AI-driven browser automation and testing