#!/usr/bin/env node

// 直接测试验证逻辑
function validateJsonStructure(json, dataFormat) {
  console.log(`验证JSON，格式: ${dataFormat}`)
  console.log('JSON数据:', JSON.stringify(json, null, 2))
  
  if (!json || typeof json !== 'object') {
    return 'JSON必须是一个对象'
  }

  if (!json.form || typeof json.form !== 'string') {
    return '缺少必要字段: form (表单ID)'
  }

  if (!json.entry || typeof json.entry !== 'object') {
    return '缺少必要字段: entry (表单数据)'
  }

  // 根据数据格式进行不同的验证
  switch (dataFormat) {
    case 'jinshuju_standard':
      if (!json.form_name || typeof json.form_name !== 'string') {
        return '缺少必要字段: form_name (表单名称)'
      }
      if (
        !json.entry.serial_number ||
        typeof json.entry.serial_number !== 'number'
      ) {
        return '缺少必要字段: entry.serial_number (序列号)'
      }
      break
    
    case 'jinshuju_automation':
      if (!json.form_name || typeof json.form_name !== 'string') {
        return '缺少必要字段: form_name (表单名称)'
      }
      if (!json.entry.token || typeof json.entry.token !== 'string') {
        return '缺少必要字段: entry.token (令牌标识)'
      }
      break
    
    case 'custom':
      // 自定义格式的验证比较宽松，只要有基本结构即可
      if (Object.keys(json.entry).length === 0) {
        return 'entry对象不能为空'
      }
      break
    
    default:
      return '不支持的数据格式'
  }

  return null
}

// 测试数据
const testData = {
  standard: {
    form: 'ZFs2eo',
    form_name: '预约免费肺功能检查',
    entry: {
      serial_number: 123,
      field_1: '张三',
      field_2: '选项1',
    },
  },
  automation: {
    form: 'usk6CY',
    form_name: '120餐券领取',
    entry: {
      token: 'TzbIm8FV',
      field_3: '',
      field_4: '2025-07-10',
      field_5: '高安市人民医院',
      field_6: '李卫平',
      field_7: '13707003778',
      x_field_weixin_openid: 'orNMswF4m_KgjYAyPLr4E5CbsW2Q',
      x_field_weixin_nickname: '李卫平',
    },
  },
}

console.log('=== 验证测试开始 ===\n')

// 测试1: 标准格式数据用标准格式验证
console.log('测试1: 标准格式数据 + 标准格式验证')
let result = validateJsonStructure(testData.standard, 'jinshuju_standard')
console.log('结果:', result || '✅ 验证通过')
console.log()

// 测试2: 自动化格式数据用标准格式验证（应该失败）
console.log('测试2: 自动化格式数据 + 标准格式验证（应该失败）')
result = validateJsonStructure(testData.automation, 'jinshuju_standard')
console.log('结果:', result || '✅ 验证通过')
console.log()

// 测试3: 自动化格式数据用自动化格式验证
console.log('测试3: 自动化格式数据 + 自动化格式验证')
result = validateJsonStructure(testData.automation, 'jinshuju_automation')
console.log('结果:', result || '✅ 验证通过')
console.log()

// 测试4: 标准格式数据用自动化格式验证（应该失败）
console.log('测试4: 标准格式数据 + 自动化格式验证（应该失败）')
result = validateJsonStructure(testData.standard, 'jinshuju_automation')
console.log('结果:', result || '✅ 验证通过')
console.log()

console.log('=== 验证测试结束 ===')

// 模拟前端组件的行为
console.log('\n=== 模拟前端组件行为 ===')

function simulateComponentBehavior() {
  // 模拟用户选择了自动化格式
  let dataFormat = 'jinshuju_automation'
  console.log(`用户选择格式: ${dataFormat}`)
  
  // 模拟用户输入了自动化格式的JSON
  let inputJson = testData.automation
  console.log('用户输入JSON:', JSON.stringify(inputJson, null, 2))
  
  // 模拟验证过程
  let error = validateJsonStructure(inputJson, dataFormat)
  console.log('验证结果:', error || '✅ 验证通过')
  
  if (!error) {
    console.log('✅ 组件应该允许进入下一步')
  } else {
    console.log('❌ 组件会显示错误:', error)
  }
}

simulateComponentBehavior()