# Product Overview

## 肺功能数据管理平台 (Lung Function Data Management Platform)

A modern full-stack data management platform designed for processing form submissions from JinShuju (金数据) and other webhook sources, with comprehensive user management and data analytics capabilities.

### Core Purpose
- **Data Collection**: Automatically receive and process webhook data from external form platforms
- **Data Management**: Provide intuitive interfaces for viewing, searching, filtering, and exporting data
- **User Administration**: Complete user management system with role-based access control
- **Analytics**: Real-time dashboards and reporting capabilities

### Key Features
- **Dynamic Form Configuration**: Auto-generate database schemas from JSON samples
- **Multi-format Support**: Handle JinShuju standard, automation, and custom data formats
- **Batch Operations**: Import/export data in Excel/CSV formats
- **Real-time Notifications**: System alerts and status updates
- **Audit Logging**: Complete operation tracking and security monitoring
- **Mobile Responsive**: Optimized for mobile, tablet, and desktop usage

### Target Users
- Healthcare administrators managing lung function test data
- Data analysts requiring structured data access
- System administrators managing user permissions and system health

### Business Value
- Automates manual data entry processes
- Provides secure, centralized data storage
- Enables data-driven decision making through analytics
- Reduces operational overhead through intelligent validation and error handling