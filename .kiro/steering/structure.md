# Project Structure & Organization

## Root Directory Structure

```
├── src/                    # Source code
├── prisma/                 # Database schema and migrations
├── public/                 # Static assets
├── docs/                   # Documentation
├── scripts/                # Utility scripts
├── tests/                  # Test files
├── .kiro/                  # Kiro configuration and specs
└── logs/                   # Application logs
```

## Source Code Organization (`src/`)

### App Router Structure (`src/app/`)
```
src/app/
├── (auth)/                 # Authentication pages group
│   └── login/             # Login page
├── (dashboard)/           # Dashboard pages group
│   ├── dashboard/         # Main dashboard
│   ├── forms/            # Form management
│   ├── data/             # Data management
│   ├── settings/         # User settings
│   └── help/             # Help pages
├── api/                   # API routes
│   ├── auth/             # Authentication endpoints
│   ├── forms/            # Form CRUD operations
│   ├── data/             # Data management endpoints
│   ├── users/            # User management
│   ├── webhook/          # Webhook receivers
│   └── notifications/    # Notification system
├── demo/                  # Demo/test pages
├── globals.css           # Global styles
├── layout.tsx            # Root layout
└── page.tsx              # Home page (redirects to dashboard)
```

### Components (`src/components/`)
```
src/components/
├── forms/                 # Form-related components
├── layout/               # Layout components (headers, sidebars)
├── notifications/        # Notification system components
├── providers/            # React context providers
├── ui/                   # Reusable UI components
│   ├── Button.tsx
│   ├── Input.tsx
│   ├── Modal.tsx
│   ├── ResponsiveContainer.tsx
│   └── MobileTable.tsx
└── user/                 # User-related components
```

### Library Code (`src/lib/`)
```
src/lib/
├── auth.ts               # NextAuth configuration
├── prisma.ts             # Prisma client setup
├── db.ts                 # Database utilities
├── utils.ts              # General utilities
├── constants.ts          # Application constants
├── responsive.ts         # Responsive design utilities
├── cache.ts              # Caching utilities
├── theme.ts              # Theme configuration
└── notification-*.ts    # Notification system modules
```

### Hooks (`src/hooks/`)
```
src/hooks/
├── useResponsive.ts      # Responsive design hooks
└── useNotifications.ts   # Notification system hooks
```

### Types (`src/types/`)
```
src/types/
├── index.ts              # Main type definitions
└── next-auth.d.ts        # NextAuth type extensions
```

### Styles (`src/styles/`)
```
src/styles/
└── mobile.css            # Mobile-specific CSS utilities
```

## Database Structure (`prisma/`)

### Key Models
- **FormConfig**: Dynamic form configurations and field mappings
- **User**: User accounts with role-based permissions
- **SystemLog**: Audit logging for all operations
- **WebhookValidationFailure**: Failed webhook processing records
- **Notification**: Real-time notification system
- **Dynamic Tables**: Auto-generated tables based on form configurations (e.g., `form_data_*`)

## Configuration Files

### Environment Configuration
- `.env.example` - Template for environment variables
- `.env.local` - Local development environment
- `.env.production` - Production environment settings

### Build & Development
- `package.json` - Dependencies and scripts
- `next.config.js` - Next.js configuration
- `tailwind.config.js` - Tailwind CSS configuration
- `tsconfig.json` - TypeScript configuration
- `jest.config.js` - Jest testing configuration
- `playwright.config.ts` - Playwright E2E testing

### Code Quality
- `.eslintrc.json` - ESLint rules
- `.prettierrc` - Prettier formatting rules
- `.gitignore` - Git ignore patterns

## Naming Conventions

### Files & Directories
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Pages**: lowercase with hyphens (e.g., `user-settings/`)
- **Utilities**: camelCase (e.g., `dataUtils.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS`)

### Database
- **Tables**: snake_case (e.g., `form_configs`, `system_logs`)
- **Columns**: snake_case (e.g., `created_at`, `user_id`)
- **Indexes**: descriptive names (e.g., `idx_created_at`, `idx_serial_number`)

### API Routes
- **RESTful**: `/api/resource/[id]` pattern
- **Webhooks**: `/api/webhook/[formId]`
- **Nested Resources**: `/api/forms/[id]/data`

## Import Path Aliases

```typescript
// Use these aliases for cleaner imports
import { Component } from '@/components/ui/Component'
import { utility } from '@/lib/utils'
import { UserType } from '@/types'
```

## Responsive Design Architecture

### Breakpoint System
- **xs**: 0px+ (mobile portrait)
- **sm**: 576px+ (mobile landscape)
- **md**: 768px+ (tablet portrait)
- **lg**: 992px+ (tablet landscape)
- **xl**: 1200px+ (desktop)
- **xxl**: 1600px+ (large desktop)

### Mobile-First Approach
- Base styles target mobile devices
- Progressive enhancement for larger screens
- Touch-friendly interactions and sizing
- Responsive utilities in `src/styles/mobile.css`

## Security Patterns

### Authentication Flow
1. Login via NextAuth.js credentials provider
2. JWT tokens with 30-day expiration
3. Session-based route protection
4. Role-based access control (RBAC)

### Data Validation
- Zod schemas for runtime validation
- Prisma type safety at database level
- Input sanitization for webhook data
- CSRF protection via NextAuth.js

## Error Handling Patterns

### API Routes
- Consistent error response format
- Proper HTTP status codes
- Detailed logging via Winston
- Graceful degradation for client errors

### Client-Side
- Error boundaries for React components
- Toast notifications for user feedback
- Retry mechanisms for failed requests
- Offline state handling