# Technology Stack & Development Guide

## Core Technologies

### Frontend Stack
- **Next.js 14** with App Router - React framework with server-side rendering
- **React 18** - UI library with modern hooks and concurrent features
- **TypeScript** - Type-safe JavaScript development
- **Ant Design 5.x** - Primary UI component library
- **Tailwind CSS 3.x** - Utility-first CSS framework for custom styling
- **Zustand 4.x** - Lightweight state management
- **React Hook Form** - Form handling and validation

### Backend & Database
- **Next.js API Routes** - Server-side API endpoints
- **Prisma ORM 5.x** - Database ORM with type safety
- **MySQL 8.0** - Primary database
- **NextAuth.js 4.x** - Authentication and session management
- **bcryptjs** - Password hashing
- **Winston** - Logging framework

### Development & Testing
- **Jest** - Unit testing framework
- **Playwright** - End-to-end testing
- **Testing Library** - React component testing
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Husky** - Git hooks for pre-commit checks

## Common Commands

### Development
```bash
npm run dev              # Start development server (port 3011)
npm run build           # Build for production
npm run start           # Start production server
npm run type-check      # TypeScript type checking
npm run lint            # Run ESLint
npm run lint:fix        # Auto-fix linting issues
npm run format          # Format code with Prettier
```

### Database Operations
```bash
npm run db:generate     # Generate Prisma client
npm run db:push         # Push schema changes to database
npm run db:migrate      # Run database migrations
npm run db:studio       # Open Prisma Studio GUI
npm run db:seed         # Seed database with initial data
```

### Testing
```bash
npm test                # Run Jest unit tests
npm run test:watch      # Run tests in watch mode
```

### Notifications & MCP
```bash
npm run notifications:seed  # Generate test notification data
npm run notifications:test  # Test notification system performance
npm run mcp:start          # Start MCP service
npm run mcp:dev            # Development mode MCP service
```

## Build System

- **Package Manager**: Yarn 1.22.0 (specified in packageManager field)
- **Node.js**: Requires 18.0+ 
- **Build Tool**: Next.js built-in webpack configuration
- **Output**: Standalone build for Docker deployment
- **Optimization**: SWC minification enabled for faster builds

## Key Configuration Files

- `next.config.js` - Next.js configuration with security headers and optimizations
- `tailwind.config.js` - Tailwind CSS configuration with custom theme
- `tsconfig.json` - TypeScript configuration with path aliases
- `prisma/schema.prisma` - Database schema definition
- `.eslintrc.json` - ESLint rules and configuration
- `.prettierrc` - Code formatting rules

## Development Standards

- **TypeScript**: Strict mode enabled, all new code must be typed
- **Code Style**: Prettier formatting with ESLint rules
- **Git Hooks**: Pre-commit hooks run linting and formatting
- **Path Aliases**: Use `@/` for src directory imports
- **Component Structure**: Functional components with hooks
- **API Routes**: RESTful design with proper error handling
- **Database**: Prisma models with proper indexing and relationships