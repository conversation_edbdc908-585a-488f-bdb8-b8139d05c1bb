#!/bin/bash

# 肺功能数据管理平台 - 传统部署脚本
# 适用于腾讯云CVM CentOS 7+/Ubuntu 20.04+

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 网络重试函数
retry_command() {
    local max_attempts=3
    local delay=5
    local command="$1"
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log_info "执行命令 (尝试 $attempt/$max_attempts): $command"
        if eval "$command"; then
            return 0
        else
            if [ $attempt -lt $max_attempts ]; then
                log_warning "命令失败，${delay}秒后重试..."
                sleep $delay
            else
                log_error "命令执行失败，已达到最大重试次数"
                return 1
            fi
        fi
        attempt=$((attempt + 1))
    done
}

# 检查是否为root用户
check_user() {
    if [ "$EUID" -eq 0 ]; then
        log_error "请不要使用root用户运行此脚本"
        log_info "建议创建普通用户: sudo useradd -m -s /bin/bash lungapp"
        exit 1
    fi
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."

    # 检查操作系统
    if grep -q "Ubuntu" /etc/os-release; then
        OS_TYPE="ubuntu"
        log_info "检测到Ubuntu系统"
    elif grep -q "CentOS" /etc/os-release || grep -q "Red Hat" /etc/os-release; then
        OS_TYPE="centos"
        log_info "检测到CentOS/RHEL系统"

        # 检查CentOS版本
        if grep -q "CentOS Linux 7" /etc/os-release || grep -q "VERSION_ID=\"7\"" /etc/os-release; then
            OS_VERSION="7"
            log_warning "检测到CentOS 7，将安装Node.js 16.x以确保兼容性"
        else
            OS_VERSION="8+"
        fi
    else
        log_warning "未识别的操作系统，脚本可能需要调整"
        OS_TYPE="unknown"
    fi
    
    # 检查内存
    total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [ "$total_mem" -lt 3800 ]; then
        log_warning "系统内存少于4GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    available_space=$(df / | awk 'NR==2{print $4}')
    if [ "$available_space" -lt 20971520 ]; then  # 20GB in KB
        log_warning "可用磁盘空间少于20GB，可能不足"
    fi
    
    log_success "系统检查完成"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    if [ "$OS_TYPE" = "ubuntu" ]; then
        # Ubuntu系统
        sudo apt update
        sudo apt install -y curl wget git build-essential
        
        # 安装Node.js 18
        if ! command -v node &> /dev/null || [ "$(node -v | cut -d'v' -f2 | cut -d'.' -f1)" -lt 16 ]; then
            log_info "安装Node.js 18..."
            curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
            sudo apt-get install -y nodejs
        fi
    elif [ "$OS_TYPE" = "centos" ]; then
        # CentOS系统
        sudo yum update -y
        sudo yum groupinstall -y "Development Tools"
        sudo yum install -y curl wget git gcc gcc-c++ make

        # 检查并安装EPEL仓库（避免重复安装）
        if ! rpm -qa | grep -q epel-release; then
            log_info "安装EPEL仓库..."
            sudo yum install -y epel-release
        fi

        # 根据CentOS版本安装合适的Node.js版本
        if ! command -v node &> /dev/null || [ "$(node -v | cut -d'v' -f2 | cut -d'.' -f1)" -lt 16 ]; then
            if [ "$OS_VERSION" = "7" ]; then
                log_info "安装Node.js 16.x (兼容CentOS 7)..."
                curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
            else
                log_info "安装Node.js 18..."
                curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
            fi
            sudo yum install -y nodejs
        fi
    else
        log_error "不支持的操作系统"
        exit 1
    fi
    
    # 安装Yarn
    if ! command -v yarn &> /dev/null; then
        log_info "安装Yarn..."
        sudo npm install -g yarn
    fi
    
    # 安装PM2
    if ! command -v pm2 &> /dev/null; then
        log_info "安装PM2..."
        sudo npm install -g pm2
    fi
    
    # 安装Nginx
    if ! command -v nginx &> /dev/null; then
        log_info "安装Nginx..."
        if [ "$OS_TYPE" = "ubuntu" ]; then
            sudo apt install -y nginx
        elif [ "$OS_TYPE" = "centos" ]; then
            # EPEL已在上面安装，直接安装nginx
            sudo yum install -y nginx
        fi
        sudo systemctl enable nginx
        sudo systemctl start nginx
    fi
    
    log_success "系统依赖安装完成"
}

# 部署应用
deploy_application() {
    log_info "开始部署应用..."

    # 设置项目目录
    PROJECT_DIR="$HOME/www/wwwroot/free_lung_function_project"
    CURRENT_DIR=$(pwd)

    # 创建应用目录
    mkdir -p "$HOME/www/wwwroot"

    # 检查当前是否在项目目录中
    if [ -f "package.json" ] && [ -f "prisma/schema.prisma" ] && [ -d ".git" ]; then
        log_info "检测到当前目录是项目目录，复制到部署位置..."

        if [ ! -d "$PROJECT_DIR" ]; then
            # 如果目标目录不存在，复制整个项目
            cp -r "$CURRENT_DIR" "$PROJECT_DIR"
            log_success "项目已复制到 $PROJECT_DIR"
        else
            # 如果目标目录存在，更新项目文件
            log_info "更新项目文件到部署目录..."
            rsync -av --exclude='.git' --exclude='node_modules' --exclude='.next' "$CURRENT_DIR/" "$PROJECT_DIR/"

            # 更新Git仓库
            cd "$PROJECT_DIR"
            if [ -d ".git" ]; then
                # 重置本地更改以避免合并冲突
                log_info "重置本地更改..."
                git reset --hard HEAD
                git clean -fd
                retry_command "git pull origin main"
            else
                log_info "初始化Git仓库..."
                git init
                git remote add origin https://github.com/peckbyte/free_lung_function_project_admin.git
                retry_command "git pull origin main"
            fi
        fi
    else
        # 当前不在项目目录中，按原逻辑处理
        if [ ! -d "$PROJECT_DIR" ]; then
            log_info "克隆项目代码..."
            cd "$HOME/www/wwwroot"
            retry_command "git clone https://github.com/peckbyte/free_lung_function_project_admin.git free_lung_function_project"
        else
            log_info "更新项目代码..."
            cd "$PROJECT_DIR"
            retry_command "git pull origin main"
        fi
    fi
    
    cd "$PROJECT_DIR"
    
    # 安装依赖
    log_info "安装项目依赖..."
    retry_command "npm install"
    
    # 配置环境变量
    if [ ! -f ".env.production" ]; then
        log_info "创建生产环境配置文件..."
        cp .env.example .env.production
    else
        log_info "检查现有的生产环境配置..."
    fi

    # 检查并修复端口配置
    if grep -q "^PORT=" .env.production; then
        # 如果端口不是3011，则更新
        if ! grep -q "^PORT=3011" .env.production; then
            log_info "更新端口配置为3011..."
            sed -i 's/^PORT=.*/PORT=3011/' .env.production
        fi
    else
        # 如果没有端口配置，则添加
        echo "PORT=3011" >> .env.production
    fi

    # 检查数据库配置是否为示例配置
    if grep -q "your-db-host\|secure_password\|production_db" .env.production; then
        log_warning "检测到示例数据库配置，需要手动配置真实数据库连接！"
        log_warning "请编辑 .env.production 文件中的 DATABASE_URL"
        log_info "配置文件位置: $PROJECT_DIR/.env.production"
        log_info "数据库配置格式: mysql://用户名:密码@主机:端口/数据库名"
    fi
    
    # 清理Docker相关文件（用户偏好不使用Docker）
    log_info "清理Docker相关文件..."
    rm -f Dockerfile docker-compose.yml .dockerignore 2>/dev/null || true

    # 创建必要目录
    mkdir -p logs public/uploads
    chmod 755 logs public/uploads
    
    # 生成Prisma客户端
    log_info "生成Prisma客户端..."
    npx prisma generate

    # 验证数据库连接（可选，如果配置了真实数据库）
    if ! grep -q "your-db-host\|secure_password\|production_db" .env.production; then
        log_info "验证数据库连接..."
        
        # 首先尝试简单的连接测试
        if npx prisma db execute --stdin <<< "SELECT 1;" 2>/dev/null; then
            log_success "数据库连接成功"
            
            # 检查是否需要数据库迁移
            log_warning "⚠️  数据库结构同步提醒："
            log_warning "   为了保护您的数据，脚本不会自动同步数据库结构"
            log_info "   如需同步数据库结构，请在部署完成后手动执行："
            log_info "   方式1 (推荐): npx prisma migrate deploy  # 使用迁移文件"
            log_info "   方式2 (仅开发): npx prisma db push     # 直接推送结构变更"
            log_warning "   ⚠️  注意：方式2可能导致数据丢失，请先备份数据库！"
        else
            log_warning "数据库连接失败，请检查 DATABASE_URL 配置"
            log_info "连接测试失败，可能的原因："
            log_info "1. 数据库服务器未启动"
            log_info "2. 网络连接问题"
            log_info "3. 用户名或密码错误"
            log_info "4. 数据库不存在"
        fi
    else
        log_warning "跳过数据库连接验证（使用示例配置）"
        log_info "配置真实数据库后的操作步骤："
        log_info "1. 编辑 .env.production 文件中的 DATABASE_URL"
        log_info "2. 运行: npx prisma migrate deploy (推荐)"
        log_info "3. 或运行: npx prisma db push (可能有数据风险)"
    fi

    # 构建应用
    log_info "构建生产版本..."
    npm run build
    
    log_success "应用部署完成"
}

# 配置PM2
configure_pm2() {
    log_info "配置PM2进程管理..."

    cd "$HOME/www/wwwroot/free_lung_function_project"

    # 检查是否已有配置文件
    if [ ! -f "ecosystem.config.js" ]; then
        log_info "创建PM2配置文件..."
        # 使用项目中的配置文件模板
        if [ -f "ecosystem.config.js.template" ]; then
            cp ecosystem.config.js.template ecosystem.config.js
        else
            # 创建基础配置文件
            cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'lung-function-admin',
    script: 'npm',
    args: 'start',
    cwd: process.cwd(),

    // 使用 node_args 加载环境文件
    node_args: '--env-file=.env.production',

    instances: 1,
    exec_mode: 'fork',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    min_uptime: '10s',
    max_restarts: 10,

    env_production: {
      NODE_ENV: 'production',
      PORT: 3011
    },

    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    log_type: 'json',

    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 10000,

    health_check_url: 'http://localhost:3011/api/health',
    health_check_grace_period: 3000,

    ignore_watch: [
      'node_modules',
      'logs',
      '.next',
      '.git',
      'public/uploads'
    ]
  }]
}
EOF
        fi
    else
        log_info "使用现有的PM2配置文件"
    fi

    # 停止现有进程
    pm2 delete lung-function-admin 2>/dev/null || true

    # 启动应用
    pm2 start ecosystem.config.js --env production

    # 保存PM2配置
    pm2 save

    # 设置开机自启
    pm2 startup | grep "sudo" | bash || log_warning "请手动执行PM2 startup命令"

    # 等待应用启动并进行健康检查
    log_info "等待应用启动..."
    sleep 15

    # 健康检查
    if curl -f -s http://localhost:3011/api/health >/dev/null 2>&1; then
        log_success "应用健康检查通过"
    else
        log_warning "应用健康检查失败，请检查日志: pm2 logs lung-function-admin"
    fi

    log_success "PM2配置完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx反向代理..."
    
    # 获取当前用户
    current_user=$(whoami)
    project_path="$HOME/www/wwwroot/free_lung_function_project"
    
    # 创建Nginx配置
    sudo tee /etc/nginx/sites-available/lung-function-admin > /dev/null << EOF
server {
    listen 80;
    server_name _;
    
    # 安全配置
    server_tokens off;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态文件
    location /_next/static/ {
        alias $project_path/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads/ {
        alias $project_path/public/uploads/;
        expires 30d;
        add_header Cache-Control "public";
    }
    
    # 主应用代理
    location / {
        proxy_pass http://127.0.0.1:3011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 文件上传大小限制
    client_max_body_size 10M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/lung-function-admin /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    sudo nginx -t
    
    # 重启Nginx
    sudo systemctl reload nginx
    
    log_success "Nginx配置完成"
}

# 验证部署配置
verify_deployment() {
    log_info "验证部署配置..."

    local project_dir="$HOME/www/wwwroot/free_lung_function_project"

    # 检查项目目录
    if [ ! -d "$project_dir" ]; then
        log_error "项目目录不存在: $project_dir"
        return 1
    fi

    # 检查关键文件
    local required_files=("package.json" ".env.production" "ecosystem.config.js")
    for file in "${required_files[@]}"; do
        if [ ! -f "$project_dir/$file" ]; then
            log_error "缺少必需文件: $file"
            return 1
        fi
    done

    # 检查PM2进程
    if ! pm2 list | grep -q "lung-function-admin"; then
        log_error "PM2进程未找到"
        return 1
    fi

    # 检查Nginx配置
    if ! sudo nginx -t >/dev/null 2>&1; then
        log_error "Nginx配置测试失败"
        return 1
    fi

    # 检查端口监听
    if ! netstat -tlnp 2>/dev/null | grep -q ":3011"; then
        log_warning "端口3011未监听，应用可能未正常启动"
    fi

    # 检查数据库配置
    if [ -f "$project_dir/.env.production" ]; then
        if grep -q "your-db-host\|secure_password\|production_db" "$project_dir/.env.production"; then
            log_warning "检测到示例数据库配置，请配置真实数据库连接"
        else
            log_info "数据库配置已更新"
        fi
    fi

    log_success "部署配置验证完成"
}

# 主函数
main() {
    log_info "开始肺功能数据管理平台传统部署..."
    
    check_user
    check_system
    install_dependencies
    deploy_application
    configure_pm2
    configure_nginx
    verify_deployment

    log_success "部署完成！"
    echo
    log_info "访问信息:"
    echo "  - 应用地址: http://$(curl -s ifconfig.me 2>/dev/null || echo 'YOUR_SERVER_IP'):3011"
    echo "  - 本地地址: http://localhost:3011"
    echo
    log_info "管理命令:"
    echo "  - 查看状态: pm2 status"
    echo "  - 查看日志: pm2 logs lung-function-admin"
    echo "  - 重启应用: pm2 restart lung-function-admin"
    echo
    log_warning "重要提醒:"
    echo "  1. 检查并配置数据库连接:"
    echo "     编辑文件: $HOME/www/wwwroot/free_lung_function_project/.env.production"
    echo "     配置格式: DATABASE_URL=\"mysql://用户名:密码@主机:端口/数据库名\""
    echo "  2. 运行数据库迁移 (⚠️ 请先备份数据库):"
    echo "     cd $HOME/www/wwwroot/free_lung_function_project"
    echo "     npx prisma migrate deploy  # 推荐：使用迁移文件"
    echo "     # 或者: npx prisma db push  # 警告：可能导致数据丢失！"
    echo "  3. 创建管理员用户:"
    echo "     cd $HOME/www/wwwroot/free_lung_function_project && node reset-password.js"

    # 根据操作系统提供相应的防火墙配置命令
    if [ "$OS_TYPE" = "centos" ]; then
        echo "  4. 配置防火墙 (CentOS/RHEL):"
        echo "     sudo firewall-cmd --permanent --add-port=80/tcp"
        echo "     sudo firewall-cmd --permanent --add-port=443/tcp"
        echo "     sudo firewall-cmd --permanent --add-port=3011/tcp"
        echo "     sudo firewall-cmd --reload"
    else
        echo "  4. 配置防火墙 (Ubuntu): sudo ufw allow 80 && sudo ufw allow 443 && sudo ufw allow 3011"
    fi
    echo
    log_info "如果部署出现问题，可以使用以下命令进行故障排除:"
    echo "  - 查看PM2状态: pm2 status"
    echo "  - 查看应用日志: pm2 logs lung-function-admin"
    echo "  - 重启应用: pm2 restart lung-function-admin"
    echo "  - 停止应用: pm2 stop lung-function-admin"
    echo "  - 查看Nginx状态: sudo systemctl status nginx"
    echo "  - 测试Nginx配置: sudo nginx -t"
}

# 执行主函数
main "$@"
