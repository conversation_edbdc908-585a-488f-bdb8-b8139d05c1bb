#!/usr/bin/env node

/**
 * 数据库一致性检查和修复工具
 * 用于修复表单配置存在但动态数据表不存在的问题
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkTableExists(tableName) {
  try {
    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM information_schema.tables 
       WHERE table_schema = DATABASE() AND table_name = ?`,
      tableName
    )
    return result[0].count > 0
  } catch (error) {
    console.error(`检查表 ${tableName} 是否存在时出错:`, error)
    return false
  }
}

async function createMissingTable(formConfig) {
  const tableName = `form_data_${formConfig.formId}`
  
  try {
    // 基础表结构
    const createSQL = `
      CREATE TABLE \`${tableName}\` (
        \`id\` bigint NOT NULL AUTO_INCREMENT,
        \`serial_number\` varchar(100) DEFAULT NULL COMMENT '流水号',
        \`source_ip\` varchar(45) DEFAULT NULL COMMENT '来源IP',
        \`creator_name\` varchar(100) DEFAULT NULL COMMENT '创建者姓名',
        \`raw_data\` json DEFAULT NULL COMMENT '原始数据',
        \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (\`id\`),
        KEY \`idx_serial_number\` (\`serial_number\`),
        KEY \`idx_created_at\` (\`created_at\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='${formConfig.formName || formConfig.formId}动态数据表'
    `
    
    await prisma.$executeRawUnsafe(createSQL)
    console.log(`✅ 已创建表: ${tableName}`)
    
    // 如果有字段映射，添加字段
    if (formConfig.fieldMapping) {
      const fieldMapping = JSON.parse(formConfig.fieldMapping)
      
      for (const [fieldName, config] of Object.entries(fieldMapping)) {
        if (config.enabled === false) continue
        
        let sqlType = 'varchar(255)'
        switch (config.type) {
          case 'number':
            sqlType = 'decimal(10,2)'
            break
          case 'integer':
            sqlType = 'int'
            break
          case 'boolean':
            sqlType = 'tinyint(1)'
            break
          case 'date':
            sqlType = 'date'
            break
          case 'datetime':
            sqlType = 'datetime'
            break
          case 'text':
            sqlType = 'text'
            break
        }
        
        try {
          await prisma.$executeRawUnsafe(
            `ALTER TABLE \`${tableName}\` ADD COLUMN \`${fieldName}\` ${sqlType} DEFAULT NULL COMMENT '${config.label || fieldName}'`
          )
          console.log(`  📝 添加字段: ${fieldName} (${sqlType})`)
        } catch (error) {
          console.warn(`  ⚠️  添加字段 ${fieldName} 失败:`, error.message)
        }
      }
    }
    
    return true
  } catch (error) {
    console.error(`❌ 创建表 ${tableName} 失败:`, error)
    return false
  }
}

async function checkAndFixConsistency() {
  try {
    console.log('🔍 开始检查数据库一致性...\n')
    
    // 获取所有表单配置
    const formConfigs = await prisma.formConfig.findMany({
      where: {
        isDeleted: false // 只检查未删除的表单
      },
      select: {
        id: true,
        formId: true,
        formName: true,
        tableName: true,
        fieldMapping: true,
        isActive: true
      }
    })
    
    console.log(`📊 找到 ${formConfigs.length} 个活跃的表单配置`)
    
    let inconsistentCount = 0
    let fixedCount = 0
    
    for (const config of formConfigs) {
      const expectedTableName = config.tableName || `form_data_${config.formId}`
      const tableExists = await checkTableExists(expectedTableName)
      
      if (!tableExists) {
        console.log(`\n❌ 发现不一致: 表单 ${config.formId} (${config.formName}) 的数据表不存在`)
        console.log(`   预期表名: ${expectedTableName}`)
        
        inconsistentCount++
        
        // 尝试修复
        console.log(`🔧 尝试修复...`)
        const fixed = await createMissingTable(config)
        
        if (fixed) {
          fixedCount++
          
          // 更新表单配置中的表名（如果不匹配）
          if (config.tableName !== expectedTableName) {
            await prisma.formConfig.update({
              where: { id: config.id },
              data: { tableName: expectedTableName }
            })
            console.log(`📝 已更新表单配置中的表名`)
          }
        }
      } else {
        console.log(`✅ 表单 ${config.formId}: 数据表 ${expectedTableName} 存在`)
      }
    }
    
    console.log(`\n📋 检查完成:`)
    console.log(`   - 总表单数: ${formConfigs.length}`)
    console.log(`   - 发现不一致: ${inconsistentCount}`)
    console.log(`   - 成功修复: ${fixedCount}`)
    
    if (inconsistentCount === 0) {
      console.log(`🎉 数据库一致性良好！`)
    } else if (fixedCount === inconsistentCount) {
      console.log(`🎉 所有不一致问题已修复！`)
    } else {
      console.log(`⚠️  仍有 ${inconsistentCount - fixedCount} 个问题未修复，需要手动处理`)
    }
    
  } catch (error) {
    console.error('❌ 检查过程中出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkAndFixConsistency()
    .then(() => {
      console.log('\n✨ 任务完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 执行失败:', error)
      process.exit(1)
    })
}

module.exports = {
  checkAndFixConsistency,
  checkTableExists,
  createMissingTable
}