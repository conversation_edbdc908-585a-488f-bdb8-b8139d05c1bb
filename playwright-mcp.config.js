// Playwright MCP 配置文件
module.exports = {
  // 浏览器配置
  browser: {
    // 支持的浏览器类型: 'chromium', 'firefox', 'webkit'
    type: 'chromium',
    // 是否以无头模式运行
    headless: false,
    // 浏览器超时时间（毫秒）
    timeout: 30000,
    // 视口配置
    viewport: {
      width: 1280,
      height: 720
    },
    // 是否启用开发者工具
    devtools: false
  },
  
  // 网络配置
  network: {
    // 是否忽略 HTTPS 错误
    ignoreHTTPSErrors: true,
    // 用户代理字符串
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  },
  
  // 服务器配置
  server: {
    // MCP 服务器端口
    port: 3001,
    // 允许的源站（CORS）
    allowedOrigins: ['http://localhost:3000', 'http://localhost:3011'],
    // 是否启用详细日志
    verbose: true
  },
  
  // 安全配置
  security: {
    // 是否启用沙盒模式
    sandbox: true,
    // 允许的域名列表
    allowedDomains: ['localhost', '127.0.0.1', '*.example.com'],
    // 是否禁用 Web 安全
    disableWebSecurity: false
  },
  
  // 截图和录制配置
  capture: {
    // 是否启用截图
    screenshots: true,
    // 截图质量 (0-100)
    screenshotQuality: 90,
    // 是否启用视频录制
    video: false,
    // 视频目录
    videoDir: './test-results/videos'
  },
  
  // 等待配置
  waitFor: {
    // 默认等待超时（毫秒）
    timeout: 10000,
    // 网络空闲超时（毫秒）
    networkIdleTimeout: 500
  }
};